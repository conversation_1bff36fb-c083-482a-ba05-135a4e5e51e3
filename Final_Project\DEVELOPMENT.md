# ECPI Development Guide

This guide provides detailed information for developers working on the ECPI platform.

## 🏗️ Architecture Overview

### Django Apps Structure

1. **core** - Static pages, site settings, notifications
2. **accounts** - User management, authentication, profiles
3. **discussions** - Community forums and discussions
4. **consultation** - Psychologist booking and sessions
5. **chat** - Real-time messaging system
6. **meetings** - Events and seminars management
7. **resources** - Educational content library
8. **payments** - Chapa payment integration

### Key Models

#### User Management (accounts/models.py)
- `UserProfile` - Extended user information
- `PsychologistProfile` - Professional credentials and settings
- `UserReport` - Content moderation system

#### Core System (core/models.py)
- `SiteSettings` - Global configuration
- `FAQ` - Frequently asked questions
- `ContactMessage` - Contact form submissions
- `Notification` - User notifications

## 🔧 Development Setup

### Required Services
```bash
# Redis for channels (Windows)
# Download and install Redis for Windows
# Or use Docker:
docker run -d -p 6379:6379 redis:alpine

# Start Redis
redis-server
```

### Development Commands
```bash
# Create new migration
python manage.py makemigrations [app_name]

# Apply migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Collect static files
python manage.py collectstatic

# Run development server
python manage.py runserver

# Run with Daphne (for WebSockets)
daphne -p 8000 ecpi_platform.asgi:application
```

## 📝 Implementation Roadmap

### Phase 1: Core Features (Current)
- [x] Project setup and structure
- [x] User authentication system
- [x] Basic templates and styling
- [x] Database models
- [x] Admin interface
- [ ] User registration and profiles
- [ ] Basic navigation and UI

### Phase 2: User Management
- [ ] User registration forms
- [ ] Email verification
- [ ] Profile management
- [ ] Psychologist registration
- [ ] Admin approval system
- [ ] Age verification

### Phase 3: Discussion System
- [ ] Discussion models
- [ ] Create/edit discussions
- [ ] Reply system
- [ ] Category management
- [ ] Content moderation
- [ ] Search functionality

### Phase 4: Consultation System
- [ ] Psychologist profiles
- [ ] Booking system
- [ ] Calendar integration
- [ ] Session management
- [ ] Rating system
- [ ] Free consultation logic

### Phase 5: Real-time Features
- [ ] WebSocket setup
- [ ] Private messaging
- [ ] Consultation chat
- [ ] Real-time notifications
- [ ] Online status

### Phase 6: Resources & Meetings
- [ ] File upload system
- [ ] Resource categorization
- [ ] Meeting creation
- [ ] Event registration
- [ ] Calendar views

### Phase 7: Payment Integration
- [ ] Chapa API integration
- [ ] Payment forms
- [ ] Transaction handling
- [ ] Webhook processing
- [ ] Payment history

### Phase 8: Advanced Features
- [ ] AI chatbot enhancement
- [ ] Search optimization
- [ ] Analytics dashboard
- [ ] Mobile app API
- [ ] Performance optimization

## 🎨 Frontend Development

### CSS Architecture
```
static/css/
├── base.css          # Global styles
├── form.css          # Form styling
├── chatbot.css       # Chatbot widget
├── amharic.css       # RTL and Amharic fonts
└── components/       # Component-specific styles
```

### JavaScript Structure
```
static/js/
├── main.js           # Core functionality
├── chatbot.js        # Chatbot logic
├── chat.js           # Real-time chat
├── booking.js        # Consultation booking
└── validation.js     # Form validation
```

### Template Hierarchy
```
templates/
├── base.html         # Main layout
├── includes/         # Reusable components
├── registration/     # Auth templates
└── [app_name]/       # App-specific templates
```

## 🔌 API Development

### REST API Endpoints (Future)
```
/api/v1/
├── auth/             # Authentication
├── users/            # User management
├── discussions/      # Forum API
├── consultations/    # Booking API
├── messages/         # Chat API
└── resources/        # Content API
```

### WebSocket Endpoints
```
/ws/
├── chat/{room_name}/              # General chat
├── consultation/{consultation_id}/ # Session chat
└── notifications/{user_id}/        # Real-time notifications
```

## 🧪 Testing Strategy

### Test Structure
```python
# Example test file
from django.test import TestCase
from django.contrib.auth.models import User
from accounts.models import UserProfile

class UserProfileTestCase(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_profile_creation(self):
        profile = UserProfile.objects.create(
            user=self.user,
            bio='Test bio'
        )
        self.assertEqual(profile.user.username, 'testuser')
```

### Testing Commands
```bash
# Run all tests
python manage.py test

# Run specific app tests
python manage.py test accounts

# Run with coverage
coverage run --source='.' manage.py test
coverage report
```

## 🌐 Internationalization

### Translation Workflow
```bash
# Extract translatable strings
python manage.py makemessages -l am

# Compile translations
python manage.py compilemessages

# Update existing translations
python manage.py makemessages -l am --no-obsolete
```

### Translation Files
```
locale/
├── en/
│   └── LC_MESSAGES/
│       ├── django.po
│       └── django.mo
└── am/
    └── LC_MESSAGES/
        ├── django.po
        └── django.mo
```

## 🔒 Security Considerations

### Security Checklist
- [ ] CSRF protection enabled
- [ ] SQL injection prevention
- [ ] XSS protection
- [ ] Secure file uploads
- [ ] Rate limiting
- [ ] Input validation
- [ ] Authentication security
- [ ] HTTPS enforcement

### Security Settings
```python
# settings.py security settings
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
```

## 📊 Performance Optimization

### Database Optimization
- Use select_related() for foreign keys
- Use prefetch_related() for many-to-many
- Add database indexes
- Optimize queries with django-debug-toolbar

### Caching Strategy
```python
# Cache configuration
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}
```

## 🚀 Deployment Guide

### Production Checklist
- [ ] Set DEBUG=False
- [ ] Configure production database
- [ ] Set up Redis server
- [ ] Configure static file serving
- [ ] Set up SSL certificates
- [ ] Configure email backend
- [ ] Set up monitoring
- [ ] Configure backups

### Docker Setup (Optional)
```dockerfile
# Dockerfile example
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["daphne", "-b", "0.0.0.0", "-p", "8000", "ecpi_platform.asgi:application"]
```

## 📚 Resources

### Documentation
- [Django Documentation](https://docs.djangoproject.com/)
- [Django Channels](https://channels.readthedocs.io/)
- [Bootstrap 5](https://getbootstrap.com/docs/5.3/)
- [Chapa API](https://developer.chapa.co/)

### Tools
- [Django Debug Toolbar](https://django-debug-toolbar.readthedocs.io/)
- [Django Extensions](https://django-extensions.readthedocs.io/)
- [Redis](https://redis.io/documentation)

## 🤝 Contributing Guidelines

1. Follow PEP 8 style guide
2. Write meaningful commit messages
3. Add tests for new features
4. Update documentation
5. Use type hints where applicable
6. Follow Django best practices

### Code Style
```python
# Example of good code style
from typing import Optional
from django.db import models

class UserProfile(models.Model):
    """Extended user profile with additional information."""
    
    user: models.OneToOneField = models.OneToOneField(
        'auth.User',
        on_delete=models.CASCADE,
        related_name='profile'
    )
    
    def get_full_name(self) -> str:
        """Return user's full name or username as fallback."""
        return f"{self.user.first_name} {self.user.last_name}".strip() or self.user.username
```

---

This development guide will be updated as the project evolves. For questions or clarifications, please refer to the project documentation or contact the development team.
