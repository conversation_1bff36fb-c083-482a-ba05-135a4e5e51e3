from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth.models import User
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django.shortcuts import redirect
from .models import User<PERSON><PERSON><PERSON>le, Psychologist<PERSON><PERSON><PERSON><PERSON>, UserReport

# Unregister the default User admin
admin.site.unregister(User)

@admin.register(User)
class CustomUserAdmin(UserAdmin):
    """Enhanced User admin with profile information"""
    list_display = ('username', 'email', 'first_name', 'last_name', 'is_staff', 'is_active', 'date_joined', 'get_user_type')
    list_filter = ('is_staff', 'is_superuser', 'is_active', 'date_joined', 'profile__is_psychologist')
    search_fields = ('username', 'first_name', 'last_name', 'email')
    ordering = ('-date_joined',)

    def get_user_type(self, obj):
        try:
            if obj.profile.is_psychologist:
                return "Psychologist"
            else:
                return "End User"
        except:
            return "No Profile"
    get_user_type.short_description = 'User Type'
    get_user_type.admin_order_field = 'profile__is_psychologist'

@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """User Profile admin"""
    list_display = ('user', 'is_psychologist', 'is_verified', 'birth_date', 'gender', 'created_at')
    list_filter = ('is_psychologist', 'is_verified', 'gender', 'created_at')
    search_fields = ('user__username', 'user__email', 'user__first_name', 'user__last_name', 'phone')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        (_('User Information'), {
            'fields': ('user', 'is_psychologist', 'is_verified')
        }),
        (_('Personal Information'), {
            'fields': ('birth_date', 'gender', 'phone', 'address', 'bio', 'avatar')
        }),
        (_('Preferences'), {
            'fields': ('preferred_language', 'show_email', 'show_phone', 'allow_messages')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(PsychologistProfile)
class PsychologistProfileAdmin(admin.ModelAdmin):
    """Psychologist Profile admin with approval and verification capabilities"""
    list_display = ('user', 'license_number', 'years_of_experience', 'approval_status', 'is_verified_display', 'is_available', 'consultation_fee', 'total_consultations', 'average_rating')
    list_filter = ('approval_status', 'user__profile__is_verified', 'is_available', 'years_of_experience', 'created_at')
    search_fields = ('user__username', 'user__email', 'license_number', 'specializations')
    readonly_fields = ('created_at', 'updated_at', 'total_consultations', 'total_ratings', 'average_rating', 'get_verification_status')

    fieldsets = (
        (_('Basic Information'), {
            'fields': ('user', 'license_number')
        }),
        (_('Professional Details'), {
            'fields': ('specializations', 'specialties', 'years_of_experience', 'education', 'certifications')
        }),
        (_('Consultation Settings'), {
            'fields': ('consultation_fee', 'offers_free_consultation', 'available_languages', 'working_hours_start', 'working_hours_end', 'working_days')
        }),
        (_('Availability & Verification'), {
            'fields': ('is_available', 'approval_status', 'approval_date', 'approved_by')
        }),
        (_('Verification Status'), {
            'fields': ('get_verification_status',),
            'classes': ('collapse',)
        }),
        (_('Documents'), {
            'fields': ('license_document', 'cv_document')
        }),
        (_('Statistics'), {
            'fields': ('total_consultations', 'total_ratings', 'average_rating'),
            'classes': ('collapse',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['approve_psychologists', 'reject_psychologists', 'deactivate_psychologists', 'verify_psychologists', 'unverify_psychologists']

    def approve_psychologists(self, request, queryset):
        from django.utils import timezone
        updated = queryset.update(
            approval_status='approved',
            approval_date=timezone.now(),
            approved_by=request.user
        )
        self.message_user(request, f'{updated} psychologist(s) approved successfully.')
    approve_psychologists.short_description = _('Approve selected psychologists')

    def reject_psychologists(self, request, queryset):
        updated = queryset.update(approval_status='rejected')
        self.message_user(request, f'{updated} psychologist(s) rejected.')
    reject_psychologists.short_description = _('Reject selected psychologists')

    def deactivate_psychologists(self, request, queryset):
        updated = queryset.update(is_available=False)
        self.message_user(request, f'{updated} psychologist(s) deactivated.')
    deactivate_psychologists.short_description = _('Deactivate selected psychologists')

    def is_verified_display(self, obj):
        """Display verification status with colored indicator"""
        if obj.user.profile.is_verified:
            return format_html(
                '<span style="color: green; font-weight: bold;">✓ Verified</span>'
            )
        else:
            return format_html(
                '<span style="color: red; font-weight: bold;">✗ Not Verified</span>'
            )
    is_verified_display.short_description = _('Verification Status')
    is_verified_display.admin_order_field = 'user__profile__is_verified'

    def verify_psychologists(self, request, queryset):
        """Verify selected psychologists"""
        updated = 0
        for psychologist in queryset:
            if not psychologist.user.profile.is_verified:
                psychologist.user.profile.is_verified = True
                psychologist.user.profile.save()
                updated += 1

        self.message_user(request, f'{updated} psychologist(s) verified successfully.')
    verify_psychologists.short_description = _('Verify selected psychologists')

    def unverify_psychologists(self, request, queryset):
        """Remove verification from selected psychologists"""
        updated = 0
        for psychologist in queryset:
            if psychologist.user.profile.is_verified:
                psychologist.user.profile.is_verified = False
                psychologist.user.profile.save()
                updated += 1

        self.message_user(request, f'{updated} psychologist(s) unverified.')
    unverify_psychologists.short_description = _('Remove verification from selected psychologists')

    def get_verification_status(self, obj):
        """Display verification status with action buttons in form"""
        if obj.user.profile.is_verified:
            return format_html(
                '<div style="padding: 10px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px;">'
                '<strong style="color: #155724;">✓ This psychologist is VERIFIED</strong><br>'
                '<small style="color: #155724;">Verified psychologists have completed identity and credential verification.</small>'
                '</div>'
            )
        else:
            return format_html(
                '<div style="padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px;">'
                '<strong style="color: #721c24;">✗ This psychologist is NOT VERIFIED</strong><br>'
                '<small style="color: #721c24;">Use the verification actions above to verify this psychologist.</small>'
                '</div>'
            )
    get_verification_status.short_description = _('Current Verification Status')

    def response_change(self, request, obj):
        """Handle verification actions from the change form"""
        if 'action' in request.POST:
            action = request.POST.get('action')

            if action == 'verify' and not obj.user.profile.is_verified:
                obj.user.profile.is_verified = True
                obj.user.profile.save()
                self.message_user(request, f'Dr. {obj.user.get_full_name()} has been verified successfully.', level='SUCCESS')

            elif action == 'unverify' and obj.user.profile.is_verified:
                obj.user.profile.is_verified = False
                obj.user.profile.save()
                self.message_user(request, f'Verification removed from Dr. {obj.user.get_full_name()}.', level='WARNING')

            # Redirect to the same page to show updated status
            return redirect(request.get_full_path())

        return super().response_change(request, obj)

@admin.register(UserReport)
class UserReportAdmin(admin.ModelAdmin):
    """User Report admin for content moderation"""
    list_display = ('reporter', 'reported_user', 'report_type', 'status', 'created_at')
    list_filter = ('report_type', 'status', 'created_at')
    search_fields = ('reporter__username', 'reported_user__username', 'description')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        (_('Report Information'), {
            'fields': ('reporter', 'reported_user', 'report_type', 'description', 'evidence')
        }),
        (_('Moderation'), {
            'fields': ('status', 'admin_notes', 'handled_by', 'resolution_date')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at')
        }),
    )

    actions = ['mark_resolved', 'mark_pending']

    def mark_resolved(self, request, queryset):
        from django.utils import timezone
        updated = queryset.update(
            status='resolved',
            handled_by=request.user,
            resolution_date=timezone.now()
        )
        self.message_user(request, f'{updated} report(s) marked as resolved.')
    mark_resolved.short_description = _('Mark selected reports as resolved')

    def mark_pending(self, request, queryset):
        updated = queryset.update(status='pending', handled_by=None, resolution_date=None)
        self.message_user(request, f'{updated} report(s) marked as pending.')
    mark_pending.short_description = _('Mark selected reports as pending')
