# Generated by Django 5.2.4 on 2025-07-25 12:42

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PsychologistProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('license_number', models.CharField(max_length=100, unique=True)),
                ('specializations', models.CharField(help_text='Comma-separated specializations', max_length=500)),
                ('years_of_experience', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(50)])),
                ('education', models.TextField(help_text='Educational background')),
                ('certifications', models.TextField(blank=True, help_text='Additional certifications')),
                ('license_document', models.FileField(help_text='Upload license document', upload_to='psychologist_docs/')),
                ('cv_document', models.FileField(blank=True, help_text='Upload CV/Resume', upload_to='psychologist_docs/')),
                ('consultation_fee', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('offers_free_consultation', models.BooleanField(default=True)),
                ('available_languages', models.CharField(default='en,am', help_text="Comma-separated language codes (e.g., 'en,am')", max_length=20)),
                ('is_available', models.BooleanField(default=True)),
                ('working_hours_start', models.TimeField(default='09:00')),
                ('working_hours_end', models.TimeField(default='17:00')),
                ('working_days', models.CharField(default='1,2,3,4,5', help_text='Comma-separated day numbers (1=Monday, 7=Sunday)', max_length=20)),
                ('approval_status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('suspended', 'Suspended')], default='pending', max_length=20)),
                ('approval_date', models.DateTimeField(blank=True, null=True)),
                ('rejection_reason', models.TextField(blank=True)),
                ('total_consultations', models.PositiveIntegerField(default=0)),
                ('average_rating', models.DecimalField(decimal_places=2, default=0.0, max_digits=3)),
                ('total_ratings', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_psychologists', to=settings.AUTH_USER_MODEL)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='psychologist_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Psychologist Profile',
                'verbose_name_plural': 'Psychologist Profiles',
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='avatars/')),
                ('bio', models.TextField(blank=True, max_length=500)),
                ('birth_date', models.DateField(blank=True, null=True)),
                ('gender', models.CharField(blank=True, choices=[('M', 'Male'), ('F', 'Female'), ('O', 'Other'), ('P', 'Prefer not to say')], max_length=1)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('address', models.TextField(blank=True)),
                ('city', models.CharField(blank=True, max_length=100)),
                ('country', models.CharField(default='Ethiopia', max_length=100)),
                ('preferred_language', models.CharField(choices=[('en', 'English'), ('am', 'Amharic')], default='en', max_length=5)),
                ('show_email', models.BooleanField(default=False)),
                ('show_phone', models.BooleanField(default=False)),
                ('allow_messages', models.BooleanField(default=True)),
                ('is_psychologist', models.BooleanField(default=False)),
                ('is_verified', models.BooleanField(default=False)),
                ('is_banned', models.BooleanField(default=False)),
                ('ban_reason', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Profile',
                'verbose_name_plural': 'User Profiles',
            },
        ),
        migrations.CreateModel(
            name='UserReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('report_type', models.CharField(choices=[('spam', 'Spam'), ('harassment', 'Harassment'), ('inappropriate', 'Inappropriate Content'), ('fake_profile', 'Fake Profile'), ('unprofessional', 'Unprofessional Behavior'), ('other', 'Other')], max_length=20)),
                ('description', models.TextField()),
                ('evidence', models.FileField(blank=True, null=True, upload_to='reports/')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('investigating', 'Under Investigation'), ('resolved', 'Resolved'), ('dismissed', 'Dismissed')], default='pending', max_length=20)),
                ('admin_notes', models.TextField(blank=True)),
                ('resolution_date', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('handled_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='handled_reports', to=settings.AUTH_USER_MODEL)),
                ('reported_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reports_received', to=settings.AUTH_USER_MODEL)),
                ('reporter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reports_made', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Report',
                'verbose_name_plural': 'User Reports',
                'ordering': ['-created_at'],
            },
        ),
    ]
