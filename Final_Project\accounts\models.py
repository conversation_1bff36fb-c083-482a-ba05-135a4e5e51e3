from django.db import models
from django.contrib.auth.models import User
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from PIL import Image
import os

class UserProfile(models.Model):
    """Extended user profile"""
    GENDER_CHOICES = [
        ('M', _('Male')),
        ('F', _('Female')),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True)
    bio = models.TextField(max_length=500, blank=True)
    birth_date = models.DateField(null=True, blank=True)
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES, blank=True)
    phone = models.CharField(max_length=20, blank=True)
    address = models.TextField(blank=True)
    city = models.CharField(max_length=100, blank=True)
    country = models.CharField(max_length=100, default='Ethiopia')

    # Language preference
    preferred_language = models.CharField(
        max_length=5,
        choices=[('en', 'English'), ('am', 'Amharic')],
        default='en'
    )

    # Privacy settings
    show_email = models.BooleanField(default=False)
    show_phone = models.BooleanField(default=False)
    allow_messages = models.BooleanField(default=True)

    # Identity verification
    id_certificate = models.FileField(upload_to='user_documents/', blank=True, help_text=_("ID card or birth certificate"))

    # Account status
    is_psychologist = models.BooleanField(default=False)
    is_verified = models.BooleanField(default=False)
    is_banned = models.BooleanField(default=False)
    ban_reason = models.TextField(blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("User Profile")
        verbose_name_plural = _("User Profiles")

    def __str__(self):
        return f"{self.user.username}'s Profile"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

        # Resize avatar if it exists
        if self.avatar:
            img = Image.open(self.avatar.path)
            if img.height > 300 or img.width > 300:
                output_size = (300, 300)
                img.thumbnail(output_size)
                img.save(self.avatar.path)

    @property
    def age(self):
        """Calculate user's age"""
        if self.birth_date:
            from datetime import date
            today = date.today()
            return today.year - self.birth_date.year - ((today.month, today.day) < (self.birth_date.month, self.birth_date.day))
        return None

    @property
    def full_name(self):
        """Get user's full name"""
        return f"{self.user.first_name} {self.user.last_name}".strip() or self.user.username

class PsychologistProfile(models.Model):
    """Extended profile for psychologists"""
    SPECIALIZATION_CHOICES = [
        ('clinical', _('Clinical Psychology')),
        ('counseling', _('Counseling Psychology')),
        ('child', _('Child Psychology')),
        ('adolescent', _('Adolescent Psychology')),
        ('family', _('Family Therapy')),
        ('cognitive', _('Cognitive Behavioral Therapy')),
        ('trauma', _('Trauma Therapy')),
        ('addiction', _('Addiction Counseling')),
        ('anxiety', _('Anxiety Disorders')),
        ('depression', _('Depression Treatment')),
        ('relationship', _('Relationship Counseling')),
        ('grief', _('Grief Counseling')),
    ]

    APPROVAL_STATUS = [
        ('pending', _('Pending')),
        ('approved', _('Approved')),
        ('rejected', _('Rejected')),
        ('suspended', _('Suspended')),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='psychologist_profile')

    # Professional information
    license_number = models.CharField(max_length=100, unique=True)
    specializations = models.CharField(max_length=500, help_text=_("Comma-separated specializations"))
    specialties = models.ManyToManyField(
        'consultation.PsychologistSpecialty',
        blank=True,
        help_text=_("Select your specialties")
    )
    years_of_experience = models.PositiveIntegerField(validators=[MinValueValidator(0), MaxValueValidator(50)])
    education = models.TextField(help_text=_("Educational background"))
    certifications = models.TextField(blank=True, help_text=_("Additional certifications"))

    # Documents
    license_document = models.FileField(upload_to='psychologist_docs/', help_text=_("Upload license document"))
    cv_document = models.FileField(upload_to='psychologist_docs/', blank=True, help_text=_("Upload CV/Resume"))

    # Consultation settings
    consultation_fee = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    offers_free_consultation = models.BooleanField(default=True)
    available_languages = models.CharField(
        max_length=20,
        default='en,am',
        help_text=_("Comma-separated language codes (e.g., 'en,am')")
    )

    # Availability
    is_available = models.BooleanField(default=True)
    working_hours_start = models.TimeField(default='09:00')
    working_hours_end = models.TimeField(default='17:00')
    working_days = models.CharField(
        max_length=20,
        default='1,2,3,4,5',
        help_text=_("Comma-separated day numbers (1=Monday, 7=Sunday)")
    )

    # Approval and ratings
    approval_status = models.CharField(max_length=20, choices=APPROVAL_STATUS, default='pending')
    approval_date = models.DateTimeField(null=True, blank=True)
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_psychologists'
    )
    rejection_reason = models.TextField(blank=True)

    # Statistics
    total_consultations = models.PositiveIntegerField(default=0)
    average_rating = models.DecimalField(max_digits=3, decimal_places=2, default=0.00)
    total_ratings = models.PositiveIntegerField(default=0)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("Psychologist Profile")
        verbose_name_plural = _("Psychologist Profiles")

    def __str__(self):
        return f"Dr. {self.user.get_full_name() or self.user.username}"

    @property
    def is_approved(self):
        return self.approval_status == 'approved'

    @property
    def specialization_list(self):
        """Get list of specializations"""
        return [spec.strip() for spec in self.specializations.split(',') if spec.strip()]

    @property
    def language_list(self):
        """Get list of available languages"""
        return [lang.strip() for lang in self.available_languages.split(',') if lang.strip()]

    @property
    def working_days_list(self):
        """Get list of working days as integers"""
        return [int(day.strip()) for day in self.working_days.split(',') if day.strip().isdigit()]

class UserReport(models.Model):
    """User reports for inappropriate content or behavior"""
    REPORT_TYPES = [
        ('spam', _('Spam')),
        ('harassment', _('Harassment')),
        ('inappropriate', _('Inappropriate Content')),
        ('fake_profile', _('Fake Profile')),
        ('unprofessional', _('Unprofessional Behavior')),
        ('other', _('Other')),
    ]

    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('investigating', _('Under Investigation')),
        ('resolved', _('Resolved')),
        ('dismissed', _('Dismissed')),
    ]

    reporter = models.ForeignKey(User, on_delete=models.CASCADE, related_name='reports_made')
    reported_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='reports_received')
    report_type = models.CharField(max_length=20, choices=REPORT_TYPES)
    description = models.TextField()
    evidence = models.FileField(upload_to='reports/', blank=True, null=True)

    # Admin handling
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    handled_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='handled_reports'
    )
    admin_notes = models.TextField(blank=True)
    resolution_date = models.DateTimeField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("User Report")
        verbose_name_plural = _("User Reports")
        ordering = ['-created_at']

    def __str__(self):
        return f"Report: {self.reporter.username} -> {self.reported_user.username}"
