from django.shortcuts import render, redirect
from django.views.generic import Template<PERSON><PERSON><PERSON>, CreateView, View
from django.contrib.auth import views as auth_views, login
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import User
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.urls import reverse_lazy
from django.db import models
from django import forms
from .models import UserProfile, PsychologistProfile

# Authentication views
class LoginView(auth_views.LoginView):
    template_name = 'registration/login.html'
    redirect_authenticated_user = True

    def get_success_url(self):
        return reverse_lazy('core:dashboard')

class LogoutView(auth_views.LogoutView):
    next_page = reverse_lazy('core:home')

    def get(self, request, *args, **kwargs):
        # Handle GET requests by logging out the user
        return self.post(request, *args, **kwargs)

    def dispatch(self, request, *args, **kwargs):
        if request.user.is_authenticated:
            messages.success(request, _('You have been successfully logged out.'))
        return super().dispatch(request, *args, **kwargs)

class CustomUserCreationForm(UserCreationForm):
    email = forms.EmailField(required=True)
    first_name = forms.CharField(max_length=30, required=False)
    last_name = forms.CharField(max_length=30, required=False)
    birth_date = forms.DateField(
        required=True,
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text=_("You must be at least 14 years old to use this platform")
    )
    id_certificate = forms.FileField(
        required=True,
        help_text=_("Upload a copy of your ID card or birth certificate (PDF, JPG, PNG)")
    )

    class Meta:
        model = User
        fields = ('username', 'email', 'first_name', 'last_name', 'password1', 'password2')

    def clean_birth_date(self):
        birth_date = self.cleaned_data.get('birth_date')
        if birth_date:
            from datetime import date
            today = date.today()
            age = today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))
            if age < 14:
                raise forms.ValidationError(_("You must be at least 14 years old to register."))
        return birth_date

    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        if commit:
            user.save()
            # Create user profile
            UserProfile.objects.create(
                user=user,
                birth_date=self.cleaned_data['birth_date'],
                id_certificate=self.cleaned_data['id_certificate']
            )
        return user

class SignUpView(CreateView):
    form_class = CustomUserCreationForm
    template_name = 'registration/signup.html'
    success_url = reverse_lazy('core:dashboard')

    def form_valid(self, form):
        response = super().form_valid(form)
        # Log the user in after successful registration
        login(self.request, self.object)
        messages.success(self.request, _('Welcome to ECPI! Your account has been created successfully.'))
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _('Sign Up')
        return context

class PsychologistCreationForm(CustomUserCreationForm):
    # Professional Information
    license_number = forms.CharField(max_length=100, required=True, help_text=_("Your professional license number"))
    years_of_experience = forms.IntegerField(min_value=0, max_value=50, required=True)
    education = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=True, help_text=_("Your educational background"))
    specializations = forms.MultipleChoiceField(
        choices=PsychologistProfile.SPECIALIZATION_CHOICES,
        widget=forms.CheckboxSelectMultiple,
        required=True,
        help_text=_("Select your specializations")
    )

    # Documents
    license_document = forms.FileField(required=True, help_text=_("Upload your license document (PDF, JPG, PNG)"))
    cv_document = forms.FileField(required=False, help_text=_("Upload your CV/Resume (PDF)"))

    class Meta:
        model = User
        fields = ('username', 'email', 'first_name', 'last_name', 'password1', 'password2')

    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        if commit:
            user.save()
            # Create user profile
            UserProfile.objects.create(user=user, is_psychologist=True)
            # Create psychologist profile
            PsychologistProfile.objects.create(
                user=user,
                license_number=self.cleaned_data['license_number'],
                years_of_experience=self.cleaned_data['years_of_experience'],
                education=self.cleaned_data['education'],
                specializations=','.join(self.cleaned_data['specializations']),
                license_document=self.cleaned_data['license_document'],
                cv_document=self.cleaned_data.get('cv_document'),
                approval_status='pending'
            )
        return user

class PsychologistSignUpView(CreateView):
    form_class = PsychologistCreationForm
    template_name = 'accounts/psychologist_register.html'
    success_url = reverse_lazy('core:dashboard')

    def form_valid(self, form):
        response = super().form_valid(form)
        # Log the user in after successful registration
        login(self.request, self.object)
        messages.success(self.request, _('Welcome to ECPI! Your psychologist application has been submitted for review.'))
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _('Join as Psychologist')
        return context

class ProfileView(LoginRequiredMixin, TemplateView):
    template_name = 'accounts/profile.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user

        # Get or create user profile
        profile, created = UserProfile.objects.get_or_create(user=user)

        # Basic statistics
        if hasattr(user, 'psychologist_profile'):
            # Psychologist statistics
            from consultation.models import Consultation, ConsultationRating
            total_consultations = Consultation.objects.filter(psychologist=user.psychologist_profile).count()
            ratings = ConsultationRating.objects.filter(consultation__psychologist=user.psychologist_profile)
            average_rating = ratings.aggregate(avg_rating=models.Avg('overall_rating'))['avg_rating'] or 0
            total_ratings = ratings.count()

            context.update({
                'total_consultations': total_consultations,
                'average_rating': round(average_rating, 1),
                'total_ratings': total_ratings,
            })
        else:
            # Patient statistics
            from consultation.models import Consultation
            from django.utils import timezone

            total_consultations = Consultation.objects.filter(user=user).count()
            completed_consultations = Consultation.objects.filter(
                user=user,
                status='completed'
            ).count()
            upcoming_consultations = Consultation.objects.filter(
                user=user,
                status__in=['confirmed', 'pending'],
                scheduled_date__gte=timezone.now().date()
            ).count()

            context.update({
                'total_consultations': total_consultations,
                'completed_consultations': completed_consultations,
                'upcoming_consultations': upcoming_consultations,
            })

        return context

class EditProfileView(LoginRequiredMixin, View):
    template_name = 'accounts/update_profile.html'

    def get(self, request):
        return render(request, self.template_name)

    def post(self, request):
        user = request.user
        profile, created = UserProfile.objects.get_or_create(user=user)

        # Update user fields
        user.first_name = request.POST.get('first_name', '')
        user.last_name = request.POST.get('last_name', '')
        user.email = request.POST.get('email', '')
        user.save()

        # Update profile fields
        profile.phone = request.POST.get('phone', '')
        profile.bio = request.POST.get('bio', '')
        profile.birth_date = request.POST.get('birth_date') or None
        profile.gender = request.POST.get('gender', '')
        profile.address = request.POST.get('address', '')
        profile.city = request.POST.get('city', '')
        profile.country = request.POST.get('country', 'Ethiopia')
        profile.preferred_language = request.POST.get('preferred_language', 'en')

        # Privacy settings
        profile.show_email = 'show_email' in request.POST
        profile.show_phone = 'show_phone' in request.POST
        profile.allow_messages = 'allow_messages' in request.POST

        # Handle file uploads
        if 'avatar' in request.FILES:
            profile.avatar = request.FILES['avatar']

        if 'id_certificate' in request.FILES:
            profile.id_certificate = request.FILES['id_certificate']

        profile.save()

        messages.success(request, _('Profile updated successfully!'))
        return redirect('accounts:profile')

class ChangePasswordView(auth_views.PasswordChangeView):
    template_name = 'registration/password_change_form.html'

class PasswordResetView(auth_views.PasswordResetView):
    template_name = 'registration/password_reset_form.html'

class PasswordResetDoneView(auth_views.PasswordResetDoneView):
    template_name = 'registration/password_reset_done.html'

class PasswordResetConfirmView(auth_views.PasswordResetConfirmView):
    template_name = 'registration/password_reset_confirm.html'

class PasswordResetCompleteView(auth_views.PasswordResetCompleteView):
    template_name = 'registration/password_reset_complete.html'

class VerifyEmailView(TemplateView):
    template_name = 'registration/verify_email.html'

class VerifyEmailDoneView(TemplateView):
    template_name = 'registration/verify_email_done.html'

class VerifyEmailConfirmView(TemplateView):
    template_name = 'registration/verify_email_confirm.html'
