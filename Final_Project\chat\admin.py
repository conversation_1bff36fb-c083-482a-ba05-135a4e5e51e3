from django.contrib import admin

# Chat models admin will be added when migrations are run
# Uncomment the following when chat migrations are applied:

# from django.utils.translation import gettext_lazy as _
# from .models import Conversation, ChatMessage
#
# @admin.register(Conversation)
# class ConversationAdmin(admin.ModelAdmin):
#     """Conversation admin"""
#     list_display = ('id', 'get_participants', 'created_at', 'updated_at')
#     list_filter = ('created_at', 'updated_at')
#     search_fields = ('participants__username', 'participants__email')
#     readonly_fields = ('id', 'created_at', 'updated_at')
#     filter_horizontal = ('participants',)
#
#     def get_participants(self, obj):
#         return ", ".join([user.username for user in obj.participants.all()])
#     get_participants.short_description = _('Participants')
#
# @admin.register(ChatMessage)
# class ChatMessageAdmin(admin.ModelAdmin):
#     """Chat message admin"""
#     list_display = ('sender', 'recipient', 'content_preview', 'is_read', 'created_at')
#     list_filter = ('is_read', 'created_at', 'sender', 'recipient')
#     search_fields = ('sender__username', 'recipient__username', 'content')
#     readonly_fields = ('id', 'created_at')
#     raw_id_fields = ('conversation', 'sender', 'recipient')
#
#     def content_preview(self, obj):
#         return obj.content[:50] + "..." if len(obj.content) > 50 else obj.content
#     content_preview.short_description = _('Content Preview')
