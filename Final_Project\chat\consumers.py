import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth.models import User
from django.utils import timezone

class ChatConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.room_name = self.scope['url_route']['kwargs']['room_name']
        self.room_group_name = f'chat_{self.room_name}'

        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )

        await self.accept()

    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )

    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message = text_data_json['message']
        user = self.scope['user']

        if user.is_authenticated:
            # Send message to room group
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'chat_message',
                    'message': message,
                    'user': user.username,
                    'user_id': user.id
                }
            )

    async def chat_message(self, event):
        message = event['message']
        user = event['user']
        user_id = event['user_id']

        # Send message to WebSocket
        await self.send(text_data=json.dumps({
            'message': message,
            'user': user,
            'user_id': user_id
        }))

class ConsultationChatConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.consultation_id = self.scope['url_route']['kwargs']['consultation_id']
        self.room_group_name = f'consultation_{self.consultation_id}'
        self.user = self.scope['user']

        # Verify user has access to this consultation
        if not await self.verify_consultation_access():
            await self.close()
            return

        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )

        await self.accept()

        # Send user joined notification
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'user_status',
                'user': self.user.username,
                'user_id': self.user.id,
                'status': 'joined',
                'timestamp': str(timezone.now())
            }
        )

    async def disconnect(self, close_code):
        # Send user left notification
        if hasattr(self, 'room_group_name') and hasattr(self, 'user'):
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'user_status',
                    'user': self.user.username,
                    'user_id': self.user.id,
                    'status': 'left',
                    'timestamp': str(timezone.now())
                }
            )

        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )

    async def receive(self, text_data):
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type', 'text')

            if not self.user.is_authenticated:
                await self.send_error('Authentication required')
                return

            if message_type == 'text':
                await self.handle_text_message(text_data_json)
            elif message_type == 'typing':
                await self.handle_typing_indicator(text_data_json)
            elif message_type == 'end_session':
                await self.handle_end_session()
            else:
                await self.send_error('Unknown message type')

        except json.JSONDecodeError:
            await self.send_error('Invalid JSON format')
        except Exception as e:
            await self.send_error(f'Error processing message: {str(e)}')

    async def handle_text_message(self, data):
        message = data.get('message', '').strip()

        if not message:
            return

        if len(message) > 1000:
            await self.send_error('Message too long (max 1000 characters)')
            return

        # Save message to database
        message_obj = await self.save_message(self.user, message, 'text')

        if message_obj:
            # Send message to room group
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'consultation_message',
                    'message': message,
                    'user': self.user.get_full_name() or self.user.username,
                    'user_id': self.user.id,
                    'message_id': str(message_obj.id),
                    'timestamp': str(message_obj.created_at)
                }
            )

    async def handle_typing_indicator(self, data):
        typing = data.get('typing', False)

        # Send typing indicator to room group (excluding sender)
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'typing_indicator',
                'user': self.user.get_full_name() or self.user.username,
                'user_id': self.user.id,
                'typing': typing
            }
        )

    async def handle_end_session(self):
        # Only psychologists can end sessions
        if not await self.is_psychologist():
            await self.send_error('Only psychologists can end sessions')
            return

        # Update consultation status
        await self.update_consultation_status('completed')

        # Send session ended notification
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'session_ended',
                'user': self.user.get_full_name() or self.user.username,
                'timestamp': str(timezone.now())
            }
        )

    async def consultation_message(self, event):
        # Don't send message back to sender
        if event['user_id'] != self.user.id:
            await self.send(text_data=json.dumps({
                'type': 'message',
                'message': event['message'],
                'user': event['user'],
                'user_id': event['user_id'],
                'message_id': event.get('message_id'),
                'timestamp': event['timestamp']
            }))

    async def typing_indicator(self, event):
        # Don't send typing indicator back to sender
        if event['user_id'] != self.user.id:
            await self.send(text_data=json.dumps({
                'type': 'typing',
                'user': event['user'],
                'typing': event['typing']
            }))

    async def user_status(self, event):
        # Don't send status back to sender
        if event['user_id'] != self.user.id:
            await self.send(text_data=json.dumps({
                'type': 'user_status',
                'user': event['user'],
                'status': event['status'],
                'timestamp': event['timestamp']
            }))

    async def session_ended(self, event):
        await self.send(text_data=json.dumps({
            'type': 'session_ended',
            'user': event['user'],
            'timestamp': event['timestamp']
        }))

    async def send_error(self, error_message):
        await self.send(text_data=json.dumps({
            'type': 'error',
            'message': error_message
        }))

    @database_sync_to_async
    def verify_consultation_access(self):
        from consultation.models import Consultation

        try:
            consultation = Consultation.objects.get(id=self.consultation_id)
            return (consultation.user == self.user or
                   consultation.psychologist.user == self.user)
        except Consultation.DoesNotExist:
            return False

    @database_sync_to_async
    def is_psychologist(self):
        from consultation.models import Consultation

        try:
            consultation = Consultation.objects.get(id=self.consultation_id)
            return consultation.psychologist.user == self.user
        except Consultation.DoesNotExist:
            return False

    @database_sync_to_async
    def save_message(self, user, message, message_type='text'):
        from consultation.models import Consultation, ConsultationMessage

        try:
            consultation = Consultation.objects.get(id=self.consultation_id)
            return ConsultationMessage.objects.create(
                consultation=consultation,
                sender=user,
                message=message,
                message_type=message_type
            )
        except Consultation.DoesNotExist:
            return None

    @database_sync_to_async
    def update_consultation_status(self, status):
        from consultation.models import Consultation

        try:
            consultation = Consultation.objects.get(id=self.consultation_id)
            consultation.status = status
            if status == 'completed':
                consultation.actual_end_time = timezone.now()
            consultation.save()
            return True
        except Consultation.DoesNotExist:
            return False
