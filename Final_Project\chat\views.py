from django.shortcuts import render
from django.views.generic import TemplateView, ListView
from django.http import JsonResponse

# Placeholder views - will be implemented later
class InboxView(ListView):
    template_name = 'chat/inbox.html'
    context_object_name = 'conversations'

    def get_queryset(self):
        return []

class ConversationView(TemplateView):
    template_name = 'chat/conversation.html'

class StartConversationView(TemplateView):
    template_name = 'chat/start_conversation.html'

class SendMessageAPIView(TemplateView):
    def post(self, request, *args, **kwargs):
        return JsonResponse({'status': 'success'})

class GetMessagesAPIView(TemplateView):
    def get(self, request, *args, **kwargs):
        return JsonResponse({'messages': []})
