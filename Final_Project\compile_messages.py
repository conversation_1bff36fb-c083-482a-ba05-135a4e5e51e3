#!/usr/bin/env python3
"""
Simple script to compile .po files to .mo files
"""
import os
import struct
import array

def compile_po_to_mo(po_file, mo_file):
    """
    Compile a .po file to .mo file
    """
    print(f"Compiling {po_file} to {mo_file}")
    
    # Read the .po file
    with open(po_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Parse the .po file
    entries = []
    current_msgid = ""
    current_msgstr = ""
    in_msgid = False
    in_msgstr = False
    
    for line in content.split('\n'):
        line = line.strip()
        
        if line.startswith('msgid '):
            if current_msgid and current_msgstr:
                entries.append((current_msgid, current_msgstr))
            current_msgid = line[7:-1]  # Remove 'msgid "' and '"'
            current_msgstr = ""
            in_msgid = True
            in_msgstr = False
        elif line.startswith('msgstr '):
            current_msgstr = line[8:-1]  # Remove 'msgstr "' and '"'
            in_msgid = False
            in_msgstr = True
        elif line.startswith('"') and line.endswith('"'):
            text = line[1:-1]  # Remove quotes
            if in_msgid:
                current_msgid += text
            elif in_msgstr:
                current_msgstr += text
        elif line == "" or line.startswith('#'):
            if current_msgid and current_msgstr:
                entries.append((current_msgid, current_msgstr))
                current_msgid = ""
                current_msgstr = ""
            in_msgid = False
            in_msgstr = False
    
    # Add the last entry
    if current_msgid and current_msgstr:
        entries.append((current_msgid, current_msgstr))
    
    # Filter out empty entries
    entries = [(msgid, msgstr) for msgid, msgstr in entries if msgid and msgstr]
    
    print(f"Found {len(entries)} translation entries")
    
    # Create .mo file content
    keys = []
    values = []
    
    for msgid, msgstr in entries:
        keys.append(msgid.encode('utf-8'))
        values.append(msgstr.encode('utf-8'))
    
    # Calculate offsets
    koffsets = []
    voffsets = []
    kencoded = b''.join(keys)
    vencoded = b''.join(values)
    
    # Header
    keystart = 7 * 4 + 16 * len(keys)
    valuestart = keystart + len(kencoded)
    
    koffset = keystart
    voffset = valuestart
    
    for key in keys:
        koffsets.append((len(key), koffset))
        koffset += len(key)
    
    for value in values:
        voffsets.append((len(value), voffset))
        voffset += len(value)
    
    # Create the .mo file
    with open(mo_file, 'wb') as f:
        # Magic number
        f.write(struct.pack('<I', 0x950412de))
        # Version
        f.write(struct.pack('<I', 0))
        # Number of entries
        f.write(struct.pack('<I', len(keys)))
        # Offset of key table
        f.write(struct.pack('<I', 7 * 4))
        # Offset of value table
        f.write(struct.pack('<I', 7 * 4 + 8 * len(keys)))
        # Hash table size
        f.write(struct.pack('<I', 0))
        # Offset of hash table
        f.write(struct.pack('<I', 0))
        
        # Key table
        for length, offset in koffsets:
            f.write(struct.pack('<I', length))
            f.write(struct.pack('<I', offset))
        
        # Value table
        for length, offset in voffsets:
            f.write(struct.pack('<I', length))
            f.write(struct.pack('<I', offset))
        
        # Keys
        f.write(kencoded)
        # Values
        f.write(vencoded)
    
    print(f"Successfully created {mo_file}")

if __name__ == "__main__":
    po_file = "locale/am/LC_MESSAGES/django.po"
    mo_file = "locale/am/LC_MESSAGES/django.mo"
    
    if os.path.exists(po_file):
        compile_po_to_mo(po_file, mo_file)
    else:
        print(f"Error: {po_file} not found")
