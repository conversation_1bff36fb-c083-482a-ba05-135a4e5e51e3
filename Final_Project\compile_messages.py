#!/usr/bin/env python3
"""
Simple script to compile .po files to .mo files
"""
import os
import struct
import array

def compile_po_to_mo(po_file, mo_file):
    """
    Compile a .po file to .mo file with proper UTF-8 handling
    """
    print(f"Compiling {po_file} to {mo_file}")

    # Read the .po file with explicit UTF-8 encoding
    with open(po_file, 'r', encoding='utf-8', errors='replace') as f:
        content = f.read()

    # Parse the .po file more carefully
    entries = []
    current_msgid = ""
    current_msgstr = ""
    in_msgid = False
    in_msgstr = False

    lines = content.split('\n')
    for i, line in enumerate(lines):
        line = line.strip()

        # Skip comments and empty lines
        if line.startswith('#') or not line:
            if current_msgid and current_msgstr:
                entries.append((current_msgid, current_msgstr))
                current_msgid = ""
                current_msgstr = ""
            in_msgid = False
            in_msgstr = False
            continue

        if line.startswith('msgid '):
            if current_msgid and current_msgstr:
                entries.append((current_msgid, current_msgstr))
            # Extract the quoted string
            current_msgid = line[6:].strip()
            if current_msgid.startswith('"') and current_msgid.endswith('"'):
                current_msgid = current_msgid[1:-1]
            current_msgstr = ""
            in_msgid = True
            in_msgstr = False
        elif line.startswith('msgstr '):
            # Extract the quoted string
            current_msgstr = line[7:].strip()
            if current_msgstr.startswith('"') and current_msgstr.endswith('"'):
                current_msgstr = current_msgstr[1:-1]
            in_msgid = False
            in_msgstr = True
        elif line.startswith('"') and line.endswith('"'):
            # Continuation line
            text = line[1:-1]  # Remove quotes
            if in_msgid:
                current_msgid += text
            elif in_msgstr:
                current_msgstr += text

    # Add the last entry
    if current_msgid and current_msgstr:
        entries.append((current_msgid, current_msgstr))

    # Filter out empty entries and decode escape sequences
    processed_entries = []
    for msgid, msgstr in entries:
        if msgid and msgstr:
            # Decode common escape sequences
            msgid = msgid.replace('\\n', '\n').replace('\\t', '\t').replace('\\"', '"')
            msgstr = msgstr.replace('\\n', '\n').replace('\\t', '\t').replace('\\"', '"')
            processed_entries.append((msgid, msgstr))

    print(f"Found {len(processed_entries)} translation entries")

    # Create .mo file using a simpler approach
    # Create a minimal .mo file that Django can read

    # Sort entries for consistent output
    processed_entries.sort()

    # Encode all strings as UTF-8
    keys = []
    values = []

    for msgid, msgstr in processed_entries:
        try:
            keys.append(msgid.encode('utf-8'))
            values.append(msgstr.encode('utf-8'))
        except UnicodeEncodeError as e:
            print(f"Warning: Skipping entry due to encoding error: {msgid[:50]}... - {e}")
            continue

    # Build the .mo file structure
    keyoffsets = []
    valueoffsets = []

    # Calculate the starting offset for the key table
    kstart = 7 * 4 + 16 * len(keys)
    vstart = kstart
    for k in keys:
        vstart += len(k)

    koffset = kstart
    voffset = vstart

    # Calculate offsets
    for k in keys:
        keyoffsets.append((len(k), koffset))
        koffset += len(k)

    for v in values:
        valueoffsets.append((len(v), voffset))
        voffset += len(v)

    # Write the .mo file
    with open(mo_file, 'wb') as f:
        # Write header
        f.write(struct.pack('<I', 0x950412de))  # Magic number
        f.write(struct.pack('<I', 0))           # Version
        f.write(struct.pack('<I', len(keys)))   # Number of entries
        f.write(struct.pack('<I', 7 * 4))       # Offset of key table
        f.write(struct.pack('<I', 7 * 4 + 8 * len(keys)))  # Offset of value table
        f.write(struct.pack('<I', 0))           # Hash table size
        f.write(struct.pack('<I', 0))           # Offset of hash table

        # Write key offsets and lengths
        for length, offset in keyoffsets:
            f.write(struct.pack('<I', length))
            f.write(struct.pack('<I', offset))

        # Write value offsets and lengths
        for length, offset in valueoffsets:
            f.write(struct.pack('<I', length))
            f.write(struct.pack('<I', offset))

        # Write keys
        for k in keys:
            f.write(k)

        # Write values
        for v in values:
            f.write(v)

    print(f"Successfully created {mo_file} with {len(keys)} entries")

if __name__ == "__main__":
    po_file = "locale/am/LC_MESSAGES/django.po"
    mo_file = "locale/am/LC_MESSAGES/django.mo"
    
    if os.path.exists(po_file):
        compile_po_to_mo(po_file, mo_file)
    else:
        print(f"Error: {po_file} not found")
