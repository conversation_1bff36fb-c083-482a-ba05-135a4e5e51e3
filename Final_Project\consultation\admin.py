from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    ConsultationType, PsychologistSpecialty, PsychologistAvailability,
    TimeSlot, Consultation, ConsultationSummary, ConsultationRating,
    ConsultationMessage
)

@admin.register(ConsultationType)
class ConsultationTypeAdmin(admin.ModelAdmin):
    """Consultation Type admin"""
    list_display = ('name', 'display_name', 'is_free', 'default_price', 'default_duration', 'is_active')
    list_filter = ('is_free', 'is_active', 'requires_approval')
    search_fields = ('name', 'display_name', 'description')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        (_('Basic Information'), {
            'fields': ('name', 'display_name', 'description', 'is_active')
        }),
        (_('Pricing & Duration'), {
            'fields': ('is_free', 'default_price', 'default_duration')
        }),
        (_('Restrictions'), {
            'fields': ('max_free_per_user', 'requires_approval')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['setup_default_consultation_types']

    def setup_default_consultation_types(self, request, queryset):
        """Set up default consultation types with free options"""
        from django.contrib import messages
        from decimal import Decimal

        consultation_types = [
            {
                'name': 'screening',
                'display_name': _('Mental Health Screening'),
                'description': _('Initial assessment to understand your mental health needs and concerns. This helps us determine the best path forward for your care.'),
                'is_free': True,
                'default_price': Decimal('0.00'),
                'default_duration': 30,
                'max_free_per_user': 2,
            },
            {
                'name': 'general_counseling',
                'display_name': _('General Counseling'),
                'description': _('Basic counseling session to discuss your concerns and receive emotional support and guidance.'),
                'is_free': False,
                'default_price': Decimal('400.00'),
                'default_duration': 45,
                'max_free_per_user': 0,
            },
            {
                'name': 'crisis_intervention',
                'display_name': _('Crisis Intervention'),
                'description': _('Immediate support for mental health crises. Available 24/7 for urgent situations.'),
                'is_free': False,
                'default_price': Decimal('600.00'),
                'default_duration': 60,
                'max_free_per_user': 0,
            },
            {
                'name': 'medication_advice',
                'display_name': _('Medication Consultation'),
                'description': _('Professional consultation about psychiatric medications, side effects, and treatment options.'),
                'is_free': False,
                'default_price': Decimal('500.00'),
                'default_duration': 30,
                'max_free_per_user': 0,
            },
            {
                'name': 'facility_recommendation',
                'display_name': _('Facility Referral'),
                'description': _('Get recommendations for specialized mental health facilities and treatment centers.'),
                'is_free': False,
                'default_price': Decimal('300.00'),
                'default_duration': 20,
                'max_free_per_user': 0,
            },
            {
                'name': 'other',
                'display_name': _('Other / Not Sure'),
                'description': _('If you\'re not sure what type of consultation you need, select this option and we\'ll help you find the right psychologist and consultation type.'),
                'is_free': True,
                'default_price': Decimal('0.00'),
                'default_duration': 30,
                'max_free_per_user': 1,
            },
        ]

        created_count = 0
        updated_count = 0
        for ct_data in consultation_types:
            consultation_type, created = ConsultationType.objects.get_or_create(
                name=ct_data['name'],
                defaults=ct_data
            )

            if created:
                created_count += 1
            else:
                # Update existing
                for key, value in ct_data.items():
                    if key != 'name':
                        setattr(consultation_type, key, value)
                consultation_type.save()
                updated_count += 1

        messages.success(request, f'Created {created_count} and updated {updated_count} consultation types!')

    setup_default_consultation_types.short_description = _('Set up default consultation types')

@admin.register(PsychologistSpecialty)
class PsychologistSpecialtyAdmin(admin.ModelAdmin):
    """Psychologist Specialty admin"""
    list_display = ('name', 'is_active', 'psychologist_count')
    list_filter = ('is_active',)
    search_fields = ('name', 'description')

    def psychologist_count(self, obj):
        return obj.psychologistprofile_set.count()
    psychologist_count.short_description = _('Psychologists')

@admin.register(PsychologistAvailability)
class PsychologistAvailabilityAdmin(admin.ModelAdmin):
    """Psychologist Availability admin"""
    list_display = ('psychologist', 'get_day_of_week_display', 'start_time', 'end_time', 'is_available')
    list_filter = ('day_of_week', 'is_available', 'psychologist__approval_status')
    search_fields = ('psychologist__user__username', 'psychologist__user__first_name', 'psychologist__user__last_name')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        (_('Schedule'), {
            'fields': ('psychologist', 'day_of_week', 'start_time', 'end_time', 'is_available')
        }),
        (_('Consultation Types'), {
            'fields': ('allowed_consultation_types',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(TimeSlot)
class TimeSlotAdmin(admin.ModelAdmin):
    """Time Slot admin"""
    list_display = ('psychologist', 'date', 'start_time', 'end_time', 'is_available', 'current_bookings', 'max_bookings')
    list_filter = ('is_available', 'date', 'psychologist__approval_status')
    search_fields = ('psychologist__user__username', 'psychologist__user__first_name', 'psychologist__user__last_name')
    readonly_fields = ('created_at', 'updated_at')
    date_hierarchy = 'date'

    fieldsets = (
        (_('Slot Information'), {
            'fields': ('psychologist', 'date', 'start_time', 'end_time', 'is_available')
        }),
        (_('Booking Limits'), {
            'fields': ('max_bookings', 'current_bookings')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(Consultation)
class ConsultationAdmin(admin.ModelAdmin):
    """Consultation admin with comprehensive management"""
    list_display = ('id', 'user', 'psychologist', 'consultation_type', 'scheduled_date', 'status', 'payment_status', 'price')
    list_filter = ('status', 'payment_status', 'consultation_type', 'scheduled_date', 'created_at')
    search_fields = ('user__username', 'psychologist__user__username', 'user__email', 'id')
    date_hierarchy = 'scheduled_date'
    readonly_fields = ('id', 'created_at', 'updated_at', 'actual_start_time', 'actual_end_time')

    fieldsets = (
        (_('Consultation Information'), {
            'fields': ('id', 'user', 'psychologist', 'consultation_type')
        }),
        (_('Scheduling'), {
            'fields': ('scheduled_date', 'scheduled_start_time', 'scheduled_end_time', 'actual_start_time', 'actual_end_time')
        }),
        (_('Status & Payment'), {
            'fields': ('status', 'payment_status', 'price', 'currency')
        }),
        (_('Payment Details'), {
            'fields': ('payment_reference', 'chapa_transaction_id'),
            'classes': ('collapse',)
        }),
        (_('Notes'), {
            'fields': ('user_notes', 'psychologist_notes')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_completed', 'mark_cancelled', 'export_consultations']

    def mark_completed(self, request, queryset):
        updated = queryset.update(status='completed')
        self.message_user(request, f'{updated} consultation(s) marked as completed.')
    mark_completed.short_description = _('Mark selected consultations as completed')

    def mark_cancelled(self, request, queryset):
        updated = queryset.update(status='cancelled')
        self.message_user(request, f'{updated} consultation(s) cancelled.')
    mark_cancelled.short_description = _('Cancel selected consultations')

    def export_consultations(self, request, queryset):
        # This would implement CSV export functionality
        self.message_user(request, 'Export functionality would be implemented here.')
    export_consultations.short_description = _('Export selected consultations')

@admin.register(ConsultationSummary)
class ConsultationSummaryAdmin(admin.ModelAdmin):
    """Consultation Summary admin"""
    list_display = ('consultation', 'get_psychologist', 'get_user', 'recommendations', 'risk_level', 'follow_up_needed')
    list_filter = ('recommendations', 'risk_level', 'follow_up_needed', 'created_at')
    search_fields = ('consultation__user__username', 'consultation__psychologist__user__username', 'presenting_concerns')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        (_('Consultation'), {
            'fields': ('consultation',)
        }),
        (_('Assessment'), {
            'fields': ('presenting_concerns', 'assessment_notes', 'diagnosis')
        }),
        (_('Recommendations'), {
            'fields': ('recommendations', 'recommendation_details', 'follow_up_needed', 'follow_up_timeframe')
        }),
        (_('Risk Assessment'), {
            'fields': ('risk_level', 'risk_notes')
        }),
        (_('Resources'), {
            'fields': ('resources_provided', 'report_file')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_psychologist(self, obj):
        return obj.consultation.psychologist.user.get_full_name()
    get_psychologist.short_description = _('Psychologist')

    def get_user(self, obj):
        return obj.consultation.user.get_full_name()
    get_user.short_description = _('User')

@admin.register(ConsultationRating)
class ConsultationRatingAdmin(admin.ModelAdmin):
    """Consultation Rating admin"""
    list_display = ('consultation', 'get_psychologist', 'overall_rating', 'psychologist_rating', 'would_recommend', 'created_at')
    list_filter = ('overall_rating', 'psychologist_rating', 'would_recommend', 'created_at')
    search_fields = ('consultation__user__username', 'consultation__psychologist__user__username', 'feedback')
    readonly_fields = ('created_at',)

    fieldsets = (
        (_('Consultation'), {
            'fields': ('consultation',)
        }),
        (_('Ratings'), {
            'fields': ('overall_rating', 'psychologist_rating', 'platform_rating', 'communication_rating', 'helpfulness_rating')
        }),
        (_('Feedback'), {
            'fields': ('feedback', 'would_recommend')
        }),
        (_('Timestamp'), {
            'fields': ('created_at',)
        }),
    )

    def get_psychologist(self, obj):
        return obj.consultation.psychologist.user.get_full_name()
    get_psychologist.short_description = _('Psychologist')

@admin.register(ConsultationMessage)
class ConsultationMessageAdmin(admin.ModelAdmin):
    """Consultation Message admin for moderation"""
    list_display = ('consultation', 'sender', 'message_type', 'created_at', 'message_preview')
    list_filter = ('message_type', 'is_system_message', 'created_at')
    search_fields = ('consultation__id', 'sender__username', 'message')
    readonly_fields = ('created_at',)

    fieldsets = (
        (_('Message Information'), {
            'fields': ('consultation', 'sender', 'message_type', 'is_system_message')
        }),
        (_('Content'), {
            'fields': ('message', 'attachment')
        }),
        (_('Timestamp'), {
            'fields': ('created_at',)
        }),
    )

    def message_preview(self, obj):
        return obj.message[:50] + '...' if len(obj.message) > 50 else obj.message
    message_preview.short_description = _('Message Preview')
