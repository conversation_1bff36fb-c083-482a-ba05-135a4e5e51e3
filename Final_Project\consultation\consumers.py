import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth.models import User
from django.utils import timezone
from .models import Consultation, ChatSession, ChatMessage, ChatSessionLog
from .utils import encrypt_message, decrypt_message


class ChatConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.consultation_id = self.scope['url_route']['kwargs']['consultation_id']
        self.room_group_name = f'chat_{self.consultation_id}'
        self.user = self.scope['user']
        
        # Check if user is authenticated
        if not self.user.is_authenticated:
            await self.close()
            return
        
        # Check if user can access this chat
        can_access = await self.check_chat_access()
        if not can_access:
            await self.close()
            return
        
        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # Log user joining
        await self.log_chat_action('joined')
        
        # Send connection confirmation
        await self.send(text_data=json.dumps({
            'type': 'connection_status',
            'message': 'Connected to chat',
            'user_type': await self.get_user_type()
        }))

    async def disconnect(self, close_code):
        # Log user leaving
        if hasattr(self, 'user') and self.user.is_authenticated:
            await self.log_chat_action('left')
        
        # Leave room group
        if hasattr(self, 'room_group_name'):
            await self.channel_layer.group_discard(
                self.room_group_name,
                self.channel_name
            )

    async def receive(self, text_data):
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type', 'chat_message')
            
            if message_type == 'chat_message':
                message = text_data_json['message']
                
                # Check if chat is currently active
                is_active = await self.is_chat_time_active()
                if not is_active:
                    await self.send(text_data=json.dumps({
                        'type': 'error',
                        'message': 'Chat is not available outside consultation hours'
                    }))
                    return
                
                # Save message to database
                chat_message = await self.save_message(message)
                
                # Send message to room group
                await self.channel_layer.group_send(
                    self.room_group_name,
                    {
                        'type': 'chat_message',
                        'message': message,
                        'sender': self.user.get_full_name() or self.user.username,
                        'sender_type': await self.get_user_type(),
                        'timestamp': chat_message.timestamp.isoformat(),
                        'message_id': chat_message.id
                    }
                )
                
                # Log message sent
                await self.log_chat_action('message_sent', {'message_id': chat_message.id})
                
        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid message format'
            }))
        except Exception as e:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Failed to send message'
            }))

    async def chat_message(self, event):
        # Send message to WebSocket
        await self.send(text_data=json.dumps({
            'type': 'chat_message',
            'message': event['message'],
            'sender': event['sender'],
            'sender_type': event['sender_type'],
            'timestamp': event['timestamp'],
            'message_id': event['message_id']
        }))

    @database_sync_to_async
    def check_chat_access(self):
        """Check if user can access this chat session"""
        try:
            consultation = Consultation.objects.get(id=self.consultation_id)
            chat_session, created = ChatSession.objects.get_or_create(
                consultation=consultation
            )
            return chat_session.can_access_chat(self.user)
        except Consultation.DoesNotExist:
            return False

    @database_sync_to_async
    def is_chat_time_active(self):
        """Check if current time is within consultation window"""
        try:
            consultation = Consultation.objects.get(id=self.consultation_id)
            chat_session = consultation.chat_session
            return chat_session.is_chat_time_active
        except (Consultation.DoesNotExist, ChatSession.DoesNotExist):
            return False

    @database_sync_to_async
    def get_user_type(self):
        """Get user type (patient or psychologist)"""
        try:
            consultation = Consultation.objects.get(id=self.consultation_id)
            if self.user == consultation.user:
                return 'patient'
            elif self.user == consultation.psychologist.user:
                return 'psychologist'
            else:
                return 'unknown'
        except Consultation.DoesNotExist:
            return 'unknown'

    @database_sync_to_async
    def save_message(self, message):
        """Save message to database with encryption"""
        consultation = Consultation.objects.get(id=self.consultation_id)
        chat_session, created = ChatSession.objects.get_or_create(
            consultation=consultation
        )

        # Encrypt message
        encrypted_message = encrypt_message(message)

        chat_message = ChatMessage.objects.create(
            chat_session=chat_session,
            sender=self.user,
            message=message,
            encrypted_message=encrypted_message,
            message_type='text'
        )

        # Create notification for the recipient
        from core.views import create_message_notification
        if self.user == consultation.user:
            # Patient sent message, notify psychologist
            recipient = consultation.psychologist.user
        else:
            # Psychologist sent message, notify patient
            recipient = consultation.user

        create_message_notification(
            recipient=recipient,
            sender=self.user,
            message_content=message,
            consultation=consultation
        )

        return chat_message

    @database_sync_to_async
    def log_chat_action(self, action, details=None):
        """Log chat session activity"""
        try:
            consultation = Consultation.objects.get(id=self.consultation_id)
            chat_session, created = ChatSession.objects.get_or_create(
                consultation=consultation
            )
            
            ChatSessionLog.objects.create(
                chat_session=chat_session,
                user=self.user,
                action=action,
                details=details or {}
            )
        except Exception:
            pass  # Don't fail if logging fails
