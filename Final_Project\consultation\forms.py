from django import forms
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Submit, Row, Column, Field, Div, HTML
from datetime import date, datetime, timedelta, time
from decimal import Decimal
from django.utils import timezone

from .models import (
    Consultation, ConsultationType, TimeSlot, ConsultationSummary, 
    ConsultationRating, PsychologistAvailability
)

class BookingForm(forms.ModelForm):
    """Form for booking a consultation"""

    consultation_type = forms.ModelChoiceField(
        queryset=ConsultationType.objects.filter(is_active=True),
        widget=forms.RadioSelect,
        empty_label=None
    )

    time_slot = forms.ModelChoiceField(
        queryset=TimeSlot.objects.none(),
        widget=forms.HiddenInput(),
        required=True,
        label=_('Selected Time Slot')
    )

    user_notes = forms.CharField(
        widget=forms.Textarea(attrs={
            'rows': 4,
            'class': 'form-control',
            'placeholder': _('Please describe your concerns or what you would like to discuss...')
        }),
        label=_('Your Concerns'),
        help_text=_('This information will help the psychologist prepare for your session.'),
        required=False
    )
    
    class Meta:
        model = Consultation
        fields = ['consultation_type', 'time_slot', 'user_notes']

    def __init__(self, *args, **kwargs):
        self.psychologist = kwargs.pop('psychologist', None)
        self.user = kwargs.pop('user', None)
        self.consultation_type_filter = kwargs.pop('consultation_type_filter', 'all')
        super().__init__(*args, **kwargs)

        # Set up time slot queryset if psychologist is provided
        if self.psychologist:
            from datetime import date
            from django.db import models
            today = date.today()

            self.fields['time_slot'].queryset = TimeSlot.objects.filter(
                psychologist=self.psychologist,
                date__gte=today,
                is_available=True,
                current_bookings__lt=models.F('max_bookings')
            ).order_by('date', 'start_time')

            # Filter consultation types based on type selection
            available_types = ConsultationType.objects.filter(is_active=True)

            if self.consultation_type_filter == 'free':
                available_types = available_types.filter(is_free=True)
            elif self.consultation_type_filter == 'paid':
                available_types = available_types.filter(is_free=False)

            # Check if psychologist offers free consultations
            if not self.psychologist.offers_free_consultation:
                available_types = available_types.filter(is_free=False)

            self.fields['consultation_type'].queryset = available_types

        self.helper = FormHelper()
        self.helper.layout = Layout(
            HTML('<h4>{% trans "Select Consultation Type" %}</h4>'),
            Field('consultation_type', css_class='consultation-type-radio'),
            HTML('<div id="consultation-type-info" class="alert alert-info mt-3" style="display:none;"></div>'),

            HTML('<h4 class="mt-4">{% trans "Select Available Time Slot" %}</h4>'),
            Field('time_slot'),

            HTML('<h4 class="mt-4">{% trans "Additional Information" %}</h4>'),
            Field('user_notes', css_class='form-group'),

            HTML('<div class="mt-4">'),
            Submit('submit', _('Book Consultation'), css_class='btn btn-primary btn-lg'),
            HTML('</div>')
        )
    
    def clean_consultation_type(self):
        consultation_type = self.cleaned_data.get('consultation_type')

        if consultation_type and consultation_type.is_free and self.user:
            # Check if user has booked any free consultation in the last 2 months
            from datetime import date, timedelta
            two_months_ago = date.today() - timedelta(days=60)

            recent_free_consultations = Consultation.objects.filter(
                user=self.user,
                payment_status='free',
                created_at__gte=two_months_ago
            ).exclude(status='cancelled').count()

            if recent_free_consultations > 0:
                raise ValidationError(
                    _('You can only book one free consultation every 2 months. Please wait before booking another free consultation or choose a paid consultation.')
                )

        return consultation_type
    
    def clean_time_slot(self):
        time_slot = self.cleaned_data.get('time_slot')

        if not time_slot:
            raise ValidationError(_('Please select a time slot.'))

        # Check if the time slot is still available
        if time_slot.current_bookings >= time_slot.max_bookings:
            raise ValidationError(_('This time slot is no longer available.'))

        # Check if the time slot is in the future
        from datetime import date, datetime, time as dt_time
        slot_datetime = datetime.combine(time_slot.date, time_slot.start_time)
        if slot_datetime <= datetime.now():
            raise ValidationError(_('Please select a future time slot.'))

        return time_slot

    def clean(self):
        cleaned_data = super().clean()
        consultation_type = cleaned_data.get('consultation_type')
        time_slot = cleaned_data.get('time_slot')

        # Consultation type is required
        if not consultation_type:
            raise ValidationError(_('Please select a consultation type.'))

        # Time slot is required
        if not time_slot:
            raise ValidationError(_('Please select a time slot.'))

        return cleaned_data

    def save(self, commit=True):
        consultation = super().save(commit=False)

        # Get the selected time slot
        time_slot = self.cleaned_data.get('time_slot')

        if time_slot:
            # Use the time slot's date and time
            consultation.scheduled_date = time_slot.date
            consultation.scheduled_start_time = time_slot.start_time
            consultation.scheduled_end_time = time_slot.end_time
        else:
            # This shouldn't happen due to validation, but provide fallback
            from datetime import date, timedelta, time
            consultation.scheduled_date = date.today() + timedelta(days=1)
            consultation.scheduled_start_time = time(9, 0)
            consultation.scheduled_end_time = time(10, 0)

        # Set consultation price based on type
        consultation_type = self.cleaned_data.get('consultation_type')
        if consultation_type:
            if consultation_type.is_free:
                consultation.price = Decimal('0.00')
                consultation.payment_status = 'free'
                consultation.status = 'confirmed'
            else:
                consultation.price = consultation_type.default_price
                consultation.payment_status = 'pending'
                consultation.status = 'pending'
        else:
            # Default values if no consultation type
            consultation.price = Decimal('0.00')
            consultation.payment_status = 'pending'
            consultation.status = 'pending'

        if commit:
            consultation.save()

            # Update time slot booking count
            if time_slot:
                time_slot.current_bookings += 1
                time_slot.save()

        return consultation

class TimeSlotForm(forms.ModelForm):
    """Form for manually creating time slots"""

    date = forms.DateField(
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'form-control',
            'min': timezone.now().date().isoformat()
        }),
        label=_('Date'),
        help_text=_('Select the date for this time slot')
    )

    start_time = forms.TimeField(
        widget=forms.TimeInput(attrs={
            'type': 'time',
            'class': 'form-control'
        }),
        label=_('Start Time'),
        help_text=_('When does this time slot start?')
    )

    end_time = forms.TimeField(
        widget=forms.TimeInput(attrs={
            'type': 'time',
            'class': 'form-control'
        }),
        label=_('End Time'),
        help_text=_('When does this time slot end?')
    )

    max_bookings = forms.IntegerField(
        min_value=1,
        max_value=10,
        initial=1,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'min': '1',
            'max': '10'
        }),
        label=_('Maximum Bookings'),
        help_text=_('How many patients can book this slot?')
    )

    is_available = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label=_('Available for Booking'),
        help_text=_('Uncheck to temporarily disable this slot')
    )

    class Meta:
        model = TimeSlot
        fields = ['date', 'start_time', 'end_time', 'max_bookings', 'is_available']

    def __init__(self, *args, **kwargs):
        self.psychologist = kwargs.pop('psychologist', None)
        super().__init__(*args, **kwargs)

    def clean(self):
        cleaned_data = super().clean()
        date = cleaned_data.get('date')
        start_time = cleaned_data.get('start_time')
        end_time = cleaned_data.get('end_time')

        if not all([date, start_time, end_time]):
            return cleaned_data

        # Validate that end time is after start time
        if start_time >= end_time:
            raise ValidationError(_('End time must be after start time.'))

        # Validate that the date is in the future
        if date <= timezone.now().date():
            raise ValidationError(_('Please select a future date.'))

        # Check for overlapping time slots on the same date
        if self.psychologist:
            overlapping_slots = TimeSlot.objects.filter(
                psychologist=self.psychologist,
                date=date,
                start_time__lt=end_time,
                end_time__gt=start_time
            )

            # Exclude current instance if editing
            if self.instance.pk:
                overlapping_slots = overlapping_slots.exclude(pk=self.instance.pk)

            if overlapping_slots.exists():
                raise ValidationError(
                    _('This time slot overlaps with an existing slot on the same date.')
                )

        return cleaned_data

    def save(self, commit=True):
        time_slot = super().save(commit=False)

        if self.psychologist:
            time_slot.psychologist = self.psychologist

        # Initialize current_bookings if not set
        if time_slot.current_bookings is None:
            time_slot.current_bookings = 0

        if commit:
            time_slot.save()

        return time_slot

class ConsultationSummaryForm(forms.ModelForm):
    """Form for psychologist to fill consultation summary"""
    
    class Meta:
        model = ConsultationSummary
        fields = [
            'presenting_concerns', 'assessment_notes', 'diagnosis',
            'recommendations', 'recommendation_details', 'follow_up_needed',
            'follow_up_timeframe', 'resources_provided', 'report_file',
            'risk_level', 'risk_notes'
        ]
        widgets = {
            'presenting_concerns': forms.Textarea(attrs={'rows': 4}),
            'assessment_notes': forms.Textarea(attrs={'rows': 5}),
            'diagnosis': forms.Textarea(attrs={'rows': 3}),
            'recommendation_details': forms.Textarea(attrs={'rows': 4}),
            'resources_provided': forms.Textarea(attrs={'rows': 3}),
            'risk_notes': forms.Textarea(attrs={'rows': 3}),
            'follow_up_timeframe': forms.TextInput(attrs={
                'placeholder': _('e.g., 2 weeks, 1 month')
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.layout = Layout(
            HTML('<h4>{% trans "Session Assessment" %}</h4>'),
            Field('presenting_concerns'),
            Field('assessment_notes'),
            Field('diagnosis'),
            
            HTML('<h4 class="mt-4">{% trans "Recommendations" %}</h4>'),
            Row(
                Column('recommendations', css_class='form-group col-md-6'),
                Column('risk_level', css_class='form-group col-md-6'),
            ),
            Field('recommendation_details'),
            
            HTML('<h4 class="mt-4">{% trans "Follow-up" %}</h4>'),
            Row(
                Column('follow_up_needed', css_class='form-group col-md-6'),
                Column('follow_up_timeframe', css_class='form-group col-md-6'),
            ),
            
            HTML('<h4 class="mt-4">{% trans "Additional Information" %}</h4>'),
            Field('resources_provided'),
            Field('risk_notes'),
            Field('report_file'),
            
            Submit('submit', _('Save Summary'), css_class='btn btn-primary btn-lg mt-4')
        )

class ConsultationRatingForm(forms.ModelForm):
    """Form for users to rate consultations"""
    
    class Meta:
        model = ConsultationRating
        fields = [
            'overall_rating', 'psychologist_rating', 'platform_rating',
            'communication_rating', 'helpfulness_rating', 'feedback',
            'would_recommend'
        ]
        widgets = {
            'overall_rating': forms.RadioSelect(choices=[(i, i) for i in range(1, 6)]),
            'psychologist_rating': forms.RadioSelect(choices=[(i, i) for i in range(1, 6)]),
            'platform_rating': forms.RadioSelect(choices=[(i, i) for i in range(1, 6)]),
            'communication_rating': forms.RadioSelect(choices=[(i, i) for i in range(1, 6)]),
            'helpfulness_rating': forms.RadioSelect(choices=[(i, i) for i in range(1, 6)]),
            'feedback': forms.Textarea(attrs={'rows': 4}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.layout = Layout(
            HTML('<h4>{% trans "Rate Your Experience" %}</h4>'),
            
            HTML('<div class="rating-section">'),
            HTML('<h5>{% trans "Overall Satisfaction" %}</h5>'),
            Field('overall_rating', css_class='rating-stars'),
            HTML('</div>'),
            
            HTML('<div class="rating-section mt-4">'),
            HTML('<h5>{% trans "Psychologist Performance" %}</h5>'),
            Field('psychologist_rating', css_class='rating-stars'),
            HTML('</div>'),
            
            HTML('<div class="rating-section mt-4">'),
            HTML('<h5>{% trans "Communication Quality" %}</h5>'),
            Field('communication_rating', css_class='rating-stars'),
            HTML('</div>'),
            
            HTML('<div class="rating-section mt-4">'),
            HTML('<h5>{% trans "How Helpful Was the Session" %}</h5>'),
            Field('helpfulness_rating', css_class='rating-stars'),
            HTML('</div>'),
            
            HTML('<div class="rating-section mt-4">'),
            HTML('<h5>{% trans "Platform Experience" %}</h5>'),
            Field('platform_rating', css_class='rating-stars'),
            HTML('</div>'),
            
            HTML('<h4 class="mt-4">{% trans "Additional Feedback" %}</h4>'),
            Field('feedback'),
            Field('would_recommend'),
            
            Submit('submit', _('Submit Rating'), css_class='btn btn-primary btn-lg mt-4')
        )

class PsychologistAvailabilityForm(forms.ModelForm):
    """Form for psychologists to set their availability"""
    
    class Meta:
        model = PsychologistAvailability
        fields = [
            'day_of_week', 'start_time', 'end_time', 'is_available',
            'allowed_consultation_types'
        ]
        widgets = {
            'start_time': forms.TimeInput(attrs={'type': 'time'}),
            'end_time': forms.TimeInput(attrs={'type': 'time'}),
            'allowed_consultation_types': forms.CheckboxSelectMultiple(),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.layout = Layout(
            Row(
                Column('day_of_week', css_class='form-group col-md-4'),
                Column('start_time', css_class='form-group col-md-4'),
                Column('end_time', css_class='form-group col-md-4'),
            ),
            Field('is_available'),
            Field('allowed_consultation_types'),
            Submit('submit', _('Save Availability'), css_class='btn btn-primary')
        )
    
    def clean(self):
        cleaned_data = super().clean()
        start_time = cleaned_data.get('start_time')
        end_time = cleaned_data.get('end_time')
        
        if start_time and end_time and start_time >= end_time:
            raise ValidationError(_('End time must be after start time.'))
        
        return cleaned_data
