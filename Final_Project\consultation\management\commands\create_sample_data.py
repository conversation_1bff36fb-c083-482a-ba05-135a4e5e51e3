from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import date, time, timedelta
from decimal import Decimal

from accounts.models import UserProfile, PsychologistProfile
from consultation.models import (
    ConsultationType, PsychologistSpecialty, PsychologistAvailability, 
    TimeSlot, Consultation
)

class Command(BaseCommand):
    help = 'Create sample data for the consultation system'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample data...')
        
        # Create consultation types
        self.create_consultation_types()
        
        # Create specialties
        self.create_specialties()
        
        # Create sample psychologists
        self.create_sample_psychologists()
        
        # Create sample users
        self.create_sample_users()
        
        # Create time slots
        self.create_time_slots()
        
        self.stdout.write(
            self.style.SUCCESS('Successfully created sample data!')
        )

    def create_consultation_types(self):
        consultation_types = [
            {
                'name': 'screening',
                'display_name': 'Mental Health Screening',
                'description': 'Initial assessment to understand your mental health needs and concerns.',
                'is_free': True,
                'default_price': Decimal('0.00'),
                'default_duration': 30,
                'max_free_per_user': 1,
            },
            {
                'name': 'medication_advice',
                'display_name': 'Medication Consultation',
                'description': 'Professional advice on mental health medications and treatment options.',
                'is_free': False,
                'default_price': Decimal('500.00'),
                'default_duration': 45,
                'max_free_per_user': 0,
            },
            {
                'name': 'facility_recommendation',
                'display_name': 'Facility Referral',
                'description': 'Recommendations for specialized mental health facilities and services.',
                'is_free': False,
                'default_price': Decimal('300.00'),
                'default_duration': 30,
                'max_free_per_user': 0,
            },
            {
                'name': 'general_counseling',
                'display_name': 'General Counseling',
                'description': 'General psychological counseling and therapy sessions.',
                'is_free': False,
                'default_price': Decimal('800.00'),
                'default_duration': 60,
                'max_free_per_user': 0,
            },
        ]
        
        for type_data in consultation_types:
            consultation_type, created = ConsultationType.objects.get_or_create(
                name=type_data['name'],
                defaults=type_data
            )
            if created:
                self.stdout.write(f'Created consultation type: {consultation_type.display_name}')

    def create_specialties(self):
        specialties = [
            'Anxiety Disorders',
            'Depression Treatment',
            'Trauma Therapy',
            'Child Psychology',
            'Adolescent Psychology',
            'Family Therapy',
            'Cognitive Behavioral Therapy',
            'Addiction Counseling',
            'Relationship Counseling',
            'Grief Counseling',
            'Stress Management',
            'PTSD Treatment',
        ]
        
        for specialty_name in specialties:
            specialty, created = PsychologistSpecialty.objects.get_or_create(
                name=specialty_name,
                defaults={'description': f'Specialized treatment for {specialty_name.lower()}'}
            )
            if created:
                self.stdout.write(f'Created specialty: {specialty.name}')

    def create_sample_psychologists(self):
        psychologists_data = [
            {
                'username': 'dr_sarah_jones',
                'first_name': 'Sarah',
                'last_name': 'Jones',
                'email': '<EMAIL>',
                'license_number': 'PSY001ET',
                'years_of_experience': 8,
                'education': 'PhD in Clinical Psychology, Addis Ababa University',
                'consultation_fee': Decimal('600.00'),
                'specialties': ['Anxiety Disorders', 'Depression Treatment', 'Cognitive Behavioral Therapy'],
            },
            {
                'username': 'dr_michael_tadesse',
                'first_name': 'Michael',
                'last_name': 'Tadesse',
                'email': '<EMAIL>',
                'license_number': 'PSY002ET',
                'years_of_experience': 12,
                'education': 'PhD in Child Psychology, University of Cape Town',
                'consultation_fee': Decimal('800.00'),
                'specialties': ['Child Psychology', 'Adolescent Psychology', 'Family Therapy'],
            },
            {
                'username': 'dr_helen_abraham',
                'first_name': 'Helen',
                'last_name': 'Abraham',
                'email': '<EMAIL>',
                'license_number': 'PSY003ET',
                'years_of_experience': 6,
                'education': 'MSc in Counseling Psychology, Jimma University',
                'consultation_fee': Decimal('500.00'),
                'specialties': ['Trauma Therapy', 'PTSD Treatment', 'Grief Counseling'],
            },
        ]
        
        for psych_data in psychologists_data:
            # Create user
            user, created = User.objects.get_or_create(
                username=psych_data['username'],
                defaults={
                    'first_name': psych_data['first_name'],
                    'last_name': psych_data['last_name'],
                    'email': psych_data['email'],
                    'is_active': True,
                }
            )
            
            if created:
                user.set_password('password123')
                user.save()
                
                # Create user profile
                UserProfile.objects.create(
                    user=user,
                    is_psychologist=True,
                    is_verified=True,
                )
                
                # Create psychologist profile
                psych_profile = PsychologistProfile.objects.create(
                    user=user,
                    license_number=psych_data['license_number'],
                    specializations=', '.join(psych_data['specialties']),
                    years_of_experience=psych_data['years_of_experience'],
                    education=psych_data['education'],
                    consultation_fee=psych_data['consultation_fee'],
                    offers_free_consultation=True,
                    available_languages='en,am',
                    is_available=True,
                    approval_status='approved',
                    approval_date=timezone.now(),
                )
                
                # Add specialties
                for specialty_name in psych_data['specialties']:
                    try:
                        specialty = PsychologistSpecialty.objects.get(name=specialty_name)
                        psych_profile.specialties.add(specialty)
                    except PsychologistSpecialty.DoesNotExist:
                        pass
                
                # Create availability schedule (Monday to Friday, 9 AM to 5 PM)
                for day in range(1, 6):  # Monday to Friday
                    PsychologistAvailability.objects.create(
                        psychologist=psych_profile,
                        day_of_week=day,
                        start_time=time(9, 0),
                        end_time=time(17, 0),
                        is_available=True,
                    )
                
                self.stdout.write(f'Created psychologist: Dr. {user.get_full_name()}')

    def create_sample_users(self):
        users_data = [
            {
                'username': 'john_doe',
                'first_name': 'John',
                'last_name': 'Doe',
                'email': '<EMAIL>',
            },
            {
                'username': 'jane_smith',
                'first_name': 'Jane',
                'last_name': 'Smith',
                'email': '<EMAIL>',
            },
        ]
        
        for user_data in users_data:
            user, created = User.objects.get_or_create(
                username=user_data['username'],
                defaults={
                    'first_name': user_data['first_name'],
                    'last_name': user_data['last_name'],
                    'email': user_data['email'],
                    'is_active': True,
                }
            )
            
            if created:
                user.set_password('password123')
                user.save()
                
                UserProfile.objects.create(
                    user=user,
                    birth_date=date(1990, 1, 1),
                    gender='M' if 'john' in user_data['username'] else 'F',
                )
                
                self.stdout.write(f'Created user: {user.get_full_name()}')

    def create_time_slots(self):
        psychologists = PsychologistProfile.objects.filter(approval_status='approved')
        
        # Create time slots for the next 30 days
        start_date = date.today()
        end_date = start_date + timedelta(days=30)
        
        for psychologist in psychologists:
            current_date = start_date
            
            while current_date <= end_date:
                # Skip weekends
                if current_date.weekday() < 5:  # Monday = 0, Friday = 4
                    # Create hourly slots from 9 AM to 5 PM
                    for hour in range(9, 17):
                        start_time = time(hour, 0)
                        end_time = time(hour + 1, 0)
                        
                        TimeSlot.objects.get_or_create(
                            psychologist=psychologist,
                            date=current_date,
                            start_time=start_time,
                            end_time=end_time,
                            defaults={
                                'is_available': True,
                                'max_bookings': 1,
                                'current_bookings': 0,
                            }
                        )
                
                current_date += timedelta(days=1)
        
        self.stdout.write('Created time slots for all psychologists')
