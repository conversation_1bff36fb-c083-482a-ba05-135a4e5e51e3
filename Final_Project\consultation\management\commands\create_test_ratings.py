from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from consultation.models import Consultation, ConsultationRating
from accounts.models import PsychologistProfile
import random


class Command(BaseCommand):
    help = 'Create test ratings for development'

    def handle(self, *args, **options):
        # Get all psychologists
        psychologists = PsychologistProfile.objects.all()
        
        if not psychologists.exists():
            self.stdout.write(
                self.style.ERROR('No psychologists found in the system')
            )
            return

        # Create test ratings for each psychologist
        for psychologist in psychologists:
            self.stdout.write(f'Creating test ratings for {psychologist.user.get_full_name()}...')
            
            # Create 3-5 test ratings per psychologist
            num_ratings = random.randint(3, 5)
            
            for i in range(num_ratings):
                # Create a fake consultation if needed
                consultation = Consultation.objects.create(
                    user=User.objects.first(),  # Use first user as patient
                    psychologist=psychologist,
                    consultation_type_id=1,  # Assume first consultation type exists
                    scheduled_date='2025-07-28',
                    scheduled_start_time='10:00:00',
                    scheduled_end_time='11:00:00',
                    status='completed'
                )
                
                # Create rating
                overall_rating = random.randint(3, 5)  # Mostly positive ratings
                rating = ConsultationRating.objects.create(
                    consultation=consultation,
                    overall_rating=overall_rating,
                    psychologist_rating=random.randint(3, 5),
                    platform_rating=random.randint(3, 5),
                    communication_rating=random.randint(3, 5),
                    helpfulness_rating=random.randint(3, 5),
                    feedback=self.get_sample_feedback(overall_rating),
                    would_recommend=overall_rating >= 4
                )
                
                self.stdout.write(f'  Created rating: {rating.overall_rating}/5')
            
            # Refresh psychologist stats
            psychologist.refresh_from_db()
            self.stdout.write(
                self.style.SUCCESS(
                    f'  Final stats: {psychologist.average_rating}/5 avg, {psychologist.total_ratings} total'
                )
            )

        self.stdout.write(
            self.style.SUCCESS('Successfully created test ratings!')
        )

    def get_sample_feedback(self, rating):
        """Get sample feedback based on rating"""
        if rating == 5:
            feedbacks = [
                "Excellent consultation! Very helpful and professional.",
                "Outstanding service. The psychologist was very understanding and provided great insights.",
                "Highly recommend! The session was exactly what I needed.",
                "Perfect experience. Professional, caring, and effective.",
                "Amazing consultation. I feel much better after the session."
            ]
        elif rating == 4:
            feedbacks = [
                "Very good consultation. The psychologist was helpful and professional.",
                "Good experience overall. Some useful insights provided.",
                "Solid consultation. Would recommend to others.",
                "Professional service with good advice.",
                "Helpful session with practical suggestions."
            ]
        else:
            feedbacks = [
                "Decent consultation. Some helpful points discussed.",
                "Average experience. Could be improved.",
                "Okay session. Got some useful information.",
                "Fair consultation. Met basic expectations.",
                "Reasonable service. Some areas for improvement."
            ]
        
        return random.choice(feedbacks)
