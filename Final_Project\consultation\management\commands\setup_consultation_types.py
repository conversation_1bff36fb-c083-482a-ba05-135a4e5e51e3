from django.core.management.base import BaseCommand
from django.utils.translation import gettext as _
from consultation.models import ConsultationType
from decimal import Decimal


class Command(BaseCommand):
    help = 'Set up consultation types with free options'

    def handle(self, *args, **options):
        consultation_types = [
            {
                'name': 'screening',
                'display_name': _('Mental Health Screening'),
                'description': _('Initial assessment to understand your mental health needs and concerns. This helps us determine the best path forward for your care.'),
                'is_free': True,
                'default_price': Decimal('0.00'),
                'default_duration': 30,
                'max_free_per_user': 2,  # 2 free sessions per user
            },
            {
                'name': 'general_counseling',
                'display_name': _('General Counseling'),
                'description': _('Basic counseling session to discuss your concerns and receive emotional support and guidance.'),
                'is_free': False,
                'default_price': Decimal('400.00'),  # Updated price
                'default_duration': 45,
                'max_free_per_user': 0,
            },
            {
                'name': 'crisis_intervention',
                'display_name': _('Crisis Intervention'),
                'description': _('Immediate support for mental health crises. Available 24/7 for urgent situations.'),
                'is_free': False,
                'default_price': Decimal('600.00'),  # Paid consultation
                'default_duration': 60,
                'max_free_per_user': 0,
            },
            {
                'name': 'medication_advice',
                'display_name': _('Medication Consultation'),
                'description': _('Professional consultation about psychiatric medications, side effects, and treatment options.'),
                'is_free': False,
                'default_price': Decimal('500.00'),
                'default_duration': 30,
                'max_free_per_user': 0,
            },
            {
                'name': 'facility_recommendation',
                'display_name': _('Facility Referral'),
                'description': _('Get recommendations for specialized mental health facilities and treatment centers.'),
                'is_free': False,
                'default_price': Decimal('300.00'),
                'default_duration': 20,
                'max_free_per_user': 0,
            },
            {
                'name': 'other',
                'display_name': _('Other / Not Sure'),
                'description': _('If you\'re not sure what type of consultation you need, select this option and we\'ll help you find the right psychologist and consultation type.'),
                'is_free': True,
                'default_price': Decimal('0.00'),
                'default_duration': 30,
                'max_free_per_user': 1,  # 1 free session per user
            },
        ]

        for ct_data in consultation_types:
            consultation_type, created = ConsultationType.objects.get_or_create(
                name=ct_data['name'],
                defaults=ct_data
            )
            
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'Created consultation type: {consultation_type.display_name}')
                )
            else:
                # Update existing
                for key, value in ct_data.items():
                    if key != 'name':
                        setattr(consultation_type, key, value)
                consultation_type.save()
                self.stdout.write(
                    self.style.WARNING(f'Updated consultation type: {consultation_type.display_name}')
                )

        self.stdout.write(
            self.style.SUCCESS('Successfully set up consultation types!')
        )
