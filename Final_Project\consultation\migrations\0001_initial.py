# Generated by Django 5.2.4 on 2025-07-25 14:25

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ConsultationType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(choices=[('screening', 'Mental Health Screening'), ('medication_advice', 'Further Medication Advice'), ('facility_recommendation', 'Recommendation to Another Facility'), ('general_counseling', 'General Counseling'), ('crisis_intervention', 'Crisis Intervention')], max_length=100, unique=True)),
                ('display_name', models.CharField(max_length=200)),
                ('description', models.TextField()),
                ('is_free', models.<PERSON><PERSON>anField(default=False, help_text='Is this consultation type free?')),
                ('default_price', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('default_duration', models.PositiveIntegerField(default=30, help_text='Duration in minutes')),
                ('is_active', models.BooleanField(default=True)),
                ('max_free_per_user', models.PositiveIntegerField(default=1, help_text='Maximum free consultations of this type per user')),
                ('requires_approval', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Consultation Type',
                'verbose_name_plural': 'Consultation Types',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='PsychologistSpecialty',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Psychologist Specialty',
                'verbose_name_plural': 'Psychologist Specialties',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Consultation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('scheduled_date', models.DateField()),
                ('scheduled_start_time', models.TimeField()),
                ('scheduled_end_time', models.TimeField()),
                ('actual_start_time', models.DateTimeField(blank=True, null=True)),
                ('actual_end_time', models.DateTimeField(blank=True, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('confirmed', 'Confirmed'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('no_show', 'No Show')], default='pending', max_length=20)),
                ('payment_status', models.CharField(choices=[('pending', 'Pending'), ('paid', 'Paid'), ('failed', 'Failed'), ('refunded', 'Refunded'), ('free', 'Free')], default='pending', max_length=20)),
                ('price', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('currency', models.CharField(default='ETB', max_length=3)),
                ('user_notes', models.TextField(blank=True, help_text="User's notes about their concerns")),
                ('psychologist_notes', models.TextField(blank=True, help_text="Psychologist's private notes")),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('payment_reference', models.CharField(blank=True, max_length=100)),
                ('chapa_transaction_id', models.CharField(blank=True, max_length=100)),
                ('psychologist', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='consultations', to='accounts.psychologistprofile')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='consultations', to=settings.AUTH_USER_MODEL)),
                ('consultation_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='consultation.consultationtype')),
            ],
            options={
                'verbose_name': 'Consultation',
                'verbose_name_plural': 'Consultations',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ConsultationMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.TextField()),
                ('attachment', models.FileField(blank=True, null=True, upload_to='consultation_attachments/')),
                ('is_system_message', models.BooleanField(default=False)),
                ('message_type', models.CharField(choices=[('text', 'Text Message'), ('file', 'File Attachment'), ('system', 'System Message'), ('resource', 'Resource Share')], default='text', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('consultation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='consultation.consultation')),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Consultation Message',
                'verbose_name_plural': 'Consultation Messages',
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='ConsultationRating',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('overall_rating', models.PositiveIntegerField(help_text='Overall satisfaction (1-5)', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('psychologist_rating', models.PositiveIntegerField(help_text='Psychologist performance (1-5)', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('platform_rating', models.PositiveIntegerField(help_text='Platform experience (1-5)', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('feedback', models.TextField(blank=True, help_text='Written feedback')),
                ('would_recommend', models.BooleanField(default=True)),
                ('communication_rating', models.PositiveIntegerField(help_text='Communication quality (1-5)', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('helpfulness_rating', models.PositiveIntegerField(help_text='How helpful was the session (1-5)', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('consultation', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='rating', to='consultation.consultation')),
            ],
            options={
                'verbose_name': 'Consultation Rating',
                'verbose_name_plural': 'Consultation Ratings',
            },
        ),
        migrations.CreateModel(
            name='ConsultationSummary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('presenting_concerns', models.TextField(help_text='Main concerns presented by the client')),
                ('assessment_notes', models.TextField(help_text='Clinical assessment and observations')),
                ('diagnosis', models.TextField(blank=True, help_text='Preliminary diagnosis or screening results')),
                ('recommendations', models.CharField(choices=[('continue_therapy', 'Continue Regular Therapy'), ('medication_referral', 'Medication/Psychiatric Referral'), ('hospital_referral', 'Hospital/Inpatient Referral'), ('specialist_referral', 'Specialist Referral'), ('self_care', 'Self-Care and Monitoring'), ('follow_up', 'Follow-up Session Recommended'), ('no_further_action', 'No Further Action Needed')], help_text='Primary recommendation', max_length=50)),
                ('recommendation_details', models.TextField(help_text='Detailed recommendation and next steps')),
                ('follow_up_needed', models.BooleanField(default=False)),
                ('follow_up_timeframe', models.CharField(blank=True, help_text="When should follow-up occur? (e.g., '2 weeks', '1 month')", max_length=100)),
                ('resources_provided', models.TextField(blank=True, help_text='Educational materials or resources shared with client')),
                ('report_file', models.FileField(blank=True, help_text='Optional PDF report', null=True, upload_to='consultation_reports/')),
                ('risk_level', models.CharField(choices=[('low', 'Low Risk'), ('moderate', 'Moderate Risk'), ('high', 'High Risk'), ('crisis', 'Crisis - Immediate Intervention Needed')], default='low', max_length=20)),
                ('risk_notes', models.TextField(blank=True, help_text='Risk assessment details')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('consultation', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='summary', to='consultation.consultation')),
            ],
            options={
                'verbose_name': 'Consultation Summary',
                'verbose_name_plural': 'Consultation Summaries',
            },
        ),
        migrations.CreateModel(
            name='PsychologistAvailability',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('day_of_week', models.PositiveIntegerField(choices=[(1, 'Monday'), (2, 'Tuesday'), (3, 'Wednesday'), (4, 'Thursday'), (5, 'Friday'), (6, 'Saturday'), (7, 'Sunday')])),
                ('start_time', models.TimeField()),
                ('end_time', models.TimeField()),
                ('is_available', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('allowed_consultation_types', models.ManyToManyField(blank=True, help_text='Leave empty to allow all consultation types', to='consultation.consultationtype')),
                ('psychologist', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='availability_schedule', to='accounts.psychologistprofile')),
            ],
            options={
                'verbose_name': 'Psychologist Availability',
                'verbose_name_plural': 'Psychologist Availabilities',
                'ordering': ['day_of_week', 'start_time'],
                'unique_together': {('psychologist', 'day_of_week', 'start_time')},
            },
        ),
        migrations.CreateModel(
            name='TimeSlot',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('start_time', models.TimeField()),
                ('end_time', models.TimeField()),
                ('is_available', models.BooleanField(default=True)),
                ('max_bookings', models.PositiveIntegerField(default=1)),
                ('current_bookings', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('psychologist', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='time_slots', to='accounts.psychologistprofile')),
            ],
            options={
                'verbose_name': 'Time Slot',
                'verbose_name_plural': 'Time Slots',
                'ordering': ['date', 'start_time'],
                'unique_together': {('psychologist', 'date', 'start_time')},
            },
        ),
    ]
