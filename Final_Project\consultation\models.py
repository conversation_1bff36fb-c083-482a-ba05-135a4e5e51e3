from django.db import models
from django.contrib.auth.models import User
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from decimal import Decimal
import uuid

class ConsultationType(models.Model):
    """Types of consultations available"""
    CONSULTATION_TYPES = [
        ('screening', _('Mental Health Screening')),
        ('medication_advice', _('Further Medication Advice')),
        ('facility_recommendation', _('Recommendation to Another Facility')),
        ('general_counseling', _('General Counseling')),
        ('crisis_intervention', _('Crisis Intervention')),
        ('other', _('Other / Not Sure')),
    ]

    name = models.CharField(max_length=100, choices=CONSULTATION_TYPES, unique=True)
    display_name = models.CharField(max_length=200)
    description = models.TextField()
    is_free = models.BooleanField(default=False, help_text=_("Is this consultation type free?"))
    default_price = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    default_duration = models.PositiveIntegerField(default=30, help_text=_("Duration in minutes"))
    is_active = models.BooleanField(default=True)

    # Restrictions
    max_free_per_user = models.PositiveIntegerField(
        default=1,
        help_text=_("Maximum free consultations of this type per user")
    )
    requires_approval = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("Consultation Type")
        verbose_name_plural = _("Consultation Types")
        ordering = ['name']

    def __str__(self):
        return self.display_name

class PsychologistAvailability(models.Model):
    """Psychologist availability schedule"""
    WEEKDAYS = [
        (1, _('Monday')),
        (2, _('Tuesday')),
        (3, _('Wednesday')),
        (4, _('Thursday')),
        (5, _('Friday')),
        (6, _('Saturday')),
        (7, _('Sunday')),
    ]

    psychologist = models.ForeignKey(
        'accounts.PsychologistProfile',
        on_delete=models.CASCADE,
        related_name='availability_schedule'
    )
    day_of_week = models.PositiveIntegerField(choices=WEEKDAYS)
    start_time = models.TimeField()
    end_time = models.TimeField()
    is_available = models.BooleanField(default=True)

    # Consultation type restrictions
    allowed_consultation_types = models.ManyToManyField(
        ConsultationType,
        blank=True,
        help_text=_("Leave empty to allow all consultation types")
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("Psychologist Availability")
        verbose_name_plural = _("Psychologist Availabilities")
        unique_together = ['psychologist', 'day_of_week', 'start_time']
        ordering = ['day_of_week', 'start_time']

    def __str__(self):
        return f"{self.psychologist} - {self.get_day_of_week_display()} {self.start_time}-{self.end_time}"

class TimeSlot(models.Model):
    """Individual time slots for booking"""
    psychologist = models.ForeignKey(
        'accounts.PsychologistProfile',
        on_delete=models.CASCADE,
        related_name='time_slots'
    )
    date = models.DateField()
    start_time = models.TimeField()
    end_time = models.TimeField()
    is_available = models.BooleanField(default=True)

    # Slot restrictions
    max_bookings = models.PositiveIntegerField(default=1)
    current_bookings = models.PositiveIntegerField(default=0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("Time Slot")
        verbose_name_plural = _("Time Slots")
        unique_together = ['psychologist', 'date', 'start_time']
        ordering = ['date', 'start_time']

    def __str__(self):
        return f"{self.psychologist} - {self.date} {self.start_time}-{self.end_time}"

    @property
    def is_fully_booked(self):
        return self.current_bookings >= self.max_bookings

    @property
    def is_past(self):
        from datetime import datetime, time
        slot_datetime = datetime.combine(self.date, self.start_time)
        return slot_datetime < timezone.now()

class Consultation(models.Model):
    """Main consultation booking model"""
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('confirmed', _('Confirmed')),
        ('in_progress', _('In Progress')),
        ('completed', _('Completed')),
        ('cancelled', _('Cancelled')),
        ('no_show', _('No Show')),
    ]

    PAYMENT_STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('paid', _('Paid')),
        ('failed', _('Failed')),
        ('refunded', _('Refunded')),
        ('free', _('Free')),
    ]

    # Basic Information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='consultations')
    psychologist = models.ForeignKey(
        'accounts.PsychologistProfile',
        on_delete=models.CASCADE,
        related_name='consultations'
    )
    consultation_type = models.ForeignKey(ConsultationType, on_delete=models.CASCADE)

    # Scheduling
    scheduled_date = models.DateField()
    scheduled_start_time = models.TimeField()
    scheduled_end_time = models.TimeField()
    actual_start_time = models.DateTimeField(null=True, blank=True)
    actual_end_time = models.DateTimeField(null=True, blank=True)

    # Status and Payment
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    payment_status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending')

    # Pricing
    price = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    currency = models.CharField(max_length=3, default='ETB')

    # Additional Information
    user_notes = models.TextField(blank=True, help_text=_("User's notes about their concerns"))
    psychologist_notes = models.TextField(blank=True, help_text=_("Psychologist's private notes"))

    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Payment tracking
    payment_reference = models.CharField(max_length=100, blank=True)
    chapa_transaction_id = models.CharField(max_length=100, blank=True)

    class Meta:
        verbose_name = _("Consultation")
        verbose_name_plural = _("Consultations")
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.psychologist} - {self.scheduled_date}"

    @classmethod
    def auto_assign_psychologist(cls, consultation_type=None, user_location=None):
        """Auto-assign a psychologist based on consultation type and availability"""
        from accounts.models import PsychologistProfile
        from django.db.models import Count, Q

        # Get available psychologists
        available_psychologists = PsychologistProfile.objects.filter(
            is_available=True,
            approval_status='approved'
        ).annotate(
            current_consultations=Count('consultations', filter=Q(
                consultations__status__in=['pending', 'confirmed'],
                consultations__scheduled_date__gte=timezone.now().date()
            ))
        ).order_by('current_consultations', '?')  # Order by workload, then random

        # If consultation type is specified, try to match specialties
        if consultation_type:
            if consultation_type.name == 'crisis_intervention':
                # For crisis, prioritize psychologists with crisis intervention specialty
                crisis_psychologists = available_psychologists.filter(
                    specialties__name__icontains='crisis'
                ).first()
                if crisis_psychologists:
                    return crisis_psychologists

            elif consultation_type.name == 'general_counseling':
                # For counseling, look for general counseling specialists
                counseling_psychologists = available_psychologists.filter(
                    Q(specialties__name__icontains='counseling') |
                    Q(specialties__name__icontains='therapy')
                ).first()
                if counseling_psychologists:
                    return counseling_psychologists

        # Return the psychologist with the least current workload
        return available_psychologists.first()

    @property
    def is_free_consultation(self):
        return self.consultation_type.is_free or self.payment_status == 'free'

    @property
    def duration_minutes(self):
        if self.actual_start_time and self.actual_end_time:
            delta = self.actual_end_time - self.actual_start_time
            return int(delta.total_seconds() / 60)
        return self.consultation_type.default_duration

    @property
    def can_start(self):
        """Check if consultation can be started"""
        if self.status != 'confirmed':
            return False

        # Check if it's within 15 minutes of scheduled time
        from datetime import datetime, timedelta
        scheduled_datetime = datetime.combine(self.scheduled_date, self.scheduled_start_time)
        # Make scheduled_datetime timezone-aware
        scheduled_datetime = timezone.make_aware(scheduled_datetime)
        now = timezone.now()

        # Allow starting 15 minutes early and up to 30 minutes late
        start_window = scheduled_datetime - timedelta(minutes=15)
        end_window = scheduled_datetime + timedelta(minutes=30)

        return start_window <= now <= end_window

    def can_user_book_free_consultation(self, user, consultation_type):
        """Check if user can book a free consultation of this type"""
        if not consultation_type.is_free:
            return True

        # Count existing free consultations of this type
        existing_count = Consultation.objects.filter(
            user=user,
            consultation_type=consultation_type,
            payment_status='free'
        ).exclude(status='cancelled').count()

        return existing_count < consultation_type.max_free_per_user

class ConsultationSummary(models.Model):
    """Post-consultation summary filled by psychologist"""
    RECOMMENDATION_TYPES = [
        ('continue_therapy', _('Continue Regular Therapy')),
        ('medication_referral', _('Medication/Psychiatric Referral')),
        ('hospital_referral', _('Hospital/Inpatient Referral')),
        ('specialist_referral', _('Specialist Referral')),
        ('self_care', _('Self-Care and Monitoring')),
        ('follow_up', _('Follow-up Session Recommended')),
        ('no_further_action', _('No Further Action Needed')),
    ]

    consultation = models.OneToOneField(
        Consultation,
        on_delete=models.CASCADE,
        related_name='summary'
    )

    # Assessment
    presenting_concerns = models.TextField(help_text=_("Main concerns presented by the client"))
    assessment_notes = models.TextField(help_text=_("Clinical assessment and observations"))
    diagnosis = models.TextField(blank=True, help_text=_("Preliminary diagnosis or screening results"))

    # Recommendations
    recommendations = models.CharField(
        max_length=50,
        choices=RECOMMENDATION_TYPES,
        help_text=_("Primary recommendation")
    )
    recommendation_details = models.TextField(help_text=_("Detailed recommendation and next steps"))

    # Follow-up
    follow_up_needed = models.BooleanField(default=False)
    follow_up_timeframe = models.CharField(
        max_length=100,
        blank=True,
        help_text=_("When should follow-up occur? (e.g., '2 weeks', '1 month')")
    )

    # Resources
    resources_provided = models.TextField(
        blank=True,
        help_text=_("Educational materials or resources shared with client")
    )

    # Files
    report_file = models.FileField(
        upload_to='consultation_reports/',
        blank=True,
        null=True,
        help_text=_("Optional PDF report")
    )

    # Risk Assessment
    risk_level = models.CharField(
        max_length=20,
        choices=[
            ('low', _('Low Risk')),
            ('moderate', _('Moderate Risk')),
            ('high', _('High Risk')),
            ('crisis', _('Crisis - Immediate Intervention Needed')),
        ],
        default='low'
    )
    risk_notes = models.TextField(blank=True, help_text=_("Risk assessment details"))

    # System fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("Consultation Summary")
        verbose_name_plural = _("Consultation Summaries")

    def __str__(self):
        return f"Summary for {self.consultation}"

class ConsultationRating(models.Model):
    """User rating and feedback for consultation"""
    consultation = models.OneToOneField(
        Consultation,
        on_delete=models.CASCADE,
        related_name='rating'
    )

    # Ratings (1-5 scale)
    overall_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text=_("Overall satisfaction (1-5)")
    )
    psychologist_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text=_("Psychologist performance (1-5)")
    )
    platform_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text=_("Platform experience (1-5)")
    )

    # Feedback
    feedback = models.TextField(blank=True, help_text=_("Written feedback"))
    would_recommend = models.BooleanField(default=True)

    # Specific aspects
    communication_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text=_("Communication quality (1-5)")
    )
    helpfulness_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text=_("How helpful was the session (1-5)")
    )

    # System fields
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _("Consultation Rating")
        verbose_name_plural = _("Consultation Ratings")

    def __str__(self):
        return f"Rating for {self.consultation} - {self.overall_rating}/5"


class ChatSession(models.Model):
    """Chat session for a consultation"""
    consultation = models.OneToOneField(
        Consultation,
        on_delete=models.CASCADE,
        related_name='chat_session'
    )
    is_active = models.BooleanField(default=False)
    started_at = models.DateTimeField(null=True, blank=True)
    ended_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'consultation_chat_session'

    def __str__(self):
        return f"Chat Session for {self.consultation}"

    @property
    def is_chat_time_active(self):
        """Check if current time is within consultation window (with 5-minute buffer)"""
        from django.utils import timezone
        from datetime import timedelta

        now = timezone.now()
        consultation = self.consultation

        # Create datetime objects for start and end times
        start_datetime = timezone.make_aware(
            timezone.datetime.combine(
                consultation.scheduled_date,
                consultation.scheduled_start_time
            )
        )
        end_datetime = timezone.make_aware(
            timezone.datetime.combine(
                consultation.scheduled_date,
                consultation.scheduled_end_time
            )
        )

        # Add 5-minute buffer before and after
        buffer_start = start_datetime - timedelta(minutes=5)
        buffer_end = end_datetime + timedelta(minutes=5)

        return buffer_start <= now <= buffer_end

    def can_access_chat(self, user):
        """Check if user can access this chat session"""
        consultation = self.consultation
        return (
            user == consultation.user or
            user == consultation.psychologist.user
        ) and consultation.status in ['confirmed', 'in_progress']


class ChatMessage(models.Model):
    """Individual chat message"""
    chat_session = models.ForeignKey(
        ChatSession,
        on_delete=models.CASCADE,
        related_name='messages'
    )
    sender = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='chat_messages'
    )
    message = models.TextField()
    encrypted_message = models.TextField(blank=True)  # For encryption
    timestamp = models.DateTimeField(auto_now_add=True)
    is_read = models.BooleanField(default=False)
    message_type = models.CharField(
        max_length=20,
        choices=[
            ('text', 'Text Message'),
            ('system', 'System Message'),
            ('notification', 'Notification'),
        ],
        default='text'
    )

    class Meta:
        db_table = 'consultation_chat_message'
        ordering = ['timestamp']

    def __str__(self):
        return f"Message from {self.sender.username} at {self.timestamp}"

    @property
    def sender_type(self):
        """Determine if sender is patient or psychologist"""
        consultation = self.chat_session.consultation
        if self.sender == consultation.user:
            return 'patient'
        elif self.sender == consultation.psychologist.user:
            return 'psychologist'
        else:
            return 'system'


class ChatSessionLog(models.Model):
    """Audit log for chat sessions"""
    chat_session = models.ForeignKey(
        ChatSession,
        on_delete=models.CASCADE,
        related_name='logs'
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    action = models.CharField(
        max_length=50,
        choices=[
            ('joined', 'Joined Chat'),
            ('left', 'Left Chat'),
            ('message_sent', 'Message Sent'),
            ('session_started', 'Session Started'),
            ('session_ended', 'Session Ended'),
        ]
    )
    timestamp = models.DateTimeField(auto_now_add=True)
    details = models.JSONField(default=dict, blank=True)

    class Meta:
        db_table = 'consultation_chat_log'
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.user.username} - {self.action} at {self.timestamp}"

class ConsultationMessage(models.Model):
    """Messages exchanged during consultation chat"""
    consultation = models.ForeignKey(
        Consultation,
        on_delete=models.CASCADE,
        related_name='messages'
    )
    sender = models.ForeignKey(User, on_delete=models.CASCADE)
    message = models.TextField()

    # File attachments
    attachment = models.FileField(
        upload_to='consultation_attachments/',
        blank=True,
        null=True
    )

    # Message metadata
    is_system_message = models.BooleanField(default=False)
    message_type = models.CharField(
        max_length=20,
        choices=[
            ('text', _('Text Message')),
            ('file', _('File Attachment')),
            ('system', _('System Message')),
            ('resource', _('Resource Share')),
        ],
        default='text'
    )

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _("Consultation Message")
        verbose_name_plural = _("Consultation Messages")
        ordering = ['created_at']

    def __str__(self):
        return f"{self.sender.username}: {self.message[:50]}..."

class PsychologistSpecialty(models.Model):
    """Specialties that psychologists can have"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name = _("Psychologist Specialty")
        verbose_name_plural = _("Psychologist Specialties")
        ordering = ['name']

    def __str__(self):
        return self.name

# Signal to update psychologist statistics
from django.db.models.signals import post_save
from django.dispatch import receiver

@receiver(post_save, sender=ConsultationRating)
def update_psychologist_rating(sender, instance, created, **kwargs):
    """Update psychologist's average rating when a new rating is added"""
    if created:
        psychologist = instance.consultation.psychologist
        ratings = ConsultationRating.objects.filter(
            consultation__psychologist=psychologist
        )

        # Calculate new averages
        total_ratings = ratings.count()
        avg_rating = ratings.aggregate(
            avg=models.Avg('overall_rating')
        )['avg']

        # Update psychologist profile
        from decimal import Decimal
        old_rating = float(psychologist.average_rating) if psychologist.average_rating else 0.0
        new_rating = round(float(avg_rating), 2) if avg_rating else 0.00

        psychologist.total_ratings = total_ratings
        psychologist.average_rating = Decimal(str(new_rating))
        psychologist.save()

        # Create milestone notifications for psychologist
        try:
            if total_ratings in [1, 5, 10, 25, 50, 100]:
                from core.views import create_notification
                create_notification(
                    user=psychologist.user,
                    title=f'Rating Milestone: {total_ratings} Reviews!',
                    message=f'Congratulations! You have received {total_ratings} patient reviews with an average rating of {new_rating}/5.',
                    notification_type='success',
                    url=f'/consultation/psychologist/{psychologist.pk}/'
                )

            # Notify if rating improved significantly
            elif old_rating > 0 and new_rating - old_rating >= 0.5:
                from core.views import create_notification
                create_notification(
                    user=psychologist.user,
                    title='Rating Improved!',
                    message=f'Great news! Your average rating has improved to {new_rating}/5.',
                    notification_type='success',
                    url=f'/consultation/psychologist/{psychologist.pk}/'
                )
        except Exception as e:
            # Log the error but don't break the rating system
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error creating rating notification: {e}")

@receiver(post_save, sender=Consultation)
def update_consultation_count(sender, instance, created, **kwargs):
    """Update psychologist's consultation count"""
    if instance.status == 'completed':
        psychologist = instance.psychologist
        completed_consultations = Consultation.objects.filter(
            psychologist=psychologist,
            status='completed'
        ).count()

        psychologist.total_consultations = completed_consultations
        psychologist.save()
