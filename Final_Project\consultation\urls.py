from django.urls import path
from . import views

app_name = 'consultation'

urlpatterns = [
    # Public consultation browsing
    path('', views.PsychologistListView.as_view(), name='psychologist_list'),
    path('psychologist/<int:pk>/', views.PsychologistDetailView.as_view(), name='psychologist_detail'),

    # Booking system
    path('book/<int:psychologist_id>/select-type/', views.ConsultationTypeSelectionView.as_view(), name='select_consultation_type'),
    path('book/<int:psychologist_id>/', views.BookConsultationView.as_view(), name='book_consultation'),
    path('book-auto/', views.AutoAssignBookingView.as_view(), name='auto_assign_booking'),
    path('my-consultations/', views.MyConsultationsView.as_view(), name='my_consultations'),
    path('consultation/<uuid:pk>/', views.ConsultationDetailView.as_view(), name='consultation_detail'),
    path('consultation/<uuid:pk>/complete/', views.CompleteConsultationView.as_view(), name='complete_consultation'),
    path('consultation/<uuid:pk>/cancel/', views.CancelConsultationView.as_view(), name='cancel_consultation'),
    path('consultation/<uuid:pk>/rate/', views.RateConsultationView.as_view(), name='rate_consultation'),
    path('consultation/<uuid:pk>/summary/', views.consultation_summary_view, name='consultation_summary'),

    # Psychologist dashboard
    path('dashboard/', views.PsychologistDashboardView.as_view(), name='psychologist_dashboard'),
    path('dashboard/consultations/', views.PsychologistConsultationsView.as_view(), name='psychologist_consultations'),
    path('dashboard/schedule/', views.ManageScheduleView.as_view(), name='manage_schedule'),
    path('dashboard/profile/', views.ManagePsychologistProfileView.as_view(), name='manage_psychologist_profile'),

    # Availability management
    path('availability/add/', views.add_availability, name='add_availability'),
    path('availability/<int:availability_id>/update/', views.update_availability, name='update_availability'),
    path('availability/<int:availability_id>/delete/', views.delete_availability, name='delete_availability'),

    # Time slot management
    path('time-slot/<int:slot_id>/edit/', views.edit_time_slot, name='edit_time_slot'),
    path('time-slot/<int:slot_id>/delete/', views.delete_time_slot, name='delete_time_slot'),

    # Chat system
    path('consultation/<uuid:consultation_id>/chat-room/', views.ChatRoomView.as_view(), name='chat_room'),

    # API endpoints
    path('api/slots/<int:psychologist_id>/', views.get_available_slots, name='get_available_slots'),
    path('api/chat/<uuid:consultation_id>/history/', views.chat_history_api, name='chat_history_api'),
    path('api/chat/<uuid:consultation_id>/status/', views.chat_status_api, name='chat_status_api'),
]
