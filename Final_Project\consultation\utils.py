import base64
from cryptography.fernet import <PERSON><PERSON><PERSON>
from django.conf import settings
import os

# Generate or get encryption key
def get_encryption_key():
    """Get or generate encryption key for chat messages"""
    key_file = os.path.join(settings.BASE_DIR, 'chat_encryption.key')
    
    if os.path.exists(key_file):
        with open(key_file, 'rb') as f:
            key = f.read()
    else:
        key = Fernet.generate_key()
        with open(key_file, 'wb') as f:
            f.write(key)
    
    return key

def encrypt_message(message):
    """Encrypt a chat message"""
    try:
        key = get_encryption_key()
        f = Fernet(key)
        encrypted_message = f.encrypt(message.encode())
        return base64.b64encode(encrypted_message).decode()
    except Exception:
        return message  # Return original if encryption fails

def decrypt_message(encrypted_message):
    """Decrypt a chat message"""
    try:
        key = get_encryption_key()
        f = Fernet(key)
        decoded_message = base64.b64decode(encrypted_message.encode())
        decrypted_message = f.decrypt(decoded_message)
        return decrypted_message.decode()
    except Exception:
        return encrypted_message  # Return original if decryption fails

def is_consultation_chat_active(consultation):
    """Check if consultation chat is currently active"""
    from django.utils import timezone
    from datetime import timedelta
    
    now = timezone.now()
    
    # Create datetime objects for start and end times
    start_datetime = timezone.make_aware(
        timezone.datetime.combine(
            consultation.scheduled_date,
            consultation.scheduled_start_time
        )
    )
    end_datetime = timezone.make_aware(
        timezone.datetime.combine(
            consultation.scheduled_date,
            consultation.scheduled_end_time
        )
    )
    
    # Add 5-minute buffer before and after
    buffer_start = start_datetime - timedelta(minutes=5)
    buffer_end = end_datetime + timedelta(minutes=5)
    
    return buffer_start <= now <= buffer_end

def get_chat_status_message(consultation):
    """Get appropriate status message for chat availability"""
    from django.utils import timezone
    from datetime import timedelta
    
    now = timezone.now()
    
    # Create datetime objects for start and end times
    start_datetime = timezone.make_aware(
        timezone.datetime.combine(
            consultation.scheduled_date,
            consultation.scheduled_start_time
        )
    )
    end_datetime = timezone.make_aware(
        timezone.datetime.combine(
            consultation.scheduled_date,
            consultation.scheduled_end_time
        )
    )
    
    # Add 5-minute buffer
    buffer_start = start_datetime - timedelta(minutes=5)
    buffer_end = end_datetime + timedelta(minutes=5)
    
    if now < buffer_start:
        time_until = buffer_start - now
        if time_until.days > 0:
            return f"Chat will be available on {start_datetime.strftime('%B %d, %Y at %I:%M %p')}"
        else:
            hours, remainder = divmod(time_until.seconds, 3600)
            minutes, _ = divmod(remainder, 60)
            if hours > 0:
                return f"Chat will be available in {hours}h {minutes}m"
            else:
                return f"Chat will be available in {minutes} minutes"
    elif buffer_start <= now <= buffer_end:
        return "Chat is currently active"
    else:
        return "Chat session has ended"

def format_chat_time_window(consultation):
    """Format the chat time window for display"""
    from django.utils import timezone
    from datetime import timedelta
    
    start_datetime = timezone.make_aware(
        timezone.datetime.combine(
            consultation.scheduled_date,
            consultation.scheduled_start_time
        )
    )
    end_datetime = timezone.make_aware(
        timezone.datetime.combine(
            consultation.scheduled_date,
            consultation.scheduled_end_time
        )
    )
    
    # Add 5-minute buffer
    buffer_start = start_datetime - timedelta(minutes=5)
    buffer_end = end_datetime + timedelta(minutes=5)
    
    return {
        'start': buffer_start,
        'end': buffer_end,
        'formatted_start': buffer_start.strftime('%I:%M %p'),
        'formatted_end': buffer_end.strftime('%I:%M %p'),
        'date': consultation.scheduled_date.strftime('%B %d, %Y')
    }
