from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, DetailView, CreateView, UpdateView, TemplateView, View
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, Http404
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.db.models import Q, Avg, Count, F
from django.urls import reverse_lazy, reverse
from datetime import datetime, timedelta, time, date
from decimal import Decimal
from django.db import models

from accounts.models import PsychologistProfile
from .models import (
    Consultation, ConsultationType, TimeSlot, ConsultationSummary,
    ConsultationRating, PsychologistAvailability, PsychologistSpecialty
)
from .forms import (
    BookingForm, ConsultationSummaryForm, ConsultationRatingForm,
    PsychologistAvailabilityForm, TimeSlotForm
)

class PsychologistListView(ListView):
    """List all approved psychologists with filtering"""
    model = PsychologistProfile
    template_name = 'consultation/booking_list.html'
    context_object_name = 'psychologists'
    paginate_by = 12

    def dispatch(self, request, *args, **kwargs):
        # Redirect psychologists and admins to appropriate dashboards
        if request.user.is_authenticated:
            if request.user.is_staff:
                messages.info(request, _('Admins should use the admin panel to manage consultations.'))
                return redirect('admin:index')
            elif hasattr(request.user, 'psychologist_profile'):
                messages.info(request, _('Psychologists should use their dashboard to manage consultations.'))
                return redirect('consultation:psychologist_dashboard')
        return super().dispatch(request, *args, **kwargs)

    def get_queryset(self):
        queryset = PsychologistProfile.objects.filter(
            approval_status='approved',
            is_available=True
        ).select_related('user').prefetch_related('specialties')

        # Filter by consultation type
        consultation_type = self.request.GET.get('consultation_type')
        if consultation_type:
            try:
                cons_type = ConsultationType.objects.get(name=consultation_type)
                # Filter psychologists who offer this consultation type
                queryset = queryset.filter(
                    availability_schedule__allowed_consultation_types=cons_type
                ).distinct()
            except ConsultationType.DoesNotExist:
                pass

        # Filter by specialty
        specialty = self.request.GET.get('specialty')
        if specialty:
            queryset = queryset.filter(specialties__name__icontains=specialty)

        # Filter by price range
        price_range = self.request.GET.get('price_range')
        if price_range == 'free':
            queryset = queryset.filter(offers_free_consultation=True)
        elif price_range == 'paid':
            queryset = queryset.filter(consultation_fee__gt=0)

        # Search by name or specialization
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(user__first_name__icontains=search) |
                Q(user__last_name__icontains=search) |
                Q(specializations__icontains=search) |
                Q(specialties__name__icontains=search)
            ).distinct()

        # Order by rating and experience
        return queryset.annotate(
            avg_rating=Avg('consultations__rating__overall_rating')
        ).order_by('-avg_rating', '-years_of_experience')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['consultation_types'] = ConsultationType.objects.filter(is_active=True)
        context['specialties'] = PsychologistSpecialty.objects.filter(is_active=True)
        context['current_filters'] = {
            'consultation_type': self.request.GET.get('consultation_type', ''),
            'specialty': self.request.GET.get('specialty', ''),
            'price_range': self.request.GET.get('price_range', ''),
            'search': self.request.GET.get('search', ''),
        }
        return context

class PsychologistDetailView(DetailView):
    """Detailed view of a psychologist"""
    model = PsychologistProfile
    template_name = 'consultation/psychologist_detail.html'
    context_object_name = 'psychologist'

    def get_queryset(self):
        return PsychologistProfile.objects.filter(
            approval_status='approved'
        ).select_related('user').prefetch_related('specialties')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        psychologist = self.object

        # Get available time slots for the next 30 days
        today = date.today()
        end_date = today + timedelta(days=30)

        context['available_slots'] = TimeSlot.objects.filter(
            psychologist=psychologist,
            date__range=[today, end_date],
            is_available=True,
            current_bookings__lt=models.F('max_bookings')
        ).order_by('date', 'start_time')[:20]  # Limit to 20 slots

        # Get consultation types
        context['consultation_types'] = ConsultationType.objects.filter(is_active=True)

        # Get recent reviews
        context['recent_reviews'] = ConsultationRating.objects.filter(
            consultation__psychologist=psychologist
        ).select_related('consultation__user').order_by('-created_at')[:5]

        # Calculate rating breakdown
        ratings = ConsultationRating.objects.filter(
            consultation__psychologist=psychologist
        )
        context['rating_breakdown'] = {
            '5': ratings.filter(overall_rating=5).count(),
            '4': ratings.filter(overall_rating=4).count(),
            '3': ratings.filter(overall_rating=3).count(),
            '2': ratings.filter(overall_rating=2).count(),
            '1': ratings.filter(overall_rating=1).count(),
        }

        return context

class ConsultationTypeSelectionView(LoginRequiredMixin, TemplateView):
    """Select consultation type (free or paid) before booking"""
    template_name = 'consultation/consultation_type_selection.html'

    def dispatch(self, request, *args, **kwargs):
        # Check if user is a regular user (not psychologist or admin)
        if request.user.is_staff or hasattr(request.user, 'psychologist_profile'):
            messages.error(request, _('Only regular users can book consultations.'))
            return redirect('consultation:psychologist_list')

        self.psychologist = get_object_or_404(
            PsychologistProfile,
            pk=kwargs['psychologist_id'],
            approval_status='approved'
        )
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['psychologist'] = self.psychologist

        # Check if user can book free consultation
        from datetime import date, timedelta
        two_months_ago = date.today() - timedelta(days=60)

        recent_free_consultations = Consultation.objects.filter(
            user=self.request.user,
            payment_status='free',
            created_at__gte=two_months_ago
        ).exclude(status='cancelled').count()

        context['can_book_free'] = recent_free_consultations == 0
        context['recent_free_count'] = recent_free_consultations

        return context

class BookConsultationView(LoginRequiredMixin, CreateView):
    """Book a consultation with a psychologist"""
    model = Consultation
    form_class = BookingForm
    template_name = 'consultation/booking_form.html'

    def dispatch(self, request, *args, **kwargs):
        # Check if user is a regular user (not psychologist or admin)
        if request.user.is_staff or hasattr(request.user, 'psychologist_profile'):
            messages.error(request, _('Only regular users can book consultations.'))
            return redirect('consultation:psychologist_list')

        self.psychologist = get_object_or_404(
            PsychologistProfile,
            pk=kwargs['psychologist_id'],
            approval_status='approved'
        )

        # Get consultation type from URL parameter
        self.consultation_type_filter = self.request.GET.get('type', None)

        # If no consultation type is specified, redirect to type selection
        if not self.consultation_type_filter:
            return redirect('consultation:select_consultation_type', psychologist_id=kwargs['psychologist_id'])

        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['psychologist'] = self.psychologist
        kwargs['user'] = self.request.user
        kwargs['consultation_type_filter'] = self.consultation_type_filter
        return kwargs

    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)

    def form_valid(self, form):
        consultation = form.save(commit=False)
        consultation.user = self.request.user
        consultation.psychologist = self.psychologist

        # Save the consultation
        consultation = form.save()

        # Redirect based on payment status
        if consultation.payment_status == 'free':
            messages.success(
                self.request,
                _('Your free consultation has been booked successfully!')
            )
        else:
            messages.success(
                self.request,
                _('Your consultation has been booked! You can complete the payment from the consultation details page.')
            )

        # Redirect based on payment status
        if consultation.payment_status == 'free':
            return redirect('consultation:my_consultations')
        else:
            # For paid consultations, redirect to payment page
            return redirect('payments:checkout', consultation_id=str(consultation.pk))

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['psychologist'] = self.psychologist
        context['consultation_type_filter'] = self.consultation_type_filter

        # Get available time slots directly (future slots only)
        from datetime import date
        today = date.today()

        available_time_slots = TimeSlot.objects.filter(
            psychologist=self.psychologist,
            date__gte=today,
            is_available=True,
            current_bookings__lt=models.F('max_bookings')
        ).order_by('date', 'start_time')

        context['available_time_slots'] = available_time_slots

        # Check free consultation eligibility
        from datetime import timedelta
        two_months_ago = date.today() - timedelta(days=60)

        recent_free_consultations = Consultation.objects.filter(
            user=self.request.user,
            payment_status='free',
            created_at__gte=two_months_ago
        ).exclude(status='cancelled').count()

        context['can_book_free'] = recent_free_consultations == 0
        context['recent_free_count'] = recent_free_consultations

        return context


class AutoAssignBookingView(LoginRequiredMixin, CreateView):
    """Auto-assign psychologist booking view"""
    model = Consultation
    form_class = BookingForm
    template_name = 'consultation/auto_assign_booking.html'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        kwargs['psychologist'] = None  # Will be assigned automatically
        return kwargs

    def form_valid(self, form):
        consultation = form.save(commit=False)
        consultation.user = self.request.user

        # Auto-assign psychologist
        assigned_psychologist = Consultation.auto_assign_psychologist(
            consultation_type=consultation.consultation_type
        )

        if not assigned_psychologist:
            messages.error(
                self.request,
                _('Sorry, no psychologists are currently available. Please try again later.')
            )
            return self.form_invalid(form)

        consultation.psychologist = assigned_psychologist
        consultation = form.save()

        messages.success(
            self.request,
            _('Your consultation has been booked with {}!').format(
                assigned_psychologist.user.get_full_name()
            )
        )

        # Redirect based on payment status
        if consultation.payment_status == 'free':
            return redirect('consultation:my_consultations')
        else:
            return redirect('payments:checkout', consultation_id=str(consultation.pk))

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # AutoAssignBookingView doesn't have a specific psychologist
        context['psychologist'] = None
        return context

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['psychologist'] = self.psychologist
        return context

class MyConsultationsView(LoginRequiredMixin, ListView):
    """User's consultation history"""
    model = Consultation
    template_name = 'consultation/my_consultations.html'
    context_object_name = 'consultations'
    paginate_by = 10

    def dispatch(self, request, *args, **kwargs):
        # Check if user is a regular user (not psychologist or admin)
        if request.user.is_staff or hasattr(request.user, 'psychologist_profile'):
            messages.error(request, _('Only regular users can view their consultation history.'))
            return redirect('core:home')
        return super().dispatch(request, *args, **kwargs)

    def get_queryset(self):
        return Consultation.objects.filter(
            user=self.request.user
        ).select_related(
            'psychologist__user', 'consultation_type'
        ).order_by('-created_at')

class ConsultationDetailView(LoginRequiredMixin, DetailView):
    """Detailed view of a consultation"""
    model = Consultation
    template_name = 'consultation/consultation_detail.html'
    context_object_name = 'consultation'

    def get_queryset(self):
        # Users can only see their own consultations or consultations they're the psychologist for
        return Consultation.objects.filter(
            Q(user=self.request.user) |
            Q(psychologist__user=self.request.user)
        ).select_related('user', 'psychologist__user', 'consultation_type')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        consultation = self.object

        # Check if user can start the consultation
        context['can_start'] = consultation.can_start

        # Check if user can rate (only after completion)
        context['can_rate'] = (
            consultation.status == 'completed' and
            consultation.user == self.request.user and
            not hasattr(consultation, 'rating')
        )

        # Check if psychologist can fill summary
        context['can_fill_summary'] = (
            consultation.status == 'completed' and
            consultation.psychologist.user == self.request.user and
            not hasattr(consultation, 'summary')
        )

        return context



class CompleteConsultationView(LoginRequiredMixin, TemplateView):
    """Complete a consultation (psychologist only)"""
    template_name = 'consultation/complete_consultation.html'

    def dispatch(self, request, *args, **kwargs):
        self.consultation = get_object_or_404(
            Consultation,
            pk=kwargs['pk'],
            psychologist__user=request.user
        )
        return super().dispatch(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        if self.consultation.status == 'in_progress':
            self.consultation.status = 'completed'
            self.consultation.actual_end_time = timezone.now()
            self.consultation.save()

            # Create notification for patient
            from core.views import create_consultation_notification
            create_consultation_notification(
                user=self.consultation.user,
                title=_('Consultation Completed'),
                message=f'Your consultation with {self.consultation.psychologist.user.get_full_name()} has been completed.',
                consultation=self.consultation,
                notification_type='consultation'
            )

            messages.success(request, _('Consultation completed successfully.'))
            return redirect('consultation:consultation_detail', pk=self.consultation.pk)

        messages.error(request, _('This consultation cannot be completed.'))
        return redirect('consultation:consultation_detail', pk=self.consultation.pk)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['consultation'] = self.consultation
        return context

class RateConsultationView(LoginRequiredMixin, CreateView):
    """Rate a completed consultation"""
    model = ConsultationRating
    form_class = ConsultationRatingForm
    template_name = 'consultation/rate_consultation.html'

    def dispatch(self, request, *args, **kwargs):
        self.consultation = get_object_or_404(
            Consultation,
            pk=kwargs['pk'],
            user=request.user,
            status='completed'
        )

        # Check if already rated
        if hasattr(self.consultation, 'rating'):
            messages.info(request, _('You have already rated this consultation.'))
            return redirect('consultation:consultation_detail', pk=self.consultation.pk)

        return super().dispatch(request, *args, **kwargs)

    def form_valid(self, form):
        rating = form.save(commit=False)
        rating.consultation = self.consultation
        rating.save()

        # Create notification for psychologist
        from core.views import create_notification
        create_notification(
            user=self.consultation.psychologist.user,
            title=_('New Rating Received'),
            message=f'You received a {rating.overall_rating}/5 star rating from {self.request.user.get_full_name() or self.request.user.username}.',
            notification_type='success',
            url=f'/consultation/psychologist/{self.consultation.psychologist.pk}/'
        )

        # Create notification for patient (confirmation)
        create_notification(
            user=self.request.user,
            title=_('Rating Submitted Successfully'),
            message=f'Thank you for rating your consultation with {self.consultation.psychologist.user.get_full_name()}.',
            notification_type='success',
            url=f'/consultation/consultation/{self.consultation.pk}/'
        )

        messages.success(self.request, _('Thank you for your feedback! Your rating helps us improve our services.'))
        return redirect('consultation:consultation_detail', pk=self.consultation.pk)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['consultation'] = self.consultation
        return context

class PsychologistDashboardView(LoginRequiredMixin, TemplateView):
    """Psychologist dashboard"""
    template_name = 'consultation/psychologist_dashboard.html'

    def dispatch(self, request, *args, **kwargs):
        try:
            self.psychologist_profile = request.user.psychologist_profile
        except PsychologistProfile.DoesNotExist:
            messages.error(request, _('You are not registered as a psychologist.'))
            return redirect('accounts:psychologist_signup')

        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        psychologist = self.psychologist_profile

        from datetime import date, timedelta
        today = date.today()

        # Get recent consultations
        context['recent_consultations'] = Consultation.objects.filter(
            psychologist=psychologist
        ).order_by('-created_at')[:5]

        # Statistics
        context['total_consultations'] = Consultation.objects.filter(
            psychologist=psychologist
        ).count()

        context['pending_consultations'] = Consultation.objects.filter(
            psychologist=psychologist,
            status='pending'
        ).count()

        context['upcoming_slots'] = TimeSlot.objects.filter(
            psychologist=psychologist,
            date__gte=today,
            is_available=True,
            current_bookings__lt=models.F('max_bookings')
        ).count()

        context['average_rating'] = psychologist.average_rating

        return context

class PsychologistConsultationsView(LoginRequiredMixin, ListView):
    """Psychologist's consultation list"""
    model = Consultation
    template_name = 'consultation/psychologist_consultations.html'
    context_object_name = 'consultations'
    paginate_by = 20

    def dispatch(self, request, *args, **kwargs):
        try:
            self.psychologist_profile = request.user.psychologist_profile
        except PsychologistProfile.DoesNotExist:
            messages.error(request, _('You are not registered as a psychologist.'))
            return redirect('accounts:psychologist_signup')

        return super().dispatch(request, *args, **kwargs)

    def get_queryset(self):
        queryset = Consultation.objects.filter(
            psychologist=self.psychologist_profile
        ).select_related('user', 'consultation_type').order_by('-created_at')

        # Filter by status
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Filter by date range
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')

        if date_from:
            try:
                date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
                queryset = queryset.filter(scheduled_date__gte=date_from)
            except ValueError:
                pass

        if date_to:
            try:
                date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
                queryset = queryset.filter(scheduled_date__lte=date_to)
            except ValueError:
                pass

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_choices'] = Consultation.STATUS_CHOICES
        context['current_filters'] = {
            'status': self.request.GET.get('status', ''),
            'date_from': self.request.GET.get('date_from', ''),
            'date_to': self.request.GET.get('date_to', ''),
        }
        return context

class ManageScheduleView(LoginRequiredMixin, View):
    """Manage psychologist time slots manually"""
    template_name = 'consultation/manage_schedule.html'

    def dispatch(self, request, *args, **kwargs):
        try:
            self.psychologist_profile = request.user.psychologist_profile
        except PsychologistProfile.DoesNotExist:
            messages.error(request, _('You are not registered as a psychologist.'))
            return redirect('accounts:psychologist_signup')

        return super().dispatch(request, *args, **kwargs)

    def get(self, request):
        form = TimeSlotForm(psychologist=self.psychologist_profile)
        return render(request, self.template_name, self.get_context_data(form=form))

    def post(self, request):
        form = TimeSlotForm(request.POST, psychologist=self.psychologist_profile)

        if form.is_valid():
            time_slot = form.save()
            messages.success(
                request,
                _('Time slot created successfully for {} from {} to {}.').format(
                    time_slot.date.strftime('%B %d, %Y'),
                    time_slot.start_time.strftime('%I:%M %p'),
                    time_slot.end_time.strftime('%I:%M %p')
                )
            )
            return redirect('consultation:manage_schedule')
        else:
            messages.error(request, _('Please correct the errors below.'))

        return render(request, self.template_name, self.get_context_data(form=form))

    def get_context_data(self, **kwargs):
        context = kwargs

        # Get manually created time slots
        from datetime import date
        today = date.today()

        time_slots = TimeSlot.objects.filter(
            psychologist=self.psychologist_profile,
            date__gte=today
        ).order_by('date', 'start_time')

        context['time_slots'] = time_slots

        # Calculate total bookings
        total_bookings = sum(slot.current_bookings for slot in time_slots)
        context['total_bookings'] = total_bookings

        # Get past time slots for reference
        context['past_slots'] = TimeSlot.objects.filter(
            psychologist=self.psychologist_profile,
            date__lt=today
        ).order_by('-date', '-start_time')[:10]

        context['consultation_types'] = ConsultationType.objects.filter(is_active=True)

        return context

@login_required
def edit_time_slot(request, slot_id):
    """Edit an existing time slot"""
    try:
        psychologist_profile = request.user.psychologist_profile
    except PsychologistProfile.DoesNotExist:
        messages.error(request, _('You are not registered as a psychologist.'))
        return redirect('accounts:psychologist_signup')

    time_slot = get_object_or_404(
        TimeSlot,
        pk=slot_id,
        psychologist=psychologist_profile
    )

    if request.method == 'POST':
        form = TimeSlotForm(
            request.POST,
            instance=time_slot,
            psychologist=psychologist_profile
        )

        if form.is_valid():
            updated_slot = form.save()
            messages.success(
                request,
                _('Time slot updated successfully for {} from {} to {}.').format(
                    updated_slot.date.strftime('%B %d, %Y'),
                    updated_slot.start_time.strftime('%I:%M %p'),
                    updated_slot.end_time.strftime('%I:%M %p')
                )
            )
            return redirect('consultation:manage_schedule')
        else:
            messages.error(request, _('Please correct the errors below.'))
    else:
        form = TimeSlotForm(instance=time_slot, psychologist=psychologist_profile)

    return render(request, 'consultation/edit_time_slot.html', {
        'form': form,
        'time_slot': time_slot
    })

@login_required
def delete_time_slot(request, slot_id):
    """Delete a time slot"""
    try:
        psychologist_profile = request.user.psychologist_profile
    except PsychologistProfile.DoesNotExist:
        messages.error(request, _('You are not registered as a psychologist.'))
        return redirect('accounts:psychologist_signup')

    time_slot = get_object_or_404(
        TimeSlot,
        pk=slot_id,
        psychologist=psychologist_profile
    )

    if request.method == 'POST':
        # Check if there are any bookings for this slot
        if time_slot.current_bookings > 0:
            messages.error(
                request,
                _('Cannot delete this time slot as it has {} booking(s). Please contact the patients first.').format(
                    time_slot.current_bookings
                )
            )
        else:
            slot_info = f"{time_slot.date.strftime('%B %d, %Y')} from {time_slot.start_time.strftime('%I:%M %p')} to {time_slot.end_time.strftime('%I:%M %p')}"
            time_slot.delete()
            messages.success(request, _('Time slot deleted successfully: {}').format(slot_info))

    return redirect('consultation:manage_schedule')

@login_required
def add_availability(request):
    """Add new availability schedule"""
    try:
        psychologist_profile = request.user.psychologist_profile
    except PsychologistProfile.DoesNotExist:
        messages.error(request, _('You are not registered as a psychologist.'))
        return redirect('accounts:psychologist_signup')

    if request.method == 'POST':
        day_of_week = request.POST.get('day_of_week')
        start_time = request.POST.get('start_time')
        end_time = request.POST.get('end_time')
        is_available = request.POST.get('is_available') == 'on'
        allowed_consultation_types = request.POST.getlist('allowed_consultation_types')

        # Validate times
        if start_time >= end_time:
            messages.error(request, _('End time must be after start time.'))
            return redirect('consultation:manage_schedule')

        # Check for existing availability on the same day and time
        existing = PsychologistAvailability.objects.filter(
            psychologist=psychologist_profile,
            day_of_week=day_of_week,
            start_time__lt=end_time,
            end_time__gt=start_time
        ).exists()

        if existing:
            messages.error(request, _('You already have availability set for this time period.'))
            return redirect('consultation:manage_schedule')

        # Create availability
        availability = PsychologistAvailability.objects.create(
            psychologist=psychologist_profile,
            day_of_week=day_of_week,
            start_time=start_time,
            end_time=end_time,
            is_available=is_available
        )

        # Add allowed consultation types
        if allowed_consultation_types:
            availability.allowed_consultation_types.set(allowed_consultation_types)

        messages.success(request, _('Availability added successfully.'))
        return redirect('consultation:manage_schedule')

    return redirect('consultation:manage_schedule')

@login_required
def update_availability(request, availability_id):
    """Update existing availability schedule"""
    try:
        psychologist_profile = request.user.psychologist_profile
    except PsychologistProfile.DoesNotExist:
        messages.error(request, _('You are not registered as a psychologist.'))
        return redirect('accounts:psychologist_signup')

    availability = get_object_or_404(
        PsychologistAvailability,
        pk=availability_id,
        psychologist=psychologist_profile
    )

    if request.method == 'POST':
        day_of_week = request.POST.get('day_of_week')
        start_time = request.POST.get('start_time')
        end_time = request.POST.get('end_time')
        is_available = request.POST.get('is_available') == 'on'
        allowed_consultation_types = request.POST.getlist('allowed_consultation_types')

        # Validate times
        if start_time >= end_time:
            messages.error(request, _('End time must be after start time.'))
            return redirect('consultation:manage_schedule')

        # Check for conflicts with other availability slots (excluding current one)
        existing = PsychologistAvailability.objects.filter(
            psychologist=psychologist_profile,
            day_of_week=day_of_week,
            start_time__lt=end_time,
            end_time__gt=start_time
        ).exclude(pk=availability_id).exists()

        if existing:
            messages.error(request, _('This conflicts with another availability slot.'))
            return redirect('consultation:manage_schedule')

        # Update availability
        availability.day_of_week = day_of_week
        availability.start_time = start_time
        availability.end_time = end_time
        availability.is_available = is_available
        availability.save()

        # Update allowed consultation types
        if allowed_consultation_types:
            availability.allowed_consultation_types.set(allowed_consultation_types)
        else:
            availability.allowed_consultation_types.clear()

        messages.success(request, _('Availability updated successfully.'))
        return redirect('consultation:manage_schedule')

    return redirect('consultation:manage_schedule')

@login_required
def delete_availability(request, availability_id):
    """Delete availability schedule"""
    try:
        psychologist_profile = request.user.psychologist_profile
    except PsychologistProfile.DoesNotExist:
        messages.error(request, _('You are not registered as a psychologist.'))
        return redirect('accounts:psychologist_signup')

    availability = get_object_or_404(
        PsychologistAvailability,
        pk=availability_id,
        psychologist=psychologist_profile
    )

    if request.method == 'POST':
        availability.delete()
        messages.success(request, _('Availability deleted successfully.'))

    return redirect('consultation:manage_schedule')



class ManagePsychologistProfileView(LoginRequiredMixin, UpdateView):
    """Manage psychologist profile"""
    model = PsychologistProfile
    template_name = 'consultation/manage_profile.html'
    fields = [
        'specializations', 'specialties', 'education', 'certifications',
        'consultation_fee', 'offers_free_consultation', 'available_languages',
        'is_available', 'working_hours_start', 'working_hours_end', 'working_days'
    ]
    success_url = reverse_lazy('consultation:psychologist_dashboard')

    def get_object(self):
        try:
            return self.request.user.psychologist_profile
        except PsychologistProfile.DoesNotExist:
            messages.error(self.request, _('You are not registered as a psychologist.'))
            return redirect('accounts:psychologist_signup')

    def form_valid(self, form):
        messages.success(self.request, _('Profile updated successfully.'))
        return super().form_valid(form)

# API Views for AJAX requests
@login_required
def get_available_slots(request, psychologist_id):
    """Get available time slots for a psychologist"""
    try:
        psychologist = PsychologistProfile.objects.get(
            pk=psychologist_id,
            approval_status='approved'
        )
    except PsychologistProfile.DoesNotExist:
        return JsonResponse({'error': 'Psychologist not found'}, status=404)

    date_str = request.GET.get('date')
    if not date_str:
        return JsonResponse({'error': 'Date parameter required'}, status=400)

    try:
        selected_date = datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        return JsonResponse({'error': 'Invalid date format'}, status=400)

    # Get available slots for the date
    slots = TimeSlot.objects.filter(
        psychologist=psychologist,
        date=selected_date,
        is_available=True,
        current_bookings__lt=models.F('max_bookings')
    ).order_by('start_time')

    slots_data = []
    for slot in slots:
        slots_data.append({
            'id': slot.id,
            'start_time': slot.start_time.strftime('%H:%M'),
            'end_time': slot.end_time.strftime('%H:%M'),
            'available_spots': slot.max_bookings - slot.current_bookings
        })

    return JsonResponse({'slots': slots_data})

@login_required
def consultation_summary_view(request, pk):
    """Create or view consultation summary"""
    consultation = get_object_or_404(
        Consultation,
        pk=pk,
        psychologist__user=request.user,
        status='completed'
    )

    if request.method == 'POST':
        if hasattr(consultation, 'summary'):
            form = ConsultationSummaryForm(request.POST, request.FILES, instance=consultation.summary)
        else:
            form = ConsultationSummaryForm(request.POST, request.FILES)

        if form.is_valid():
            summary = form.save(commit=False)
            summary.consultation = consultation
            summary.save()

            messages.success(request, _('Consultation summary saved successfully.'))
            return redirect('consultation:consultation_detail', pk=consultation.pk)
    else:
        if hasattr(consultation, 'summary'):
            form = ConsultationSummaryForm(instance=consultation.summary)
        else:
            form = ConsultationSummaryForm()

    return render(request, 'consultation/consultation_summary.html', {
        'form': form,
        'consultation': consultation
    })


class CancelConsultationView(LoginRequiredMixin, View):
    """Cancel a consultation"""

    def post(self, request, pk):
        consultation = get_object_or_404(
            Consultation,
            pk=pk,
            user=request.user
        )

        # Only allow cancellation if consultation is pending or confirmed
        if consultation.status in ['pending', 'confirmed']:
            consultation.status = 'cancelled'
            consultation.save()

            messages.success(
                request,
                _('Your consultation has been cancelled successfully.')
            )
        else:
            messages.error(
                request,
                _('This consultation cannot be cancelled.')
            )

        return redirect('consultation:my_consultations')


# Chat System Views
class ChatRoomView(LoginRequiredMixin, TemplateView):
    """Real-time chat room for consultation"""
    template_name = 'consultation/chat_room.html'

    def dispatch(self, request, *args, **kwargs):
        self.consultation = get_object_or_404(
            Consultation,
            pk=kwargs['consultation_id']
        )

        # Check if user has access to this consultation
        if not (self.consultation.user == request.user or
                self.consultation.psychologist.user == request.user):
            raise Http404("You don't have access to this chat.")

        # Check if consultation allows chat
        if self.consultation.status not in ['confirmed', 'in_progress']:
            messages.error(request, _('Chat is only available for confirmed or in-progress consultations.'))
            return redirect('consultation:consultation_detail', pk=self.consultation.pk)

        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from .models import ChatSession, ChatMessage
        from .utils import is_consultation_chat_active, get_chat_status_message, format_chat_time_window

        # Get or create chat session
        chat_session, created = ChatSession.objects.get_or_create(
            consultation=self.consultation
        )

        # Get chat messages
        messages = ChatMessage.objects.filter(
            chat_session=chat_session
        ).select_related('sender').order_by('timestamp')

        # Check if chat is currently active
        is_active = is_consultation_chat_active(self.consultation)
        status_message = get_chat_status_message(self.consultation)
        time_window = format_chat_time_window(self.consultation)

        context.update({
            'consultation': self.consultation,
            'chat_session': chat_session,
            'chat_messages': messages,
            'is_chat_active': is_active,
            'chat_status_message': status_message,
            'time_window': time_window,
            'is_patient': self.consultation.user == self.request.user,
            'is_psychologist': self.consultation.psychologist.user == self.request.user,
            'websocket_url': f'ws://{self.request.get_host()}/ws/chat/{self.consultation.pk}/',
        })

        return context


@login_required
def chat_history_api(request, consultation_id):
    """API endpoint to get chat history"""
    consultation = get_object_or_404(Consultation, pk=consultation_id)

    # Check access
    if not (consultation.user == request.user or
            consultation.psychologist.user == request.user):
        return JsonResponse({'error': 'Access denied'}, status=403)

    from .models import ChatSession, ChatMessage

    try:
        chat_session = ChatSession.objects.get(consultation=consultation)
        messages = ChatMessage.objects.filter(
            chat_session=chat_session
        ).select_related('sender').order_by('timestamp')

        messages_data = []
        for msg in messages:
            messages_data.append({
                'id': msg.id,
                'message': msg.message,
                'sender': msg.sender.get_full_name() or msg.sender.username,
                'sender_type': msg.sender_type,
                'timestamp': msg.timestamp.isoformat(),
                'is_read': msg.is_read,
                'message_type': msg.message_type
            })

        return JsonResponse({
            'messages': messages_data,
            'total_count': len(messages_data)
        })

    except ChatSession.DoesNotExist:
        return JsonResponse({'messages': [], 'total_count': 0})


@login_required
def chat_status_api(request, consultation_id):
    """API endpoint to check chat status"""
    consultation = get_object_or_404(Consultation, pk=consultation_id)

    # Check access
    if not (consultation.user == request.user or
            consultation.psychologist.user == request.user):
        return JsonResponse({'error': 'Access denied'}, status=403)

    from .utils import is_consultation_chat_active, get_chat_status_message, format_chat_time_window

    is_active = is_consultation_chat_active(consultation)
    status_message = get_chat_status_message(consultation)
    time_window = format_chat_time_window(consultation)

    return JsonResponse({
        'is_active': is_active,
        'status_message': status_message,
        'time_window': time_window,
        'consultation_status': consultation.status
    })
