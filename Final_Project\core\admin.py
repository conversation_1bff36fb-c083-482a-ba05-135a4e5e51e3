from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import SiteSettings, FAQ, ContactMessage, Notification

@admin.register(SiteSettings)
class SiteSettingsAdmin(admin.ModelAdmin):
    """Site Settings admin"""
    list_display = ('site_name', 'contact_email', 'total_users', 'updated_at')
    readonly_fields = ('updated_at',)

    fieldsets = (
        (_('Basic Settings'), {
            'fields': ('site_name', 'site_description', 'site_logo', 'favicon')
        }),
        (_('Contact Information'), {
            'fields': ('contact_email', 'contact_phone', 'address')
        }),
        (_('Social Media'), {
            'fields': ('facebook_url', 'twitter_url', 'instagram_url', 'linkedin_url')
        }),
        (_('Emergency Contact'), {
            'fields': ('emergency_hotline', 'crisis_text_line')
        }),
        (_('Statistics'), {
            'fields': ('total_users', 'total_psychologists', 'total_consultations', 'total_resources')
        }),
        (_('Timestamp'), {
            'fields': ('updated_at',),
            'classes': ('collapse',)
        }),
    )

    def has_add_permission(self, request):
        # Only allow one SiteSettings instance
        return not SiteSettings.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Don't allow deletion of site settings
        return False

@admin.register(FAQ)
class FAQAdmin(admin.ModelAdmin):
    """FAQ admin"""
    list_display = ('question', 'category', 'is_active', 'order', 'created_at')
    list_filter = ('category', 'is_active', 'created_at')
    search_fields = ('question', 'answer')
    list_editable = ('is_active', 'order')
    ordering = ('category', 'order')

    fieldsets = (
        (_('FAQ Information'), {
            'fields': ('question', 'answer', 'category', 'is_active', 'order')
        }),
        (_('Timestamp'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    readonly_fields = ('created_at', 'updated_at')

@admin.register(ContactMessage)
class ContactMessageAdmin(admin.ModelAdmin):
    """Contact Message admin"""
    list_display = ('name', 'email', 'subject', 'is_read', 'created_at')
    list_filter = ('is_read', 'created_at')
    search_fields = ('name', 'email', 'subject', 'message')
    readonly_fields = ('created_at',)

    fieldsets = (
        (_('Contact Information'), {
            'fields': ('name', 'email', 'subject')
        }),
        (_('Message'), {
            'fields': ('message',)
        }),
        (_('Status'), {
            'fields': ('is_read', 'admin_response')
        }),
        (_('Timestamp'), {
            'fields': ('created_at',)
        }),
    )

    actions = ['mark_as_read', 'mark_as_unread']

    def mark_as_read(self, request, queryset):
        updated = queryset.update(is_read=True)
        self.message_user(request, f'{updated} message(s) marked as read.')
    mark_as_read.short_description = _('Mark selected messages as read')

    def mark_as_unread(self, request, queryset):
        updated = queryset.update(is_read=False)
        self.message_user(request, f'{updated} message(s) marked as unread.')
    mark_as_unread.short_description = _('Mark selected messages as unread')

@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    """Notification admin"""
    list_display = ('user', 'title', 'notification_type', 'is_read', 'created_at')
    list_filter = ('notification_type', 'is_read', 'created_at')
    search_fields = ('user__username', 'title', 'message')
    readonly_fields = ('created_at',)

    fieldsets = (
        (_('Notification Information'), {
            'fields': ('user', 'title', 'message', 'notification_type')
        }),
        (_('Status'), {
            'fields': ('is_read',)
        }),
        (_('Links'), {
            'fields': ('url',)
        }),
        (_('Timestamp'), {
            'fields': ('created_at',)
        }),
    )

    actions = ['mark_as_read', 'mark_as_unread', 'create_test_notifications']

    def create_test_notifications(self, request, queryset):
        """Create test notifications for the current user"""
        from django.contrib import messages

        test_notifications = [
            {
                'title': 'Welcome to ECPI!',
                'message': 'Thank you for joining our mental health platform. We\'re here to support you.',
                'notification_type': 'info',
            },
            {
                'title': 'Consultation Reminder',
                'message': 'You have an upcoming consultation tomorrow at 2:00 PM.',
                'notification_type': 'consultation',
            },
            {
                'title': 'New Message',
                'message': 'You have received a new message from Dr. Smith.',
                'notification_type': 'message',
            },
        ]

        created_count = 0
        for notif_data in test_notifications:
            notification, created = Notification.objects.get_or_create(
                user=request.user,
                title=notif_data['title'],
                defaults=notif_data
            )
            if created:
                created_count += 1

        messages.success(request, f'Created {created_count} test notifications for you!')

    create_test_notifications.short_description = _('Create test notifications for me')

    def mark_as_read(self, request, queryset):
        from django.utils import timezone
        updated = 0
        for notification in queryset.filter(is_read=False):
            notification.is_read = True
            notification.read_at = timezone.now()
            notification.save()
            updated += 1

        self.message_user(request, f'{updated} notification(s) marked as read.')
    mark_as_read.short_description = _('Mark selected notifications as read')

    def mark_as_unread(self, request, queryset):
        updated = queryset.update(is_read=False, read_at=None)
        self.message_user(request, f'{updated} notification(s) marked as unread.')
    mark_as_unread.short_description = _('Mark selected notifications as unread')
