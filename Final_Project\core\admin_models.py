from django.db import models
from django.contrib.auth.models import User
from django.utils.translation import gettext_lazy as _

class AdminRole(models.Model):
    """Custom admin roles for the system"""
    ROLE_CHOICES = [
        ('super_admin', _('Super Admin')),
        ('hr_manager', _('HR Manager')),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='admin_role')
    role = models.CharField(max_length=20, choices=ROLE_CHOICES)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        related_name='created_admin_roles'
    )
    
    class Meta:
        verbose_name = _("Admin Role")
        verbose_name_plural = _("Admin Roles")
    
    def __str__(self):
        return f"{self.user.get_full_name()} - {self.get_role_display()}"
    
    @property
    def is_super_admin(self):
        return self.role == 'super_admin'
    
    @property
    def is_hr_manager(self):
        return self.role == 'hr_manager'

class AdminSession(models.Model):
    """Track admin login sessions"""
    admin_role = models.ForeignKey(AdminRole, on_delete=models.CASCADE)
    login_time = models.DateTimeField(auto_now_add=True)
    logout_time = models.DateTimeField(null=True, blank=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    is_active = models.BooleanField(default=True)
    
    class Meta:
        verbose_name = _("Admin Session")
        verbose_name_plural = _("Admin Sessions")
        ordering = ['-login_time']
    
    def __str__(self):
        return f"{self.admin_role.user.username} - {self.login_time}"
