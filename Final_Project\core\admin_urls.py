from django.urls import path
from . import admin_views

app_name = 'custom_admin'

urlpatterns = [
    # Custom Admin System
    path('', admin_views.admin_dashboard, name='dashboard'),  # /admin/ -> dashboard
    path('login/', admin_views.admin_login, name='login'),
    path('logout/', admin_views.admin_logout, name='logout'),
    path('dashboard/', admin_views.admin_dashboard, name='dashboard_alt'),  # Alternative path
    
    # Super Admin Only
    path('users/', admin_views.manage_users, name='manage_users'),
    path('accounts/', admin_views.manage_accounts, name='manage_accounts'),
    path('accounts/<int:user_id>/', admin_views.account_details, name='account_details'),
    path('accounts/<int:user_id>/toggle-status/', admin_views.toggle_account_status, name='toggle_account_status'),
    path('psychologists/', admin_views.manage_psychologists, name='manage_psychologists'),
    path('psychologists/<int:psychologist_id>/approve/', admin_views.approve_psychologist, name='approve_psychologist'),
    
    # HR Manager Only
    path('consultations/', admin_views.manage_consultations, name='manage_consultations'),
    path('consultation-types/', admin_views.manage_consultation_types, name='manage_consultation_types'),
    path('analytics/', admin_views.hr_analytics, name='hr_analytics'),
    path('chat-sessions/', admin_views.chat_sessions_overview, name='chat_sessions'),
    path('chat-sessions/<int:session_id>/', admin_views.chat_session_detail, name='chat_session_detail'),
]
