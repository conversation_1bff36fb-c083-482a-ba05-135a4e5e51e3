from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.views.decorators.csrf import csrf_protect
from django.views.decorators.cache import never_cache
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count, Avg
from django.utils import timezone
from datetime import timedelta, date

from .admin_models import AdminRole, AdminSession
from accounts.models import PsychologistProfile
from consultation.models import Consultation, ConsultationType, TimeSlot

def admin_required(role=None):
    """Decorator to require admin role"""
    def decorator(view_func):
        def wrapper(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return redirect('custom_admin:login')

            try:
                admin_role = request.user.admin_role
                if not admin_role.is_active:
                    messages.error(request, 'Your admin account is inactive.')
                    return redirect('custom_admin:login')

                if role and admin_role.role != role:
                    messages.error(request, 'You do not have permission to access this page.')
                    return redirect('custom_admin:dashboard')

                return view_func(request, *args, **kwargs)
            except AdminRole.DoesNotExist:
                messages.error(request, 'You do not have admin privileges.')
                return redirect('custom_admin:login')
        
        return wrapper
    return decorator

@csrf_protect
@never_cache
def admin_login(request):
    """Custom admin login"""
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        
        user = authenticate(request, username=username, password=password)
        if user:
            try:
                admin_role = user.admin_role
                if admin_role.is_active:
                    login(request, user)
                    
                    # Create admin session record
                    AdminSession.objects.create(
                        admin_role=admin_role,
                        ip_address=request.META.get('REMOTE_ADDR', ''),
                        user_agent=request.META.get('HTTP_USER_AGENT', '')
                    )
                    
                    messages.success(request, 'Welcome to the admin panel!')
                    return redirect('custom_admin:dashboard')
                else:
                    messages.error(request, 'Your admin account is inactive.')
            except AdminRole.DoesNotExist:
                messages.error(request, 'You do not have admin privileges.')
        else:
            messages.error(request, 'Invalid username or password.')
    
    return render(request, 'core/admin/login.html')

@admin_required()
def admin_dashboard(request):
    """Main admin dashboard"""
    admin_role = request.user.admin_role
    
    # Common statistics
    total_users = User.objects.count()
    total_psychologists = PsychologistProfile.objects.count()
    pending_approvals = PsychologistProfile.objects.filter(approval_status='pending').count()
    total_consultations = Consultation.objects.count()
    recent_consultations = Consultation.objects.order_by('-created_at')[:5]
    
    context = {
        'admin_role': admin_role,
        'total_users': total_users,
        'total_psychologists': total_psychologists,
        'pending_approvals': pending_approvals,
        'total_consultations': total_consultations,
        'recent_consultations': recent_consultations,
    }
    
    # HR Manager specific statistics
    if admin_role.is_hr_manager:
        today = date.today()
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)
        
        context.update({
            'consultations_this_week': Consultation.objects.filter(created_at__gte=week_ago).count(),
            'consultations_this_month': Consultation.objects.filter(created_at__gte=month_ago).count(),
            'pending_consultations': Consultation.objects.filter(status='pending').count(),
            'completed_consultations': Consultation.objects.filter(status='completed').count(),
            'free_consultations': Consultation.objects.filter(payment_status='free').count(),
            'paid_consultations': Consultation.objects.filter(payment_status='completed').count(),
            'average_rating': Consultation.objects.aggregate(
                avg_rating=Avg('rating__overall_rating')
            )['avg_rating'] or 0,
            'total_time_slots': TimeSlot.objects.count(),
            'available_slots': TimeSlot.objects.filter(
                is_available=True,
                date__gte=today
            ).count(),
        })
    
    return render(request, 'core/admin/dashboard.html', context)

@admin_required(role='super_admin')
def manage_users(request):
    """Manage users - Super Admin only"""
    search_query = request.GET.get('search', '')
    users = User.objects.all().order_by('-date_joined')
    
    if search_query:
        users = users.filter(
            Q(username__icontains=search_query) |
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(email__icontains=search_query)
        )
    
    paginator = Paginator(users, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    return render(request, 'core/admin/manage_users.html', {
        'page_obj': page_obj,
        'search_query': search_query,
    })

@admin_required(role='super_admin')
def manage_psychologists(request):
    """Manage psychologist approvals - Super Admin only"""
    status_filter = request.GET.get('status', 'all')
    psychologists = PsychologistProfile.objects.all().order_by('-created_at')
    
    if status_filter != 'all':
        psychologists = psychologists.filter(approval_status=status_filter)
    
    paginator = Paginator(psychologists, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    return render(request, 'core/admin/manage_psychologists.html', {
        'page_obj': page_obj,
        'status_filter': status_filter,
    })

@admin_required(role='hr_manager')
def manage_consultations(request):
    """Manage consultations - HR Manager only"""
    status_filter = request.GET.get('status', 'all')
    consultations = Consultation.objects.all().order_by('-created_at')
    
    if status_filter != 'all':
        consultations = consultations.filter(status=status_filter)
    
    paginator = Paginator(consultations, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    return render(request, 'core/admin/manage_consultations.html', {
        'page_obj': page_obj,
        'status_filter': status_filter,
    })

@admin_required(role='hr_manager')
def manage_consultation_types(request):
    """Manage consultation types - HR Manager only"""
    consultation_types = ConsultationType.objects.all().order_by('display_name')
    
    return render(request, 'core/admin/manage_consultation_types.html', {
        'consultation_types': consultation_types,
    })

@admin_required(role='hr_manager')
def hr_analytics(request):
    """HR Analytics dashboard"""
    today = date.today()
    month_ago = today - timedelta(days=30)

    # Consultation statistics
    total_consultations = Consultation.objects.count()
    monthly_consultations = Consultation.objects.filter(created_at__gte=month_ago).count()

    # Calculate growth rate
    growth_rate = 0
    if total_consultations > 0:
        growth_rate = round((monthly_consultations * 100) / total_consultations, 1)

    # Calculate estimated revenue
    estimated_revenue = total_consultations * 50  # Assuming average $50 per consultation

    # Status breakdown
    status_stats = list(Consultation.objects.values('status').annotate(count=Count('id')))

    # Payment statistics
    payment_stats = list(Consultation.objects.values('payment_status').annotate(count=Count('id')))

    # Calculate completion rate
    completion_rate = 0
    completed_count = 0
    for status in status_stats:
        if status['status'] == 'completed':
            completed_count = status['count']
            break

    if total_consultations > 0:
        completion_rate = round((completed_count * 100) / total_consultations, 1)

    # Top psychologists
    top_psychologists = PsychologistProfile.objects.annotate(
        consultation_count=Count('consultations')
    ).order_by('-consultation_count')[:10]

    # Calculate average consultations per day
    avg_per_day = 0
    if monthly_consultations > 0:
        avg_per_day = round(monthly_consultations / 30, 1)

    context = {
        'total_consultations': total_consultations,
        'monthly_consultations': monthly_consultations,
        'growth_rate': growth_rate,
        'estimated_revenue': estimated_revenue,
        'completion_rate': completion_rate,
        'avg_per_day': avg_per_day,
        'status_stats': status_stats,
        'payment_stats': payment_stats,
        'top_psychologists': top_psychologists,
    }

    return render(request, 'core/admin/hr_analytics.html', context)

@admin_required(role='super_admin')
def manage_accounts(request):
    """Comprehensive account management - Super Admin only"""
    # Get filter parameters
    account_type = request.GET.get('type', 'all')
    status_filter = request.GET.get('status', 'all')
    search_query = request.GET.get('search', '')

    # Base queryset
    users = User.objects.all().select_related('psychologist_profile').prefetch_related('admin_role')

    # Apply filters
    if account_type == 'patients':
        users = users.filter(psychologist_profile__isnull=True, admin_role__isnull=True)
    elif account_type == 'psychologists':
        users = users.filter(psychologist_profile__isnull=False)
    elif account_type == 'admins':
        users = users.filter(admin_role__isnull=False)

    if status_filter == 'active':
        users = users.filter(is_active=True)
    elif status_filter == 'inactive':
        users = users.filter(is_active=False)
    elif status_filter == 'staff':
        users = users.filter(is_staff=True)

    if search_query:
        users = users.filter(
            Q(username__icontains=search_query) |
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(email__icontains=search_query)
        )

    # Order by date joined (newest first)
    users = users.order_by('-date_joined')

    # Pagination
    paginator = Paginator(users, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Account statistics
    total_accounts = User.objects.count()
    patient_accounts = User.objects.filter(psychologist_profile__isnull=True, admin_role__isnull=True).count()
    psychologist_accounts = User.objects.filter(psychologist_profile__isnull=False).count()
    admin_accounts = User.objects.filter(admin_role__isnull=False).count()
    active_accounts = User.objects.filter(is_active=True).count()
    inactive_accounts = User.objects.filter(is_active=False).count()

    # Recent registrations (last 7 days)
    week_ago = timezone.now() - timedelta(days=7)
    recent_registrations = User.objects.filter(date_joined__gte=week_ago).count()

    context = {
        'page_obj': page_obj,
        'account_type': account_type,
        'status_filter': status_filter,
        'search_query': search_query,
        'total_accounts': total_accounts,
        'patient_accounts': patient_accounts,
        'psychologist_accounts': psychologist_accounts,
        'admin_accounts': admin_accounts,
        'active_accounts': active_accounts,
        'inactive_accounts': inactive_accounts,
        'recent_registrations': recent_registrations,
    }

    return render(request, 'core/admin/manage_accounts.html', context)

@admin_required(role='super_admin')
def account_details(request, user_id):
    """View detailed account information - Super Admin only"""
    user = get_object_or_404(User, pk=user_id)

    # Get related information
    psychologist_profile = getattr(user, 'psychologist_profile', None)
    admin_role = getattr(user, 'admin_role', None)

    # Get user's consultations
    consultations = Consultation.objects.filter(user=user).order_by('-created_at')[:10]

    # Get user's notifications
    notifications = user.notifications.order_by('-created_at')[:5]

    context = {
        'account_user': user,  # Renamed to avoid conflict with request.user
        'psychologist_profile': psychologist_profile,
        'admin_role': admin_role,
        'consultations': consultations,
        'notifications': notifications,
    }

    return render(request, 'core/admin/account_details.html', context)

@admin_required(role='super_admin')
def toggle_account_status(request, user_id):
    """Toggle user account active/inactive status - Super Admin only"""
    if request.method == 'POST':
        user = get_object_or_404(User, pk=user_id)

        # Don't allow deactivating superusers
        if user.is_superuser and user.is_active:
            messages.error(request, 'Cannot deactivate superuser accounts.')
            return redirect('custom_admin:account_details', user_id=user_id)

        # Toggle status
        user.is_active = not user.is_active
        user.save()

        status = 'activated' if user.is_active else 'deactivated'
        messages.success(request, f'Account for {user.get_full_name() or user.username} has been {status}.')

        return redirect('custom_admin:account_details', user_id=user_id)

    return redirect('custom_admin:manage_accounts')

@admin_required(role='hr_manager')
def chat_sessions_overview(request):
    """HR Manager chat sessions overview"""
    from consultation.models import ChatSession, ChatMessage, ChatSessionLog
    from django.db.models import Count, Q

    # Get filter parameters
    status_filter = request.GET.get('status', 'all')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')

    # Base queryset
    chat_sessions = ChatSession.objects.select_related(
        'consultation__user',
        'consultation__psychologist__user',
        'consultation__consultation_type'
    ).annotate(
        message_count=Count('messages'),
        log_count=Count('logs')
    )

    # Apply filters
    if status_filter == 'active':
        chat_sessions = chat_sessions.filter(is_active=True)
    elif status_filter == 'completed':
        chat_sessions = chat_sessions.filter(is_active=False, ended_at__isnull=False)

    if date_from:
        try:
            date_from_obj = timezone.datetime.strptime(date_from, '%Y-%m-%d').date()
            chat_sessions = chat_sessions.filter(consultation__scheduled_date__gte=date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = timezone.datetime.strptime(date_to, '%Y-%m-%d').date()
            chat_sessions = chat_sessions.filter(consultation__scheduled_date__lte=date_to_obj)
        except ValueError:
            pass

    # Order by most recent
    chat_sessions = chat_sessions.order_by('-created_at')

    # Pagination
    paginator = Paginator(chat_sessions, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Statistics
    total_sessions = ChatSession.objects.count()
    active_sessions = ChatSession.objects.filter(is_active=True).count()
    total_messages = ChatMessage.objects.count()

    # Recent activity
    recent_messages = ChatMessage.objects.select_related(
        'chat_session__consultation__user',
        'chat_session__consultation__psychologist__user',
        'sender'
    ).order_by('-timestamp')[:10]

    context = {
        'page_obj': page_obj,
        'status_filter': status_filter,
        'date_from': date_from,
        'date_to': date_to,
        'total_sessions': total_sessions,
        'active_sessions': active_sessions,
        'total_messages': total_messages,
        'recent_messages': recent_messages,
    }

    return render(request, 'core/admin/chat_sessions.html', context)

@admin_required(role='hr_manager')
def chat_session_detail(request, session_id):
    """Detailed view of a chat session"""
    from consultation.models import ChatSession, ChatMessage, ChatSessionLog

    chat_session = get_object_or_404(
        ChatSession.objects.select_related(
            'consultation__user',
            'consultation__psychologist__user',
            'consultation__consultation_type'
        ),
        pk=session_id
    )

    # Get messages
    messages = ChatMessage.objects.filter(
        chat_session=chat_session
    ).select_related('sender').order_by('timestamp')

    # Get logs
    logs = ChatSessionLog.objects.filter(
        chat_session=chat_session
    ).select_related('user').order_by('-timestamp')

    # Session statistics
    message_stats = {
        'total_messages': messages.count(),
        'patient_messages': messages.filter(sender=chat_session.consultation.user).count(),
        'psychologist_messages': messages.filter(sender=chat_session.consultation.psychologist.user).count(),
    }

    context = {
        'chat_session': chat_session,
        'messages': messages,
        'logs': logs,
        'message_stats': message_stats,
    }

    return render(request, 'core/admin/chat_session_detail.html', context)

@admin_required()
def admin_logout(request):
    """Admin logout"""
    if hasattr(request.user, 'admin_role'):
        try:
            session = AdminSession.objects.filter(
                admin_role=request.user.admin_role,
                is_active=True
            ).latest('login_time')
            session.logout_time = timezone.now()
            session.is_active = False
            session.save()
        except AdminSession.DoesNotExist:
            pass
    
    logout(request)
    messages.success(request, 'You have been logged out successfully.')
    return redirect('custom_admin:login')

@admin_required(role='super_admin')
def approve_psychologist(request, psychologist_id):
    """Approve a psychologist"""
    psychologist = get_object_or_404(PsychologistProfile, pk=psychologist_id)
    
    if request.method == 'POST':
        action = request.POST.get('action')
        
        if action == 'approve':
            psychologist.approval_status = 'approved'
            psychologist.approved_by = request.user
            psychologist.approval_date = timezone.now()
            messages.success(request, f'Psychologist {psychologist.user.get_full_name()} has been approved.')
        elif action == 'reject':
            psychologist.approval_status = 'rejected'
            psychologist.rejection_reason = request.POST.get('reason', '')
            messages.success(request, f'Psychologist {psychologist.user.get_full_name()} has been rejected.')
        
        psychologist.save()
        return redirect('custom_admin:manage_psychologists')
    
    return render(request, 'core/admin/approve_psychologist.html', {
        'psychologist': psychologist,
    })
