from django.db.models import Q
from core.models import Notification


def notifications_context(request):
    """Add notification count to all templates (messages are now integrated into notifications)"""
    if request.user.is_authenticated:
        # Get unread notifications count (includes messages)
        unread_notifications_count = Notification.objects.filter(
            user=request.user,
            is_read=False
        ).count()

        return {
            'unread_notifications_count': unread_notifications_count,
            'unread_messages_count': 0,  # Deprecated - messages are now in notifications
        }

    return {
        'unread_notifications_count': 0,
        'unread_messages_count': 0,
    }
