from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.db.models import Count, Q, Avg
from django.utils import timezone
from datetime import datetime, timedelta

from accounts.models import UserProfile, PsychologistProfile
from consultation.models import Consultation, ConsultationRating
from payments.models import Payment
from core.models import Notification

@login_required
def user_dashboard(request):
    """Dashboard for regular users"""
    user = request.user
    
    # Redirect psychologists to their dashboard
    if hasattr(user, 'profile') and user.profile.is_psychologist:
        return redirect('core:psychologist_dashboard')
    
    # Redirect staff to admin dashboard
    if user.is_staff:
        return redirect('custom_admin:dashboard')
    
    # User statistics
    total_consultations = Consultation.objects.filter(user=user).count()

    # Get upcoming consultations count and limited list separately
    upcoming_consultations_count = Consultation.objects.filter(
        user=user,
        status__in=['confirmed', 'pending'],
        scheduled_date__gte=timezone.now().date()
    ).count()

    upcoming_consultations = Consultation.objects.filter(
        user=user,
        status__in=['confirmed', 'pending'],
        scheduled_date__gte=timezone.now().date()
    ).select_related('psychologist__user', 'consultation_type').order_by('scheduled_date', 'scheduled_start_time')[:5]

    completed_consultations = Consultation.objects.filter(
        user=user,
        status='completed'
    ).count()

    # Recent consultations
    recent_consultations = Consultation.objects.filter(
        user=user
    ).select_related('psychologist__user', 'consultation_type').order_by('-created_at')[:5]

    # Payment statistics - fix the aggregation
    from django.db.models import Sum
    total_spent = Payment.objects.filter(
        user=user,
        status='completed'
    ).aggregate(total=Sum('amount'))['total'] or 0
    
    # Notifications
    unread_notifications = Notification.objects.filter(
        user=user,
        is_read=False
    ).order_by('-created_at')[:5]
    
    # Favorite psychologists (based on ratings)
    favorite_psychologists = PsychologistProfile.objects.filter(
        consultations__user=user,
        consultations__rating__overall_rating__gte=4
    ).distinct()[:3]
    
    context = {
        'total_consultations': total_consultations,
        'upcoming_consultations': upcoming_consultations,
        'upcoming_consultations_count': upcoming_consultations_count,
        'completed_consultations': completed_consultations,
        'recent_consultations': recent_consultations,
        'total_spent': total_spent,
        'unread_notifications': unread_notifications,
        'favorite_psychologists': favorite_psychologists,
    }
    
    return render(request, 'dashboards/user_dashboard.html', context)

@login_required
def psychologist_dashboard(request):
    """Dashboard for psychologists"""
    user = request.user
    
    # Check if user is a psychologist
    try:
        psychologist_profile = user.psychologist_profile
    except:
        messages.error(request, _('You do not have a psychologist profile.'))
        return redirect('core:user_dashboard')
    
    # Redirect non-psychologists
    if not psychologist_profile:
        return redirect('core:user_dashboard')
    
    # Psychologist statistics
    total_consultations = Consultation.objects.filter(psychologist=psychologist_profile).count()
    
    today_consultations = Consultation.objects.filter(
        psychologist=psychologist_profile,
        scheduled_date=timezone.now().date(),
        status__in=['confirmed', 'in_progress']
    ).order_by('scheduled_start_time')
    
    upcoming_consultations = Consultation.objects.filter(
        psychologist=psychologist_profile,
        status__in=['confirmed', 'pending'],
        scheduled_date__gte=timezone.now().date()
    ).order_by('scheduled_date', 'scheduled_start_time')[:10]
    
    completed_this_month = Consultation.objects.filter(
        psychologist=psychologist_profile,
        status='completed',
        created_at__gte=timezone.now().replace(day=1)
    ).count()
    
    # Rating statistics
    ratings = ConsultationRating.objects.filter(
        consultation__psychologist=psychologist_profile
    )
    
    average_rating = ratings.aggregate(avg=Avg('overall_rating'))['avg'] or 0
    total_ratings = ratings.count()
    
    # Recent ratings and feedback
    recent_ratings = ratings.select_related(
        'consultation__user'
    ).order_by('-created_at')[:5]
    
    # Revenue statistics
    monthly_revenue = Payment.objects.filter(
        consultation__psychologist=psychologist_profile,
        status='completed',
        paid_at__gte=timezone.now().replace(day=1)
    ).aggregate(total=Count('amount'))['total'] or 0
    
    total_revenue = Payment.objects.filter(
        consultation__psychologist=psychologist_profile,
        status='completed'
    ).aggregate(total=Count('amount'))['total'] or 0
    
    # Pending consultations requiring attention
    pending_summaries = Consultation.objects.filter(
        psychologist=psychologist_profile,
        status='completed',
        summary__isnull=True
    ).count()
    
    # Weekly schedule overview
    week_start = timezone.now().date() - timedelta(days=timezone.now().weekday())
    weekly_consultations = []
    
    for i in range(7):
        day = week_start + timedelta(days=i)
        day_consultations = Consultation.objects.filter(
            psychologist=psychologist_profile,
            scheduled_date=day,
            status__in=['confirmed', 'pending', 'in_progress']
        ).count()
        
        weekly_consultations.append({
            'date': day,
            'day_name': day.strftime('%A'),
            'count': day_consultations
        })
    
    context = {
        'psychologist_profile': psychologist_profile,
        'total_consultations': total_consultations,
        'today_consultations': today_consultations,
        'upcoming_consultations': upcoming_consultations,
        'completed_this_month': completed_this_month,
        'average_rating': round(average_rating, 2) if average_rating else 0,
        'total_ratings': total_ratings,
        'recent_ratings': recent_ratings,
        'monthly_revenue': monthly_revenue,
        'total_revenue': total_revenue,
        'pending_summaries': pending_summaries,
        'weekly_consultations': weekly_consultations,
    }
    
    return render(request, 'dashboards/psychologist_dashboard.html', context)

@login_required
def dashboard_redirect(request):
    """Redirect users to appropriate dashboard based on their role"""
    user = request.user
    
    # Check if user is staff/admin
    if user.is_staff:
        return redirect('custom_admin:dashboard')
    
    # Check if user is psychologist
    try:
        if user.profile.is_psychologist and hasattr(user, 'psychologist_profile'):
            return redirect('core:psychologist_dashboard')
    except:
        pass
    
    # Default to user dashboard
    return redirect('core:user_dashboard')

@login_required
def notifications_view(request):
    """View all notifications for the user"""
    notifications = Notification.objects.filter(
        user=request.user
    ).order_by('-created_at')

    # Get statistics
    unread_count = notifications.filter(is_read=False).count()
    message_count = notifications.filter(notification_type='message').count()
    consultation_count = notifications.filter(notification_type='consultation').count()

    context = {
        'notifications': notifications,
        'unread_count': unread_count,
        'message_count': message_count,
        'consultation_count': consultation_count,
    }

    return render(request, 'dashboards/notifications.html', context)

@login_required
def mark_notification_read(request, notification_id):
    """Mark a specific notification as read"""
    try:
        notification = Notification.objects.get(
            id=notification_id,
            user=request.user
        )
        notification.is_read = True
        notification.save()

        if notification.url:
            return redirect(notification.url)
        
    except Notification.DoesNotExist:
        messages.error(request, _('Notification not found.'))
    
    return redirect('core:notifications')

@login_required
def check_notifications_api(request):
    """API endpoint to check for new notifications"""
    from django.http import JsonResponse

    unread_count = Notification.objects.filter(
        user=request.user,
        is_read=False
    ).count()

    # Get recent notifications (last 5 minutes)
    from datetime import timedelta
    recent_time = timezone.now() - timedelta(minutes=5)
    new_count = Notification.objects.filter(
        user=request.user,
        created_at__gte=recent_time,
        is_read=False
    ).count()

    return JsonResponse({
        'unread_count': unread_count,
        'new_count': new_count,
        'status': 'success'
    })

@login_required
def quick_stats_api(request):
    """API endpoint for quick dashboard stats"""
    from django.http import JsonResponse
    
    user = request.user
    
    if user.is_staff:
        # Admin stats
        stats = {
            'total_users': User.objects.count(),
            'pending_psychologists': PsychologistProfile.objects.filter(approval_status='pending').count(),
            'total_consultations': Consultation.objects.count(),
            'pending_reports': 0,  # UserReport model not available
        }
    elif hasattr(user, 'profile') and user.profile.is_psychologist:
        # Psychologist stats
        try:
            psychologist_profile = user.psychologist_profile
            stats = {
                'today_consultations': Consultation.objects.filter(
                    psychologist=psychologist_profile,
                    scheduled_date=timezone.now().date()
                ).count(),
                'upcoming_consultations': Consultation.objects.filter(
                    psychologist=psychologist_profile,
                    status__in=['confirmed', 'pending'],
                    scheduled_date__gte=timezone.now().date()
                ).count(),
                'pending_summaries': Consultation.objects.filter(
                    psychologist=psychologist_profile,
                    status='completed',
                    summary__isnull=True
                ).count(),
                'average_rating': ConsultationRating.objects.filter(
                    consultation__psychologist=psychologist_profile
                ).aggregate(avg=Avg('overall_rating'))['avg'] or 0,
            }
        except:
            stats = {'error': 'Psychologist profile not found'}
    else:
        # Regular user stats
        stats = {
            'total_consultations': Consultation.objects.filter(user=user).count(),
            'upcoming_consultations': Consultation.objects.filter(
                user=user,
                status__in=['confirmed', 'pending'],
                scheduled_date__gte=timezone.now().date()
            ).count(),
            'completed_consultations': Consultation.objects.filter(
                user=user,
                status='completed'
            ).count(),
            'unread_notifications': Notification.objects.filter(
                user=user,
                is_read=False
            ).count(),
        }
    
    return JsonResponse(stats)
