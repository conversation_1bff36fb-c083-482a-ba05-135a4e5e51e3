from django import forms
from django.utils.translation import gettext_lazy as _
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Submit, Row, Column, Field

from .models import ContactMessage

class ContactForm(forms.ModelForm):
    """Contact form for users to send messages"""
    
    class Meta:
        model = ContactMessage
        fields = ['name', 'email', 'subject', 'message']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('Your full name')
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': _('Your email address')
            }),
            'subject': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('Subject of your message')
            }),
            'message': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 5,
                'placeholder': _('Your message...')
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.layout = Layout(
            Row(
                Column('name', css_class='form-group col-md-6 mb-3'),
                Column('email', css_class='form-group col-md-6 mb-3'),
                css_class='form-row'
            ),
            Field('subject', css_class='form-group mb-3'),
            Field('message', css_class='form-group mb-3'),
            Submit('submit', _('Send Message'), css_class='btn btn-primary btn-lg')
        )
        
        # Add labels
        self.fields['name'].label = _('Full Name')
        self.fields['email'].label = _('Email Address')
        self.fields['subject'].label = _('Subject')
        self.fields['message'].label = _('Message')
        
        # Add validation
        self.fields['name'].required = True
        self.fields['email'].required = True
        self.fields['subject'].required = True
        self.fields['message'].required = True
    
    def clean_message(self):
        message = self.cleaned_data.get('message')
        if len(message) < 10:
            raise forms.ValidationError(_('Message must be at least 10 characters long.'))
        return message

class NewsletterSubscriptionForm(forms.Form):
    """Newsletter subscription form"""
    email = forms.EmailField(
        label=_('Email Address'),
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': _('Enter your email address')
        })
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.layout = Layout(
            Row(
                Column('email', css_class='form-group col-md-8'),
                Column(
                    Submit('subscribe', _('Subscribe'), css_class='btn btn-primary'),
                    css_class='form-group col-md-4 d-flex align-items-end'
                ),
                css_class='form-row'
            )
        )

class SearchForm(forms.Form):
    """Global search form"""
    query = forms.CharField(
        max_length=255,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': _('Search discussions, resources, psychologists...'),
            'autocomplete': 'off'
        }),
        label=''
    )
    
    category = forms.ChoiceField(
        choices=[
            ('all', _('All')),
            ('discussions', _('Discussions')),
            ('psychologists', _('Psychologists')),
            ('resources', _('Resources')),
            ('meetings', _('Meetings')),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'}),
        label=_('Category')
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'GET'
        self.helper.layout = Layout(
            Row(
                Column('query', css_class='form-group col-md-8'),
                Column('category', css_class='form-group col-md-2'),
                Column(
                    Submit('search', _('Search'), css_class='btn btn-primary'),
                    css_class='form-group col-md-2 d-flex align-items-end'
                ),
                css_class='form-row'
            )
        )
