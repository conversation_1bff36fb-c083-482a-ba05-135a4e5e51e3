from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from core.models import Notification


class Command(BaseCommand):
    help = 'Create test notifications for testing the notification system'

    def handle(self, *args, **options):
        # Get the first user (or create one)
        try:
            user = User.objects.first()
            if not user:
                self.stdout.write(self.style.ERROR('No users found. Please create a user first.'))
                return
            
            # Create test notifications
            notifications = [
                {
                    'title': 'Welcome to ECPI!',
                    'message': 'Thank you for joining our mental health platform. We\'re here to support you.',
                    'notification_type': 'info',
                },
                {
                    'title': 'Consultation Reminder',
                    'message': 'You have an upcoming consultation tomorrow at 2:00 PM.',
                    'notification_type': 'consultation',
                },
                {
                    'title': 'New Message',
                    'message': 'You have received a new message from <PERSON><PERSON>.',
                    'notification_type': 'message',
                },
            ]
            
            for notif_data in notifications:
                notification, created = Notification.objects.get_or_create(
                    user=user,
                    title=notif_data['title'],
                    defaults=notif_data
                )
                
                if created:
                    self.stdout.write(
                        self.style.SUCCESS(f'Created notification: {notification.title}')
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(f'Notification already exists: {notification.title}')
                    )
            
            self.stdout.write(
                self.style.SUCCESS(f'Successfully created test notifications for user: {user.username}')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error creating notifications: {e}')
            )
