# Generated by Django 5.2.4 on 2025-07-27 11:01

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AdminRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('super_admin', 'Super Admin'), ('hr_manager', 'HR Manager')], max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_admin_roles', to=settings.AUTH_USER_MODEL)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='admin_role', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Admin Role',
                'verbose_name_plural': 'Admin Roles',
            },
        ),
        migrations.CreateModel(
            name='AdminSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('login_time', models.DateTimeField(auto_now_add=True)),
                ('logout_time', models.DateTimeField(blank=True, null=True)),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField()),
                ('is_active', models.BooleanField(default=True)),
                ('admin_role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.adminrole')),
            ],
            options={
                'verbose_name': 'Admin Session',
                'verbose_name_plural': 'Admin Sessions',
                'ordering': ['-login_time'],
            },
        ),
    ]
