from django.db import models
from django.contrib.auth.models import User
from django.utils.translation import gettext_lazy as _

class SiteSettings(models.Model):
    """Global site settings"""
    site_name = models.CharField(max_length=100, default="ECPI")
    site_description = models.TextField(default="Ethiopian Psychological Consultation Platform")
    contact_email = models.EmailField(default="<EMAIL>")
    contact_phone = models.CharField(max_length=20, default="+251-11-XXX-XXXX")
    address = models.TextField(default="Addis Ababa, Ethiopia")

    # Social media links
    facebook_url = models.URLField(blank=True)
    twitter_url = models.URLField(blank=True)
    instagram_url = models.URLField(blank=True)
    linkedin_url = models.URLField(blank=True)

    # Emergency contact info
    emergency_hotline = models.CharField(max_length=20, blank=True)
    crisis_text_line = models.CharField(max_length=20, blank=True)

    # Site statistics
    total_users = models.PositiveIntegerField(default=0)
    total_psychologists = models.PositiveIntegerField(default=0)
    total_consultations = models.PositiveIntegerField(default=0)
    total_resources = models.PositiveIntegerField(default=0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("Site Settings")
        verbose_name_plural = _("Site Settings")

    def __str__(self):
        return self.site_name

class FAQ(models.Model):
    """Frequently Asked Questions"""
    question = models.CharField(max_length=255)
    answer = models.TextField()
    category = models.CharField(max_length=100, choices=[
        ('general', _('General')),
        ('consultation', _('Consultation')),
        ('payment', _('Payment')),
        ('technical', _('Technical')),
        ('mental_health', _('Mental Health')),
    ], default='general')
    is_active = models.BooleanField(default=True)
    order = models.PositiveIntegerField(default=0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['category', 'order', 'question']
        verbose_name = _("FAQ")
        verbose_name_plural = _("FAQs")

    def __str__(self):
        return self.question

class ContactMessage(models.Model):
    """Contact form messages"""
    name = models.CharField(max_length=100)
    email = models.EmailField()
    subject = models.CharField(max_length=200)
    message = models.TextField()
    is_read = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = _("Contact Message")
        verbose_name_plural = _("Contact Messages")

    def __str__(self):
        return f"{self.name} - {self.subject}"

class Notification(models.Model):
    """User notifications"""
    NOTIFICATION_TYPES = [
        ('info', _('Information')),
        ('success', _('Success')),
        ('warning', _('Warning')),
        ('error', _('Error')),
        ('consultation', _('Consultation')),
        ('message', _('Message')),
        ('meeting', _('Meeting')),
        ('system', _('System')),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    title = models.CharField(max_length=200)
    message = models.TextField()
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES, default='info')
    is_read = models.BooleanField(default=False)
    url = models.URLField(blank=True, help_text=_("Optional URL to redirect when notification is clicked"))

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = _("Notification")
        verbose_name_plural = _("Notifications")

    def __str__(self):
        return f"{self.user.username} - {self.title}"

# Import admin models to make them available
from .admin_models import AdminRole, AdminSession
