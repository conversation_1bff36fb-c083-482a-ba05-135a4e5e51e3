from django import template

register = template.Library()

@register.filter
def mul(value, arg):
    """Multiply the value by the argument."""
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def div(value, arg):
    """Divide the value by the argument."""
    try:
        return float(value) / float(arg)
    except (ValueError, TypeError, ZeroDivisionError):
        return 0

@register.filter
def add_percentage(value, percentage):
    """Add a percentage to the value."""
    try:
        return float(value) * (1 + float(percentage) / 100)
    except (ValueError, TypeError):
        return value

@register.filter
def subtract_percentage(value, percentage):
    """Subtract a percentage from the value."""
    try:
        return float(value) * (1 - float(percentage) / 100)
    except (ValueError, TypeError):
        return value
