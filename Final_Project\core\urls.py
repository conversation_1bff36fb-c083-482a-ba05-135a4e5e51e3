from django.urls import path
from . import views, dashboard_views, admin_views

app_name = 'core'

urlpatterns = [
    path('', views.HomeView.as_view(), name='home'),
    path('about/', views.AboutView.as_view(), name='about'),
    path('contact/', views.ContactView.as_view(), name='contact'),
    path('faq/', views.FAQView.as_view(), name='faq'),
    path('terms/', views.TermsView.as_view(), name='terms'),
    path('privacy/', views.PrivacyView.as_view(), name='privacy'),
    path('chatbot/response/', views.ChatbotResponseView.as_view(), name='chatbot_response'),

    # User dashboards
    path('dashboard/', dashboard_views.dashboard_redirect, name='dashboard'),
    path('dashboard/user/', dashboard_views.user_dashboard, name='user_dashboard'),
    path('dashboard/psychologist/', dashboard_views.psychologist_dashboard, name='psychologist_dashboard'),
    path('notifications/', dashboard_views.notifications_view, name='notifications'),
    path('notifications/<int:notification_id>/read/', dashboard_views.mark_notification_read, name='mark_notification_read'),
    path('api/quick-stats/', dashboard_views.quick_stats_api, name='quick_stats_api'),
    path('api/notifications/check/', dashboard_views.check_notifications_api, name='check_notifications_api'),

    # Note: Custom admin URLs are now handled by /admin/ in main urls.py
]
