from django.shortcuts import render, redirect
from django.contrib import messages
from django.views.generic import TemplateView, FormView
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import json

from .models import SiteSettings, FAQ, ContactMessage, Notification
from .forms import ContactForm

class HomeView(TemplateView):
    """Home page view"""
    template_name = 'home/home.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get site settings for statistics
        try:
            site_settings = SiteSettings.objects.first()
            if site_settings:
                context['stats'] = {
                    'total_users': site_settings.total_users,
                    'total_psychologists': site_settings.total_psychologists,
                    'total_consultations': site_settings.total_consultations,
                    'total_resources': site_settings.total_resources,
                }
        except SiteSettings.DoesNotExist:
            context['stats'] = {
                'total_users': '500+',
                'total_psychologists': '50+',
                'total_consultations': '1000+',
                'total_resources': '200+',
            }

        return context

class AboutView(TemplateView):
    """About page view"""
    template_name = 'home/about.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get site settings
        try:
            context['site_settings'] = SiteSettings.objects.first()
        except SiteSettings.DoesNotExist:
            context['site_settings'] = None

        return context

class ContactView(FormView):
    """Contact page view"""
    template_name = 'home/contact.html'
    form_class = ContactForm
    success_url = '/contact/'

    def form_valid(self, form):
        # Save contact message
        contact_message = ContactMessage.objects.create(
            name=form.cleaned_data['name'],
            email=form.cleaned_data['email'],
            subject=form.cleaned_data['subject'],
            message=form.cleaned_data['message']
        )

        messages.success(
            self.request,
            _('Thank you for your message. We will get back to you soon!')
        )

        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get site settings for contact info
        try:
            context['site_settings'] = SiteSettings.objects.first()
        except SiteSettings.DoesNotExist:
            context['site_settings'] = None

        return context

class FAQView(TemplateView):
    """FAQ page view"""
    template_name = 'home/faq.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get FAQs grouped by category
        faqs = FAQ.objects.filter(is_active=True)
        faq_categories = {}

        for faq in faqs:
            if faq.category not in faq_categories:
                faq_categories[faq.category] = []
            faq_categories[faq.category].append(faq)

        context['faq_categories'] = faq_categories
        return context

class TermsView(TemplateView):
    """Terms of Service page view"""
    template_name = 'home/terms.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _('Terms of Service')
        return context

class PrivacyView(TemplateView):
    """Privacy Policy page view"""
    template_name = 'home/privacy.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _('Privacy Policy')
        return context

@method_decorator(csrf_exempt, name='dispatch')
class ChatbotResponseView(TemplateView):
    """Handle chatbot responses"""

    def post(self, request, *args, **kwargs):
        try:
            data = json.loads(request.body)
            message = data.get('message', '').lower().strip()

            # Simple rule-based responses
            response = self.get_chatbot_response(message)

            return JsonResponse({'response': response})

        except Exception as e:
            return JsonResponse({
                'response': "I'm sorry, I'm having trouble understanding. Could you please rephrase your question?"
            })

    def get_chatbot_response(self, message):
        """Generate chatbot response based on message"""

        # Crisis keywords - highest priority
        crisis_keywords = ['suicide', 'kill myself', 'end my life', 'self harm', 'hurt myself']
        if any(keyword in message for keyword in crisis_keywords):
            return ("I'm very concerned about you. Please reach out for immediate help:\n"
                   "• Emergency: 911 or local emergency services\n"
                   "• Crisis Text Line: Text HOME to 741741\n"
                   "• National Suicide Prevention Lifeline: 988\n"
                   "Your life has value and help is available.")

        # Mental health topics
        if any(word in message for word in ['anxiety', 'anxious', 'worried', 'panic']):
            return ("Anxiety is very common and treatable. Some helpful techniques include:\n"
                   "• Deep breathing exercises\n"
                   "• Mindfulness and meditation\n"
                   "• Regular exercise\n"
                   "• Talking to a mental health professional\n"
                   "Would you like to book a consultation with one of our psychologists?")

        if any(word in message for word in ['depression', 'depressed', 'sad', 'hopeless']):
            return ("I understand you're going through a difficult time. Depression is treatable, and you don't have to face it alone.\n"
                   "Consider:\n"
                   "• Speaking with a mental health professional\n"
                   "• Maintaining daily routines\n"
                   "• Staying connected with supportive people\n"
                   "• Regular physical activity\n"
                   "Our certified psychologists are here to help.")

        if any(word in message for word in ['stress', 'stressed', 'overwhelmed']):
            return ("Stress management is important for your wellbeing. Try these techniques:\n"
                   "• Deep breathing or meditation\n"
                   "• Regular exercise\n"
                   "• Adequate sleep (7-9 hours)\n"
                   "• Time management and prioritization\n"
                   "• Taking breaks and practicing self-care")

        # Platform features
        if any(word in message for word in ['psychologist', 'therapist', 'counselor', 'consultation']):
            return ("You can browse and book consultations with our certified psychologists. We offer:\n"
                   "• Free initial consultations for new users\n"
                   "• Paid sessions with experienced professionals\n"
                   "• Both English and Amharic speaking psychologists\n"
                   "• Secure, private sessions\n"
                   "Would you like to see available psychologists?")

        if any(word in message for word in ['discussion', 'community', 'forum', 'talk']):
            return ("Our community discussions are a safe space to:\n"
                   "• Share your experiences\n"
                   "• Get support from others who understand\n"
                   "• Learn coping strategies\n"
                   "• Connect with peers\n"
                   "All discussions are moderated to ensure a supportive environment.")

        # General help
        if any(word in message for word in ['help', 'support', 'assistance']):
            return ("I'm here to help! I can provide information about:\n"
                   "• Mental health topics and coping strategies\n"
                   "• Our platform features and services\n"
                   "• Connecting you with professional help\n"
                   "• Community resources and discussions\n"
                   "What specific area would you like to know more about?")

        # Greetings
        if any(word in message for word in ['hello', 'hi', 'hey', 'good morning', 'good afternoon']):
            return ("Hello! I'm your mental health assistant. I'm here to provide support and information.\n"
                   "How are you feeling today? Is there anything specific I can help you with?")

        # Default response
        return ("Thank you for reaching out. While I can provide general information and support, "
               "I'd recommend speaking with one of our certified psychologists for personalized help. "
               "You can also join our community discussions or explore our mental health resources. "
               "How can I best assist you today?")

# Utility function to create notifications
def create_notification(user, title, message, notification_type='info', url=''):
    """Create a notification for a user"""
    return Notification.objects.create(
        user=user,
        title=title,
        message=message,
        notification_type=notification_type,
        url=url
    )

def create_message_notification(recipient, sender, message_content, consultation=None):
    """Create a notification for a new message"""
    if consultation:
        title = f"New message from {sender.get_full_name() or sender.username}"
        message = f"Consultation message: {message_content[:100]}..."
        url = f"/consultation/consultation/{consultation.pk}/chat-room/"
    else:
        title = f"New message from {sender.get_full_name() or sender.username}"
        message = message_content[:100] + "..." if len(message_content) > 100 else message_content
        url = "/chat/"

    return create_notification(
        user=recipient,
        title=title,
        message=message,
        notification_type='message',
        url=url
    )

def create_consultation_notification(user, title, message, consultation, notification_type='consultation'):
    """Create a consultation-related notification"""
    url = f"/consultation/consultation/{consultation.pk}/"
    return create_notification(
        user=user,
        title=title,
        message=message,
        notification_type=notification_type,
        url=url
    )
