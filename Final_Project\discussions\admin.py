from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django.urls import reverse
from .models import DiscussionCategory, Discussion, DiscussionReply, DiscussionVote, DiscussionView

@admin.register(DiscussionCategory)
class DiscussionCategoryAdmin(admin.ModelAdmin):
    """Discussion Category admin"""
    list_display = ('name', 'discussion_count', 'is_active', 'requires_approval', 'order', 'created_at')
    list_filter = ('is_active', 'requires_approval', 'created_at')
    search_fields = ('name', 'description')
    filter_horizontal = ('moderators',)
    ordering = ['order', 'name']

    def discussion_count(self, obj):
        return obj.discussions.count()
    discussion_count.short_description = _('Discussions')

@admin.register(Discussion)
class DiscussionAdmin(admin.ModelAdmin):
    """Discussion admin with comprehensive management"""
    list_display = ('title', 'category', 'author', 'status', 'is_pinned', 'is_locked', 'reply_count', 'vote_score', 'created_at')
    list_filter = ('status', 'is_pinned', 'is_locked', 'is_anonymous', 'category', 'created_at')
    search_fields = ('title', 'content', 'tags', 'author__username')
    prepopulated_fields = {'slug': ('title',)}
    readonly_fields = ('id', 'view_count', 'reply_count', 'vote_score', 'created_at', 'updated_at', 'last_activity')
    date_hierarchy = 'created_at'

    fieldsets = (
        (_('Basic Information'), {
            'fields': ('title', 'slug', 'content', 'category', 'tags', 'author')
        }),
        (_('Status & Moderation'), {
            'fields': ('status', 'is_pinned', 'is_locked', 'is_anonymous')
        }),
        (_('Statistics'), {
            'fields': ('view_count', 'reply_count', 'vote_score'),
            'classes': ('collapse',)
        }),
        (_('Timestamps'), {
            'fields': ('id', 'created_at', 'updated_at', 'last_activity'),
            'classes': ('collapse',)
        }),
    )

    actions = ['pin_discussions', 'unpin_discussions', 'lock_discussions', 'unlock_discussions', 'close_discussions']

    def pin_discussions(self, request, queryset):
        """Pin selected discussions"""
        updated = queryset.update(is_pinned=True)
        self.message_user(request, f'{updated} discussion(s) pinned.')
    pin_discussions.short_description = _('Pin selected discussions')

    def unpin_discussions(self, request, queryset):
        """Unpin selected discussions"""
        updated = queryset.update(is_pinned=False)
        self.message_user(request, f'{updated} discussion(s) unpinned.')
    unpin_discussions.short_description = _('Unpin selected discussions')

    def lock_discussions(self, request, queryset):
        """Lock selected discussions"""
        updated = queryset.update(is_locked=True)
        self.message_user(request, f'{updated} discussion(s) locked.')
    lock_discussions.short_description = _('Lock selected discussions')

    def unlock_discussions(self, request, queryset):
        """Unlock selected discussions"""
        updated = queryset.update(is_locked=False)
        self.message_user(request, f'{updated} discussion(s) unlocked.')
    unlock_discussions.short_description = _('Unlock selected discussions')

    def close_discussions(self, request, queryset):
        """Close selected discussions"""
        updated = queryset.update(status='closed')
        self.message_user(request, f'{updated} discussion(s) closed.')
    close_discussions.short_description = _('Close selected discussions')

@admin.register(DiscussionReply)
class DiscussionReplyAdmin(admin.ModelAdmin):
    """Discussion Reply admin"""
    list_display = ('get_discussion', 'author', 'status', 'is_solution', 'vote_score', 'created_at')
    list_filter = ('status', 'is_solution', 'is_anonymous', 'created_at')
    search_fields = ('content', 'author__username', 'discussion__title')
    readonly_fields = ('id', 'vote_score', 'created_at', 'updated_at')
    date_hierarchy = 'created_at'

    fieldsets = (
        (_('Basic Information'), {
            'fields': ('discussion', 'content', 'author', 'parent')
        }),
        (_('Status & Moderation'), {
            'fields': ('status', 'is_solution', 'is_anonymous')
        }),
        (_('Statistics'), {
            'fields': ('vote_score',),
            'classes': ('collapse',)
        }),
        (_('Timestamps'), {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_as_solution', 'unmark_as_solution', 'hide_replies', 'show_replies']

    def get_discussion(self, obj):
        """Link to discussion"""
        url = reverse('admin:discussions_discussion_change', args=[obj.discussion.pk])
        return format_html('<a href="{}">{}</a>', url, obj.discussion.title[:50])
    get_discussion.short_description = _('Discussion')
    get_discussion.admin_order_field = 'discussion'

    def mark_as_solution(self, request, queryset):
        """Mark selected replies as solutions"""
        updated = queryset.update(is_solution=True)
        self.message_user(request, f'{updated} reply(ies) marked as solution.')
    mark_as_solution.short_description = _('Mark as solution')

    def unmark_as_solution(self, request, queryset):
        """Unmark selected replies as solutions"""
        updated = queryset.update(is_solution=False)
        self.message_user(request, f'{updated} reply(ies) unmarked as solution.')
    unmark_as_solution.short_description = _('Unmark as solution')

    def hide_replies(self, request, queryset):
        """Hide selected replies"""
        updated = queryset.update(status='hidden')
        self.message_user(request, f'{updated} reply(ies) hidden.')
    hide_replies.short_description = _('Hide selected replies')

    def show_replies(self, request, queryset):
        """Show selected replies"""
        updated = queryset.update(status='published')
        self.message_user(request, f'{updated} reply(ies) shown.')
    show_replies.short_description = _('Show selected replies')

@admin.register(DiscussionVote)
class DiscussionVoteAdmin(admin.ModelAdmin):
    """Discussion Vote admin for analytics"""
    list_display = ('user', 'get_target', 'vote', 'created_at')
    list_filter = ('vote', 'created_at')
    search_fields = ('user__username', 'discussion__title', 'reply__content')
    readonly_fields = ('user', 'vote', 'discussion', 'reply', 'created_at', 'updated_at')
    date_hierarchy = 'created_at'

    def get_target(self, obj):
        """Show what was voted on"""
        if obj.discussion:
            return f"Discussion: {obj.discussion.title[:50]}"
        elif obj.reply:
            return f"Reply: {obj.reply.content[:50]}"
        return "Unknown"
    get_target.short_description = _('Target')

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

@admin.register(DiscussionView)
class DiscussionViewAdmin(admin.ModelAdmin):
    """Discussion View admin for analytics"""
    list_display = ('discussion', 'user', 'ip_address', 'viewed_at')
    list_filter = ('viewed_at', 'discussion__category')
    search_fields = ('discussion__title', 'user__username', 'ip_address')
    readonly_fields = ('discussion', 'user', 'ip_address', 'user_agent', 'viewed_at')
    date_hierarchy = 'viewed_at'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False
