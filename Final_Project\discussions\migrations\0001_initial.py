# Generated by Django 5.2.4 on 2025-07-25 18:17

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DiscussionCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('icon', models.CharField(blank=True, help_text='FontAwesome icon class', max_length=50)),
                ('color', models.CharField(default='#007bff', help_text='Hex color code', max_length=7)),
                ('is_active', models.BooleanField(default=True)),
                ('order', models.PositiveIntegerField(default=0)),
                ('requires_approval', models.BooleanField(default=False, help_text='New discussions require approval')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('moderators', models.ManyToManyField(blank=True, related_name='moderated_categories', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Discussion Category',
                'verbose_name_plural': 'Discussion Categories',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Discussion',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200)),
                ('slug', models.SlugField(max_length=200, unique=True)),
                ('content', models.TextField()),
                ('tags', models.CharField(blank=True, help_text='Comma-separated tags', max_length=500)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('published', 'Published'), ('closed', 'Closed'), ('archived', 'Archived')], default='published', max_length=20)),
                ('is_pinned', models.BooleanField(default=False)),
                ('is_locked', models.BooleanField(default=False)),
                ('is_anonymous', models.BooleanField(default=False, help_text='Hide author name')),
                ('view_count', models.PositiveIntegerField(default=0)),
                ('reply_count', models.PositiveIntegerField(default=0)),
                ('vote_score', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_activity', models.DateTimeField(auto_now_add=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='discussions', to=settings.AUTH_USER_MODEL)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='discussions', to='discussions.discussioncategory')),
            ],
            options={
                'verbose_name': 'Discussion',
                'verbose_name_plural': 'Discussions',
                'ordering': ['-last_activity'],
            },
        ),
        migrations.CreateModel(
            name='DiscussionReply',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('content', models.TextField()),
                ('status', models.CharField(choices=[('published', 'Published'), ('hidden', 'Hidden'), ('deleted', 'Deleted')], default='published', max_length=20)),
                ('is_anonymous', models.BooleanField(default=False)),
                ('vote_score', models.IntegerField(default=0)),
                ('is_solution', models.BooleanField(default=False, help_text='Mark as solution to the discussion')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='discussion_replies', to=settings.AUTH_USER_MODEL)),
                ('discussion', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='replies', to='discussions.discussion')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='discussions.discussionreply')),
            ],
            options={
                'verbose_name': 'Discussion Reply',
                'verbose_name_plural': 'Discussion Replies',
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='DiscussionView',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField(blank=True)),
                ('viewed_at', models.DateTimeField(auto_now_add=True)),
                ('discussion', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='views', to='discussions.discussion')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Discussion View',
                'verbose_name_plural': 'Discussion Views',
                'unique_together': {('discussion', 'user', 'ip_address')},
            },
        ),
        migrations.CreateModel(
            name='DiscussionVote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('vote', models.SmallIntegerField(choices=[(1, 'Upvote'), (-1, 'Downvote')])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('discussion', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='votes', to='discussions.discussion')),
                ('reply', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='votes', to='discussions.discussionreply')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='discussion_votes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Discussion Vote',
                'verbose_name_plural': 'Discussion Votes',
                'constraints': [models.CheckConstraint(condition=models.Q(('discussion__isnull', False), ('reply__isnull', False), _connector='OR'), name='vote_has_target'), models.UniqueConstraint(condition=models.Q(('discussion__isnull', False)), fields=('user', 'discussion'), name='unique_discussion_vote'), models.UniqueConstraint(condition=models.Q(('reply__isnull', False)), fields=('user', 'reply'), name='unique_reply_vote')],
            },
        ),
    ]
