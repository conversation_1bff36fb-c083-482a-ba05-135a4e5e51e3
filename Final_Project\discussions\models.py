from django.db import models
from django.contrib.auth.models import User
from django.utils.translation import gettext_lazy as _
from django.urls import reverse
import uuid

class DiscussionCategory(models.Model):
    """Categories for organizing discussions"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    icon = models.CharField(max_length=50, blank=True, help_text=_("FontAwesome icon class"))
    color = models.CharField(max_length=7, default='#007bff', help_text=_("Hex color code"))
    is_active = models.BooleanField(default=True)
    order = models.PositiveIntegerField(default=0)

    # Moderation settings
    requires_approval = models.BooleanField(default=False, help_text=_("New discussions require approval"))
    moderators = models.ManyToManyField(User, blank=True, related_name='moderated_categories')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("Discussion Category")
        verbose_name_plural = _("Discussion Categories")
        ordering = ['order', 'name']

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('discussions:category_detail', kwargs={'pk': self.pk})

class Discussion(models.Model):
    """Main discussion/forum topic"""
    STATUS_CHOICES = [
        ('draft', _('Draft')),
        ('published', _('Published')),
        ('closed', _('Closed')),
        ('archived', _('Archived')),
    ]

    # Basic Information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=200)
    slug = models.SlugField(max_length=200, unique=True)
    content = models.TextField()

    # Categorization
    category = models.ForeignKey(DiscussionCategory, on_delete=models.CASCADE, related_name='discussions')
    tags = models.CharField(max_length=500, blank=True, help_text=_("Comma-separated tags"))

    # Author and Status
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='discussions')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='published')

    # Moderation
    is_pinned = models.BooleanField(default=False)
    is_locked = models.BooleanField(default=False)
    is_anonymous = models.BooleanField(default=False, help_text=_("Hide author name"))

    # Statistics
    view_count = models.PositiveIntegerField(default=0)
    reply_count = models.PositiveIntegerField(default=0)
    vote_score = models.IntegerField(default=0)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_activity = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _("Discussion")
        verbose_name_plural = _("Discussions")
        ordering = ['-last_activity']

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        return reverse('discussions:discussion_detail', kwargs={'slug': self.slug})

class DiscussionReply(models.Model):
    """Replies to discussions"""
    STATUS_CHOICES = [
        ('published', _('Published')),
        ('hidden', _('Hidden')),
        ('deleted', _('Deleted')),
    ]

    # Basic Information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    discussion = models.ForeignKey(Discussion, on_delete=models.CASCADE, related_name='replies')
    content = models.TextField()

    # Author and Status
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='discussion_replies')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='published')
    is_anonymous = models.BooleanField(default=False)

    # Threading (for nested replies)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, blank=True, null=True, related_name='children')

    # Statistics
    vote_score = models.IntegerField(default=0)

    # Moderation
    is_solution = models.BooleanField(default=False, help_text=_("Mark as solution to the discussion"))

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("Discussion Reply")
        verbose_name_plural = _("Discussion Replies")
        ordering = ['created_at']

    def __str__(self):
        return f"Reply to {self.discussion.title}"

class DiscussionVote(models.Model):
    """Voting system for discussions and replies"""
    VOTE_CHOICES = [
        (1, _('Upvote')),
        (-1, _('Downvote')),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='discussion_votes')
    vote = models.SmallIntegerField(choices=VOTE_CHOICES)

    # Polymorphic voting (can vote on discussions or replies)
    discussion = models.ForeignKey(Discussion, on_delete=models.CASCADE, blank=True, null=True, related_name='votes')
    reply = models.ForeignKey(DiscussionReply, on_delete=models.CASCADE, blank=True, null=True, related_name='votes')

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("Discussion Vote")
        verbose_name_plural = _("Discussion Votes")
        constraints = [
            models.CheckConstraint(
                check=models.Q(discussion__isnull=False) | models.Q(reply__isnull=False),
                name='vote_has_target'
            ),
            models.UniqueConstraint(
                fields=['user', 'discussion'],
                condition=models.Q(discussion__isnull=False),
                name='unique_discussion_vote'
            ),
            models.UniqueConstraint(
                fields=['user', 'reply'],
                condition=models.Q(reply__isnull=False),
                name='unique_reply_vote'
            ),
        ]

    def __str__(self):
        target = self.discussion or self.reply
        vote_type = "Upvote" if self.vote == 1 else "Downvote"
        return f"{self.user.username} {vote_type} on {target}"

class DiscussionView(models.Model):
    """Track discussion views for analytics"""
    discussion = models.ForeignKey(Discussion, on_delete=models.CASCADE, related_name='views')
    user = models.ForeignKey(User, on_delete=models.CASCADE, blank=True, null=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)
    viewed_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _("Discussion View")
        verbose_name_plural = _("Discussion Views")
        unique_together = ['discussion', 'user', 'ip_address']

    def __str__(self):
        return f"{self.discussion.title} - {self.user or self.ip_address}"
