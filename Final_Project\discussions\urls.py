from django.urls import path
from . import views

app_name = 'discussions'

urlpatterns = [
    path('', views.DiscussionListView.as_view(), name='discussion_list'),
    path('create/', views.CreateDiscussionView.as_view(), name='create_discussion'),
    path('<int:pk>/', views.DiscussionDetailView.as_view(), name='discussion_detail'),
    path('<int:pk>/edit/', views.EditDiscussionView.as_view(), name='edit_discussion'),
    path('<int:pk>/delete/', views.DeleteDiscussionView.as_view(), name='delete_discussion'),
    path('<int:pk>/reply/', views.CreateReplyView.as_view(), name='create_reply'),
    path('reply/<int:pk>/edit/', views.EditReplyView.as_view(), name='edit_reply'),
    path('reply/<int:pk>/delete/', views.DeleteReplyView.as_view(), name='delete_reply'),
    path('<int:pk>/report/', views.ReportDiscussionView.as_view(), name='report_discussion'),
    path('category/<slug:slug>/', views.CategoryDiscussionListView.as_view(), name='category_discussions'),
]
