from django.shortcuts import render, get_object_or_404
from django.views.generic import Template<PERSON>iew, ListView, CreateView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.urls import reverse_lazy
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from .models import Discussion, DiscussionCategory, DiscussionReply

class DiscussionListView(ListView):
    model = Discussion
    template_name = 'discussions/discussion_list.html'
    context_object_name = 'discussions'
    paginate_by = 20

    def get_queryset(self):
        return Discussion.objects.filter(status='published').select_related('author', 'category').order_by('-last_activity')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = DiscussionCategory.objects.filter(is_active=True).order_by('order', 'name')
        context['pinned_discussions'] = Discussion.objects.filter(is_pinned=True, status='published').order_by('-created_at')[:5]
        context['total_discussions'] = Discussion.objects.filter(status='published').count()
        context['total_replies'] = DiscussionReply.objects.filter(status='published').count()
        return context

class CreateDiscussionView(LoginRequiredMixin, CreateView):
    model = Discussion
    template_name = 'discussions/discussion_form.html'
    fields = ['title', 'category', 'content', 'tags', 'is_anonymous']
    success_url = reverse_lazy('discussions:discussion_list')

    def form_valid(self, form):
        form.instance.author = self.request.user
        form.instance.slug = self.generate_slug(form.instance.title)
        messages.success(self.request, _('Your discussion has been created successfully!'))
        return super().form_valid(form)

    def generate_slug(self, title):
        from django.utils.text import slugify
        import uuid
        base_slug = slugify(title)[:50]
        return f"{base_slug}-{str(uuid.uuid4())[:8]}"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = DiscussionCategory.objects.filter(is_active=True).order_by('order', 'name')
        return context

class DiscussionDetailView(TemplateView):
    template_name = 'discussions/discussion_detail.html'

class EditDiscussionView(TemplateView):
    template_name = 'discussions/discussion_form.html'

class DeleteDiscussionView(TemplateView):
    template_name = 'discussions/discussion_confirm_delete.html'

class CreateReplyView(TemplateView):
    template_name = 'discussions/reply_form.html'

class EditReplyView(TemplateView):
    template_name = 'discussions/reply_form.html'

class DeleteReplyView(TemplateView):
    template_name = 'discussions/reply_confirm_delete.html'

class ReportDiscussionView(TemplateView):
    template_name = 'discussions/discussion_report.html'

class CategoryDiscussionListView(ListView):
    template_name = 'discussions/discussion_list.html'
    context_object_name = 'discussions'

    def get_queryset(self):
        return []
