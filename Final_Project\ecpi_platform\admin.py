from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _

# Customize admin site
admin.site.site_header = _("ECPI Administration")
admin.site.site_title = _("ECPI Admin")
admin.site.index_title = _("Welcome to ECPI Administration")

# Add custom admin index template
class ECPIAdminSite(admin.AdminSite):
    site_header = _("ECPI Administration")
    site_title = _("ECPI Admin")
    index_title = _("Welcome to ECPI Administration")
    
    def index(self, request, extra_context=None):
        """
        Display the main admin index page, which lists all of the installed
        apps that have been registered in this site.
        """
        extra_context = extra_context or {}
        
        # Add custom dashboard link
        extra_context['dashboard_url'] = reverse('custom_admin:dashboard')
        
        return super().index(request, extra_context)

# Replace the default admin site
admin_site = ECPIAdminSite(name='admin')

# Copy all registered models to our custom admin site
for model, model_admin in admin.site._registry.items():
    admin_site.register(model, model_admin.__class__)

# Replace the default admin site
admin.site = admin_site
