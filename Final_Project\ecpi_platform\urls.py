"""
URL configuration for ecpi_platform project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.conf.urls.i18n import i18n_patterns
from django.shortcuts import redirect

urlpatterns = [
    # Django Admin (moved to different path)
    path('django-admin/', admin.site.urls),

    # Custom Admin System (primary admin interface)
    path('admin/', include('core.admin_urls')),

    # Internationalization
    path('i18n/', include('django.conf.urls.i18n')),
]

# Add i18n patterns for multilingual support
urlpatterns += i18n_patterns(
    # Core app (home, about, contact, etc.)
    path('', include('core.urls')),

    # User accounts (login, register, profile, etc.)
    path('accounts/', include('accounts.urls')),

    # Discussions and forums
    path('discussions/', include('discussions.urls')),

    # Consultation system
    path('consultation/', include('consultation.urls')),

    # Chat and messaging
    path('chat/', include('chat.urls')),

    # Meetings and seminars
    path('meetings/', include('meetings.urls')),

    # Resources (videos, audio, documents)
    path('resources/', include('resources.urls')),

    # Payment system
    path('payments/', include('payments.urls')),

    prefix_default_language=False,
)

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
