from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import Meeting, MeetingRegistration, MeetingResource, MeetingFeedback

@admin.register(Meeting)
class MeetingAdmin(admin.ModelAdmin):
    """Meeting admin with comprehensive management"""
    list_display = ('title', 'meeting_type', 'organizer', 'start_datetime', 'status', 'is_featured', 'registration_count', 'max_participants')
    list_filter = ('meeting_type', 'status', 'is_featured', 'is_online', 'is_free', 'start_datetime')
    search_fields = ('title', 'description', 'organizer__username', 'venue_name')
    prepopulated_fields = {'slug': ('title',)}
    filter_horizontal = ('facilitators',)
    readonly_fields = ('id', 'registration_count', 'attendance_count', 'created_at', 'updated_at')
    date_hierarchy = 'start_datetime'

    fieldsets = (
        (_('Basic Information'), {
            'fields': ('title', 'slug', 'description', 'meeting_type', 'organizer', 'facilitators')
        }),
        (_('Schedule'), {
            'fields': ('start_datetime', 'end_datetime', 'timezone')
        }),
        (_('Registration & Capacity'), {
            'fields': ('registration_required', 'registration_deadline', 'max_participants', 'registration_count')
        }),
        (_('Pricing'), {
            'fields': ('is_free', 'price', 'currency')
        }),
        (_('Meeting Access'), {
            'fields': ('meeting_url', 'meeting_id', 'meeting_password'),
            'classes': ('collapse',)
        }),
        (_('Location'), {
            'fields': ('is_online', 'venue_name', 'venue_address'),
            'classes': ('collapse',)
        }),
        (_('Content'), {
            'fields': ('featured_image', 'agenda', 'prerequisites')
        }),
        (_('Status & Visibility'), {
            'fields': ('status', 'is_featured', 'is_recurring')
        }),
        (_('Statistics'), {
            'fields': ('attendance_count',),
            'classes': ('collapse',)
        }),
        (_('SEO'), {
            'fields': ('meta_title', 'meta_description'),
            'classes': ('collapse',)
        }),
        (_('Timestamps'), {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['publish_meetings', 'feature_meetings', 'unfeature_meetings', 'cancel_meetings']

    def publish_meetings(self, request, queryset):
        """Publish selected meetings"""
        updated = queryset.update(status='published')
        self.message_user(request, f'{updated} meeting(s) published.')
    publish_meetings.short_description = _('Publish selected meetings')

    def feature_meetings(self, request, queryset):
        """Feature selected meetings"""
        updated = queryset.update(is_featured=True)
        self.message_user(request, f'{updated} meeting(s) featured.')
    feature_meetings.short_description = _('Feature selected meetings')

    def unfeature_meetings(self, request, queryset):
        """Unfeature selected meetings"""
        updated = queryset.update(is_featured=False)
        self.message_user(request, f'{updated} meeting(s) unfeatured.')
    unfeature_meetings.short_description = _('Unfeature selected meetings')

    def cancel_meetings(self, request, queryset):
        """Cancel selected meetings"""
        updated = queryset.update(status='cancelled')
        self.message_user(request, f'{updated} meeting(s) cancelled.')
    cancel_meetings.short_description = _('Cancel selected meetings')

@admin.register(MeetingRegistration)
class MeetingRegistrationAdmin(admin.ModelAdmin):
    """Meeting Registration admin"""
    list_display = ('get_meeting', 'user', 'status', 'attended', 'payment_status', 'registered_at')
    list_filter = ('status', 'attended', 'payment_status', 'payment_required', 'registered_at')
    search_fields = ('meeting__title', 'user__username', 'contact_email')
    readonly_fields = ('id', 'registered_at', 'confirmed_at', 'cancelled_at')
    date_hierarchy = 'registered_at'

    fieldsets = (
        (_('Registration Details'), {
            'fields': ('meeting', 'user', 'status', 'registration_notes')
        }),
        (_('Contact Information'), {
            'fields': ('contact_email', 'contact_phone')
        }),
        (_('Payment'), {
            'fields': ('payment_required', 'payment_status', 'payment_amount'),
            'classes': ('collapse',)
        }),
        (_('Attendance'), {
            'fields': ('attended', 'attendance_duration')
        }),
        (_('Feedback'), {
            'fields': ('rating', 'feedback'),
            'classes': ('collapse',)
        }),
        (_('Timestamps'), {
            'fields': ('id', 'registered_at', 'confirmed_at', 'cancelled_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['confirm_registrations', 'mark_attended', 'mark_no_show']

    def get_meeting(self, obj):
        """Link to meeting"""
        url = reverse('admin:meetings_meeting_change', args=[obj.meeting.pk])
        return format_html('<a href="{}">{}</a>', url, obj.meeting.title[:50])
    get_meeting.short_description = _('Meeting')
    get_meeting.admin_order_field = 'meeting'

    def confirm_registrations(self, request, queryset):
        """Confirm selected registrations"""
        updated = 0
        for registration in queryset:
            if registration.status == 'registered':
                registration.status = 'confirmed'
                registration.confirmed_at = timezone.now()
                registration.save()
                updated += 1
        self.message_user(request, f'{updated} registration(s) confirmed.')
    confirm_registrations.short_description = _('Confirm selected registrations')

    def mark_attended(self, request, queryset):
        """Mark selected registrations as attended"""
        updated = queryset.update(attended=True, status='attended')
        self.message_user(request, f'{updated} registration(s) marked as attended.')
    mark_attended.short_description = _('Mark as attended')

    def mark_no_show(self, request, queryset):
        """Mark selected registrations as no show"""
        updated = queryset.update(attended=False, status='no_show')
        self.message_user(request, f'{updated} registration(s) marked as no show.')
    mark_no_show.short_description = _('Mark as no show')

@admin.register(MeetingResource)
class MeetingResourceAdmin(admin.ModelAdmin):
    """Meeting Resource admin"""
    list_display = ('title', 'get_meeting', 'resource_type', 'is_public', 'download_count', 'created_at')
    list_filter = ('resource_type', 'is_public', 'requires_registration', 'created_at')
    search_fields = ('title', 'description', 'meeting__title')
    readonly_fields = ('id', 'file_size', 'download_count', 'view_count', 'created_at', 'updated_at')
    date_hierarchy = 'created_at'

    fieldsets = (
        (_('Basic Information'), {
            'fields': ('meeting', 'title', 'description', 'resource_type')
        }),
        (_('Files & Links'), {
            'fields': ('file', 'external_url')
        }),
        (_('Access Control'), {
            'fields': ('is_public', 'requires_registration', 'available_before_meeting', 'available_after_meeting')
        }),
        (_('Metadata'), {
            'fields': ('file_size', 'duration_minutes'),
            'classes': ('collapse',)
        }),
        (_('Statistics'), {
            'fields': ('download_count', 'view_count'),
            'classes': ('collapse',)
        }),
        (_('Timestamps'), {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_meeting(self, obj):
        """Link to meeting"""
        url = reverse('admin:meetings_meeting_change', args=[obj.meeting.pk])
        return format_html('<a href="{}">{}</a>', url, obj.meeting.title[:50])
    get_meeting.short_description = _('Meeting')
    get_meeting.admin_order_field = 'meeting'

@admin.register(MeetingFeedback)
class MeetingFeedbackAdmin(admin.ModelAdmin):
    """Meeting Feedback admin"""
    list_display = ('get_meeting', 'user', 'overall_rating', 'would_recommend', 'created_at')
    list_filter = ('overall_rating', 'content_rating', 'facilitator_rating', 'would_recommend', 'would_attend_again', 'created_at')
    search_fields = ('meeting__title', 'user__username', 'what_liked', 'what_improved')
    readonly_fields = ('id', 'created_at')
    date_hierarchy = 'created_at'

    fieldsets = (
        (_('Basic Information'), {
            'fields': ('meeting', 'user')
        }),
        (_('Ratings'), {
            'fields': ('overall_rating', 'content_rating', 'facilitator_rating')
        }),
        (_('Feedback'), {
            'fields': ('what_liked', 'what_improved', 'additional_comments')
        }),
        (_('Recommendations'), {
            'fields': ('would_recommend', 'would_attend_again')
        }),
        (_('Timestamps'), {
            'fields': ('id', 'created_at'),
            'classes': ('collapse',)
        }),
    )

    def get_meeting(self, obj):
        """Link to meeting"""
        url = reverse('admin:meetings_meeting_change', args=[obj.meeting.pk])
        return format_html('<a href="{}">{}</a>', url, obj.meeting.title[:50])
    get_meeting.short_description = _('Meeting')
    get_meeting.admin_order_field = 'meeting'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False
