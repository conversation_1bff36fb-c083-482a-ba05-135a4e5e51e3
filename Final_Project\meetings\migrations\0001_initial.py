# Generated by Django 5.2.4 on 2025-07-25 18:17

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Meeting',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200)),
                ('slug', models.SlugField(max_length=200, unique=True)),
                ('description', models.TextField()),
                ('meeting_type', models.CharField(choices=[('webinar', 'Webinar'), ('workshop', 'Workshop'), ('seminar', 'Seminar'), ('group_therapy', 'Group Therapy'), ('support_group', 'Support Group'), ('conference', 'Conference'), ('training', 'Training')], max_length=20)),
                ('start_datetime', models.DateTimeField()),
                ('end_datetime', models.DateTimeField()),
                ('timezone', models.CharField(default='Africa/Addis_Ababa', max_length=50)),
                ('max_participants', models.PositiveIntegerField(default=50)),
                ('registration_required', models.BooleanField(default=True)),
                ('registration_deadline', models.DateTimeField(blank=True, null=True)),
                ('is_free', models.BooleanField(default=True)),
                ('price', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('currency', models.CharField(default='ETB', max_length=3)),
                ('meeting_url', models.URLField(blank=True, help_text='Zoom, Teams, or other meeting platform URL')),
                ('meeting_id', models.CharField(blank=True, help_text='Meeting ID or room number', max_length=100)),
                ('meeting_password', models.CharField(blank=True, max_length=100)),
                ('is_online', models.BooleanField(default=True)),
                ('venue_name', models.CharField(blank=True, max_length=200)),
                ('venue_address', models.TextField(blank=True)),
                ('featured_image', models.ImageField(blank=True, upload_to='meetings/images/')),
                ('agenda', models.TextField(blank=True)),
                ('prerequisites', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('published', 'Published'), ('ongoing', 'Ongoing'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('is_featured', models.BooleanField(default=False)),
                ('is_recurring', models.BooleanField(default=False)),
                ('registration_count', models.PositiveIntegerField(default=0)),
                ('attendance_count', models.PositiveIntegerField(default=0)),
                ('meta_title', models.CharField(blank=True, max_length=200)),
                ('meta_description', models.CharField(blank=True, max_length=300)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('facilitators', models.ManyToManyField(blank=True, related_name='facilitated_meetings', to=settings.AUTH_USER_MODEL)),
                ('organizer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='organized_meetings', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Meeting',
                'verbose_name_plural': 'Meetings',
                'ordering': ['start_datetime'],
            },
        ),
        migrations.CreateModel(
            name='MeetingResource',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('resource_type', models.CharField(choices=[('presentation', 'Presentation'), ('document', 'Document'), ('video', 'Video'), ('audio', 'Audio'), ('link', 'External Link'), ('recording', 'Meeting Recording')], max_length=20)),
                ('file', models.FileField(blank=True, upload_to='meetings/resources/')),
                ('external_url', models.URLField(blank=True)),
                ('is_public', models.BooleanField(default=False, help_text='Available to non-registered users')),
                ('requires_registration', models.BooleanField(default=True)),
                ('available_before_meeting', models.BooleanField(default=True)),
                ('available_after_meeting', models.BooleanField(default=True)),
                ('file_size', models.PositiveIntegerField(blank=True, help_text='File size in bytes', null=True)),
                ('duration_minutes', models.PositiveIntegerField(blank=True, null=True)),
                ('download_count', models.PositiveIntegerField(default=0)),
                ('view_count', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('meeting', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resources', to='meetings.meeting')),
            ],
            options={
                'verbose_name': 'Meeting Resource',
                'verbose_name_plural': 'Meeting Resources',
                'ordering': ['created_at'],
            },
        ),
        migrations.CreateModel(
            name='MeetingFeedback',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('overall_rating', models.PositiveIntegerField(help_text='Overall rating from 1 to 5', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('content_rating', models.PositiveIntegerField(help_text='Content quality rating', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('facilitator_rating', models.PositiveIntegerField(help_text='Facilitator rating', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('what_liked', models.TextField(blank=True, help_text='What did you like most?')),
                ('what_improved', models.TextField(blank=True, help_text='What could be improved?')),
                ('additional_comments', models.TextField(blank=True)),
                ('would_recommend', models.BooleanField(default=True)),
                ('would_attend_again', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('meeting', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='feedback', to='meetings.meeting')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='meeting_feedback', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Meeting Feedback',
                'verbose_name_plural': 'Meeting Feedback',
                'ordering': ['-created_at'],
                'unique_together': {('meeting', 'user')},
            },
        ),
        migrations.CreateModel(
            name='MeetingRegistration',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('status', models.CharField(choices=[('registered', 'Registered'), ('confirmed', 'Confirmed'), ('attended', 'Attended'), ('no_show', 'No Show'), ('cancelled', 'Cancelled')], default='registered', max_length=20)),
                ('registration_notes', models.TextField(blank=True, help_text='Special requirements or notes')),
                ('contact_email', models.EmailField(max_length=254)),
                ('contact_phone', models.CharField(blank=True, max_length=20)),
                ('payment_required', models.BooleanField(default=False)),
                ('payment_status', models.CharField(default='pending', max_length=20)),
                ('payment_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('attended', models.BooleanField(default=False)),
                ('attendance_duration', models.PositiveIntegerField(default=0, help_text='Minutes attended')),
                ('rating', models.PositiveIntegerField(blank=True, help_text='Rating from 1 to 5', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('feedback', models.TextField(blank=True)),
                ('registered_at', models.DateTimeField(auto_now_add=True)),
                ('confirmed_at', models.DateTimeField(blank=True, null=True)),
                ('cancelled_at', models.DateTimeField(blank=True, null=True)),
                ('meeting', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='registrations', to='meetings.meeting')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='meeting_registrations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Meeting Registration',
                'verbose_name_plural': 'Meeting Registrations',
                'ordering': ['registered_at'],
                'unique_together': {('meeting', 'user')},
            },
        ),
    ]
