from django.db import models
from django.contrib.auth.models import User
from django.utils.translation import gettext_lazy as _
from django.urls import reverse
from django.core.validators import MinValueValidator, MaxValueValidator
import uuid

class Meeting(models.Model):
    """Events, seminars, and group meetings"""
    MEETING_TYPES = [
        ('webinar', _('Webinar')),
        ('workshop', _('Workshop')),
        ('seminar', _('Seminar')),
        ('group_therapy', _('Group Therapy')),
        ('support_group', _('Support Group')),
        ('conference', _('Conference')),
        ('training', _('Training')),
    ]

    STATUS_CHOICES = [
        ('draft', _('Draft')),
        ('published', _('Published')),
        ('ongoing', _('Ongoing')),
        ('completed', _('Completed')),
        ('cancelled', _('Cancelled')),
    ]

    # Basic Information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=200)
    slug = models.SlugField(max_length=200, unique=True)
    description = models.TextField()
    meeting_type = models.CharField(max_length=20, choices=MEETING_TYPES)

    # Organizer and Facilitators
    organizer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='organized_meetings')
    facilitators = models.ManyToManyField(User, blank=True, related_name='facilitated_meetings')

    # Schedule
    start_datetime = models.DateTimeField()
    end_datetime = models.DateTimeField()
    timezone = models.CharField(max_length=50, default='Africa/Addis_Ababa')

    # Capacity and Registration
    max_participants = models.PositiveIntegerField(default=50)
    registration_required = models.BooleanField(default=True)
    registration_deadline = models.DateTimeField(blank=True, null=True)

    # Pricing
    is_free = models.BooleanField(default=True)
    price = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    currency = models.CharField(max_length=3, default='ETB')

    # Meeting Details
    meeting_url = models.URLField(blank=True, help_text=_("Zoom, Teams, or other meeting platform URL"))
    meeting_id = models.CharField(max_length=100, blank=True, help_text=_("Meeting ID or room number"))
    meeting_password = models.CharField(max_length=100, blank=True)

    # Location (for in-person meetings)
    is_online = models.BooleanField(default=True)
    venue_name = models.CharField(max_length=200, blank=True)
    venue_address = models.TextField(blank=True)

    # Content and Materials
    featured_image = models.ImageField(upload_to='meetings/images/', blank=True)
    agenda = models.TextField(blank=True)
    prerequisites = models.TextField(blank=True)

    # Status and Visibility
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    is_featured = models.BooleanField(default=False)
    is_recurring = models.BooleanField(default=False)

    # Statistics
    registration_count = models.PositiveIntegerField(default=0)
    attendance_count = models.PositiveIntegerField(default=0)

    # SEO
    meta_title = models.CharField(max_length=200, blank=True)
    meta_description = models.CharField(max_length=300, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("Meeting")
        verbose_name_plural = _("Meetings")
        ordering = ['start_datetime']

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        return reverse('meetings:meeting_detail', kwargs={'slug': self.slug})

    @property
    def is_full(self):
        return self.registration_count >= self.max_participants

    @property
    def available_spots(self):
        return max(0, self.max_participants - self.registration_count)

class MeetingRegistration(models.Model):
    """User registrations for meetings"""
    STATUS_CHOICES = [
        ('registered', _('Registered')),
        ('confirmed', _('Confirmed')),
        ('attended', _('Attended')),
        ('no_show', _('No Show')),
        ('cancelled', _('Cancelled')),
    ]

    # Basic Information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    meeting = models.ForeignKey(Meeting, on_delete=models.CASCADE, related_name='registrations')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='meeting_registrations')

    # Registration Details
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='registered')
    registration_notes = models.TextField(blank=True, help_text=_("Special requirements or notes"))

    # Contact Information (in case user details change)
    contact_email = models.EmailField()
    contact_phone = models.CharField(max_length=20, blank=True)

    # Payment (if applicable)
    payment_required = models.BooleanField(default=False)
    payment_status = models.CharField(max_length=20, default='pending')
    payment_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)

    # Attendance Tracking
    attended = models.BooleanField(default=False)
    attendance_duration = models.PositiveIntegerField(default=0, help_text=_("Minutes attended"))

    # Feedback
    rating = models.PositiveIntegerField(
        blank=True, null=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text=_("Rating from 1 to 5")
    )
    feedback = models.TextField(blank=True)

    # Timestamps
    registered_at = models.DateTimeField(auto_now_add=True)
    confirmed_at = models.DateTimeField(blank=True, null=True)
    cancelled_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        verbose_name = _("Meeting Registration")
        verbose_name_plural = _("Meeting Registrations")
        unique_together = ['meeting', 'user']
        ordering = ['registered_at']

    def __str__(self):
        return f"{self.user.username} - {self.meeting.title}"

class MeetingResource(models.Model):
    """Resources and materials for meetings"""
    RESOURCE_TYPES = [
        ('presentation', _('Presentation')),
        ('document', _('Document')),
        ('video', _('Video')),
        ('audio', _('Audio')),
        ('link', _('External Link')),
        ('recording', _('Meeting Recording')),
    ]

    # Basic Information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    meeting = models.ForeignKey(Meeting, on_delete=models.CASCADE, related_name='resources')
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    resource_type = models.CharField(max_length=20, choices=RESOURCE_TYPES)

    # Files and Links
    file = models.FileField(upload_to='meetings/resources/', blank=True)
    external_url = models.URLField(blank=True)

    # Access Control
    is_public = models.BooleanField(default=False, help_text=_("Available to non-registered users"))
    requires_registration = models.BooleanField(default=True)
    available_before_meeting = models.BooleanField(default=True)
    available_after_meeting = models.BooleanField(default=True)

    # Metadata
    file_size = models.PositiveIntegerField(blank=True, null=True, help_text=_("File size in bytes"))
    duration_minutes = models.PositiveIntegerField(blank=True, null=True)

    # Statistics
    download_count = models.PositiveIntegerField(default=0)
    view_count = models.PositiveIntegerField(default=0)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("Meeting Resource")
        verbose_name_plural = _("Meeting Resources")
        ordering = ['created_at']

    def __str__(self):
        return f"{self.meeting.title} - {self.title}"

class MeetingFeedback(models.Model):
    """Feedback and evaluations for meetings"""
    # Basic Information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    meeting = models.ForeignKey(Meeting, on_delete=models.CASCADE, related_name='feedback')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='meeting_feedback')

    # Ratings
    overall_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text=_("Overall rating from 1 to 5")
    )
    content_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text=_("Content quality rating")
    )
    facilitator_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text=_("Facilitator rating")
    )

    # Feedback
    what_liked = models.TextField(blank=True, help_text=_("What did you like most?"))
    what_improved = models.TextField(blank=True, help_text=_("What could be improved?"))
    additional_comments = models.TextField(blank=True)

    # Recommendations
    would_recommend = models.BooleanField(default=True)
    would_attend_again = models.BooleanField(default=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _("Meeting Feedback")
        verbose_name_plural = _("Meeting Feedback")
        unique_together = ['meeting', 'user']
        ordering = ['-created_at']

    def __str__(self):
        return f"Feedback for {self.meeting.title} by {self.user.username}"
