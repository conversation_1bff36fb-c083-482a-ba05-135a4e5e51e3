from django.urls import path
from . import views

app_name = 'meetings'

urlpatterns = [
    path('', views.MeetingListView.as_view(), name='meeting_list'),
    path('<int:pk>/', views.MeetingDetailView.as_view(), name='meeting_detail'),
    path('create/', views.CreateMeetingView.as_view(), name='create_meeting'),
    path('<int:pk>/edit/', views.EditMeetingView.as_view(), name='edit_meeting'),
    path('<int:pk>/delete/', views.DeleteMeetingView.as_view(), name='delete_meeting'),
    path('<int:pk>/register/', views.RegisterForMeetingView.as_view(), name='register_meeting'),
    path('<int:pk>/unregister/', views.UnregisterFromMeetingView.as_view(), name='unregister_meeting'),
    path('my-meetings/', views.MyMeetingsView.as_view(), name='my_meetings'),
]
