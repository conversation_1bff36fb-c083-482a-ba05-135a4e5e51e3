from django.shortcuts import render
from django.views.generic import TemplateView, ListView

# Placeholder views - will be implemented later
class MeetingListView(ListView):
    template_name = 'meetings/meeting_list.html'
    context_object_name = 'meetings'

    def get_queryset(self):
        return []

class MeetingDetailView(TemplateView):
    template_name = 'meetings/meeting_detail.html'

class CreateMeetingView(TemplateView):
    template_name = 'meetings/meeting_form.html'

class EditMeetingView(TemplateView):
    template_name = 'meetings/meeting_form.html'

class DeleteMeetingView(TemplateView):
    template_name = 'meetings/meeting_confirm_delete.html'

class RegisterForMeetingView(TemplateView):
    template_name = 'meetings/register_meeting.html'

class UnregisterFromMeetingView(TemplateView):
    template_name = 'meetings/unregister_meeting.html'

class MyMeetingsView(ListView):
    template_name = 'meetings/my_meetings.html'
    context_object_name = 'meetings'

    def get_queryset(self):
        return []
