from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django.urls import reverse
from .models import Payment, PaymentWebhook, Refund

@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    """Payment admin with comprehensive management"""
    list_display = ('id', 'user', 'get_consultation', 'amount', 'currency', 'payment_method', 'status', 'created_at')
    list_filter = ('status', 'payment_method', 'currency', 'created_at')
    search_fields = ('user__username', 'user__email', 'chapa_transaction_id', 'chapa_reference', 'id')
    readonly_fields = ('id', 'created_at', 'updated_at', 'paid_at', 'net_amount', 'transaction_fee')
    date_hierarchy = 'created_at'

    fieldsets = (
        (_('Payment Information'), {
            'fields': ('id', 'user', 'consultation', 'amount', 'currency', 'payment_method', 'status')
        }),
        (_('Chapa Integration'), {
            'fields': ('chapa_checkout_url', 'chapa_transaction_id', 'chapa_reference', 'chapa_response'),
            'classes': ('collapse',)
        }),
        (_('Transaction Details'), {
            'fields': ('transaction_fee', 'net_amount', 'failure_reason', 'notes')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at', 'paid_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_completed', 'mark_failed', 'export_payments']

    def get_consultation(self, obj):
        if obj.consultation:
            url = reverse('admin:consultation_consultation_change', args=[obj.consultation.pk])
            return format_html('<a href="{}">{}</a>', url, obj.consultation.id)
        return '-'
    get_consultation.short_description = _('Consultation')
    get_consultation.admin_order_field = 'consultation'

    def mark_completed(self, request, queryset):
        from django.utils import timezone
        updated = 0
        for payment in queryset:
            if payment.status in ['pending', 'processing']:
                payment.status = 'completed'
                payment.paid_at = timezone.now()
                payment.save()

                # Update consultation status
                if payment.consultation:
                    payment.consultation.payment_status = 'paid'
                    payment.consultation.status = 'confirmed'
                    payment.consultation.save()

                updated += 1

        self.message_user(request, f'{updated} payment(s) marked as completed.')
    mark_completed.short_description = _('Mark selected payments as completed')

    def mark_failed(self, request, queryset):
        updated = queryset.filter(status__in=['pending', 'processing']).update(status='failed')
        self.message_user(request, f'{updated} payment(s) marked as failed.')
    mark_failed.short_description = _('Mark selected payments as failed')

    def export_payments(self, request, queryset):
        # This would implement CSV export functionality
        self.message_user(request, 'Export functionality would be implemented here.')
    export_payments.short_description = _('Export selected payments')

@admin.register(PaymentWebhook)
class PaymentWebhookAdmin(admin.ModelAdmin):
    """Payment Webhook admin for debugging"""
    list_display = ('id', 'payment', 'provider', 'processed', 'created_at', 'processed_at')
    list_filter = ('provider', 'processed', 'created_at')
    search_fields = ('payment__id', 'payment__chapa_reference')
    readonly_fields = ('created_at', 'processed_at', 'webhook_data')

    fieldsets = (
        (_('Webhook Information'), {
            'fields': ('payment', 'provider', 'processed')
        }),
        (_('Processing'), {
            'fields': ('processing_error', 'processed_at')
        }),
        (_('Data'), {
            'fields': ('webhook_data',),
            'classes': ('collapse',)
        }),
        (_('Timestamp'), {
            'fields': ('created_at',)
        }),
    )

    actions = ['reprocess_webhooks']

    def reprocess_webhooks(self, request, queryset):
        # This would implement webhook reprocessing
        self.message_user(request, 'Webhook reprocessing would be implemented here.')
    reprocess_webhooks.short_description = _('Reprocess selected webhooks')

@admin.register(Refund)
class RefundAdmin(admin.ModelAdmin):
    """Refund admin"""
    list_display = ('id', 'get_payment', 'amount', 'status', 'requested_by', 'approved_by', 'created_at')
    list_filter = ('status', 'created_at')
    search_fields = ('payment__id', 'requested_by__username', 'reason')
    readonly_fields = ('created_at', 'processed_at')

    fieldsets = (
        (_('Refund Information'), {
            'fields': ('payment', 'amount', 'reason', 'status')
        }),
        (_('Administration'), {
            'fields': ('requested_by', 'approved_by')
        }),
        (_('Provider Details'), {
            'fields': ('provider_refund_id', 'provider_response'),
            'classes': ('collapse',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'processed_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['approve_refunds', 'reject_refunds']

    def get_payment(self, obj):
        url = reverse('admin:payments_payment_change', args=[obj.payment.pk])
        return format_html('<a href="{}">{}</a>', url, obj.payment.id)
    get_payment.short_description = _('Payment')
    get_payment.admin_order_field = 'payment'

    def approve_refunds(self, request, queryset):
        from django.utils import timezone
        updated = 0
        for refund in queryset.filter(status='pending'):
            refund.status = 'completed'
            refund.approved_by = request.user
            refund.processed_at = timezone.now()
            refund.save()
            updated += 1

        self.message_user(request, f'{updated} refund(s) approved.')
    approve_refunds.short_description = _('Approve selected refunds')

    def reject_refunds(self, request, queryset):
        updated = queryset.filter(status='pending').update(status='failed')
        self.message_user(request, f'{updated} refund(s) rejected.')
    reject_refunds.short_description = _('Reject selected refunds')
