from django.db import models
from django.contrib.auth.models import User
from django.utils.translation import gettext_lazy as _
from decimal import Decimal
import uuid

class Payment(models.Model):
    """Payment records for consultations"""
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('processing', _('Processing')),
        ('completed', _('Completed')),
        ('failed', _('Failed')),
        ('cancelled', _('Cancelled')),
        ('refunded', _('Refunded')),
    ]

    PAYMENT_METHOD_CHOICES = [
        ('chapa', _('Chapa')),
        ('bank_transfer', _('Bank Transfer')),
        ('cash', _('Cash')),
        ('free', _('Free')),
    ]

    # Basic Information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='payments')
    consultation = models.OneToOneField(
        'consultation.Consultation',
        on_delete=models.CASCADE,
        related_name='payment'
    )

    # Payment Details
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='ETB')
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES, default='chapa')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    # Chapa Integration Fields
    chapa_checkout_url = models.URLField(blank=True)
    chapa_transaction_id = models.CharField(max_length=100, blank=True)
    chapa_reference = models.CharField(max_length=100, blank=True)
    chapa_response = models.JSONField(blank=True, null=True)

    # Transaction Details
    transaction_fee = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    net_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    paid_at = models.DateTimeField(null=True, blank=True)

    # Additional Information
    failure_reason = models.TextField(blank=True)
    notes = models.TextField(blank=True)

    class Meta:
        verbose_name = _("Payment")
        verbose_name_plural = _("Payments")
        ordering = ['-created_at']

    def __str__(self):
        return f"Payment {self.id} - {self.user.username} - {self.amount} {self.currency}"

    @property
    def is_successful(self):
        return self.status == 'completed'

    def calculate_net_amount(self):
        """Calculate net amount after transaction fees"""
        # Chapa typically charges 2.5% + 5 ETB
        if self.payment_method == 'chapa':
            fee_percentage = Decimal('0.025')  # 2.5%
            fixed_fee = Decimal('5.00')  # 5 ETB

            self.transaction_fee = (self.amount * fee_percentage) + fixed_fee
            self.net_amount = self.amount - self.transaction_fee
        else:
            self.net_amount = self.amount

        return self.net_amount

class PaymentWebhook(models.Model):
    """Store webhook data from payment providers"""
    payment = models.ForeignKey(
        Payment,
        on_delete=models.CASCADE,
        related_name='webhooks',
        null=True,
        blank=True
    )

    provider = models.CharField(max_length=20, default='chapa')
    webhook_data = models.JSONField()
    processed = models.BooleanField(default=False)
    processing_error = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = _("Payment Webhook")
        verbose_name_plural = _("Payment Webhooks")
        ordering = ['-created_at']

    def __str__(self):
        return f"Webhook {self.id} - {self.provider} - {self.created_at}"

class Refund(models.Model):
    """Refund records"""
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('processing', _('Processing')),
        ('completed', _('Completed')),
        ('failed', _('Failed')),
    ]

    payment = models.ForeignKey(Payment, on_delete=models.CASCADE, related_name='refunds')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    reason = models.TextField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    # Admin fields
    requested_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='requested_refunds')
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_refunds'
    )

    # Provider fields
    provider_refund_id = models.CharField(max_length=100, blank=True)
    provider_response = models.JSONField(blank=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = _("Refund")
        verbose_name_plural = _("Refunds")
        ordering = ['-created_at']

    def __str__(self):
        return f"Refund {self.id} - {self.amount} {self.payment.currency}"
