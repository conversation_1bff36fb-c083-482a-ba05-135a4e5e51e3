from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import TemplateView, ListView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.utils.translation import gettext_lazy as _
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.conf import settings
from django.utils import timezone
import json
import requests
import uuid
from decimal import Decimal

from consultation.models import Consultation
from .models import Payment, PaymentWebhook

class CheckoutView(LoginRequiredMixin, TemplateView):
    """Handle payment checkout for consultations"""
    template_name = 'payments/checkout.html'

    def dispatch(self, request, *args, **kwargs):
        self.consultation = get_object_or_404(
            Consultation,
            pk=kwargs['consultation_id'],
            user=request.user
        )

        # Check if consultation needs payment
        if self.consultation.is_free_consultation:
            messages.info(request, _('This consultation is free. No payment required.'))
            return redirect('consultation:consultation_detail', pk=self.consultation.pk)

        # Check if already paid
        if self.consultation.payment_status == 'paid':
            messages.info(request, _('This consultation has already been paid for.'))
            return redirect('consultation:consultation_detail', pk=self.consultation.pk)

        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['consultation'] = self.consultation

        # Get or create payment record
        payment, created = Payment.objects.get_or_create(
            consultation=self.consultation,
            defaults={
                'user': self.request.user,
                'amount': self.consultation.price,
                'currency': self.consultation.currency,
                'payment_method': 'chapa',
                'status': 'pending'
            }
        )

        context['payment'] = payment
        return context

    def post(self, request, *args, **kwargs):
        # Mock payment system - automatically mark as paid for testing
        payment, created = Payment.objects.get_or_create(
            consultation=self.consultation,
            defaults={
                'user': request.user,
                'amount': self.consultation.price,
                'currency': self.consultation.currency,
                'payment_method': 'mock',
                'status': 'pending'
            }
        )

        # Simulate successful payment
        payment.status = 'completed'
        payment.paid_at = timezone.now()
        payment.chapa_reference = f"MOCK-{payment.id}-{timezone.now().strftime('%Y%m%d%H%M%S')}"
        payment.save()

        # Update consultation status
        self.consultation.payment_status = 'paid'
        self.consultation.status = 'confirmed'
        self.consultation.save()

        messages.success(request, _('Payment successful! Your consultation has been confirmed.'))
        return redirect('consultation:consultation_detail', pk=self.consultation.pk)

    # def create_chapa_payment(self, payment):
    #     """Create payment with Chapa API - DISABLED FOR MOCK TESTING"""
    #     # Chapa integration temporarily disabled for testing
    #     return None

class PaymentSuccessView(TemplateView):
    """Handle successful payment"""
    template_name = 'payments/success.html'

    def get(self, request, *args, **kwargs):
        tx_ref = request.GET.get('tx_ref')

        if tx_ref:
            try:
                payment = Payment.objects.get(chapa_reference=tx_ref)

                # Verify payment with Chapa
                if self.verify_chapa_payment(tx_ref):
                    payment.status = 'completed'
                    payment.paid_at = timezone.now()
                    payment.save()

                    # Update consultation status
                    consultation = payment.consultation
                    consultation.payment_status = 'paid'
                    consultation.status = 'confirmed'
                    consultation.save()

                    messages.success(request, _('Payment successful! Your consultation has been confirmed.'))
                    return redirect('consultation:consultation_detail', pk=consultation.pk)
                else:
                    payment.status = 'failed'
                    payment.failure_reason = 'Payment verification failed'
                    payment.save()

                    messages.error(request, _('Payment verification failed. Please contact support.'))

            except Payment.DoesNotExist:
                messages.error(request, _('Payment record not found.'))

        return super().get(request, *args, **kwargs)

    def verify_chapa_payment(self, tx_ref):
        """Verify payment with Chapa API"""
        try:
            url = f"https://api.chapa.co/v1/transaction/verify/{tx_ref}"
            headers = {
                "Authorization": f"Bearer {settings.CHAPA_SECRET_KEY}",
            }

            response = requests.get(url, headers=headers)
            data = response.json()

            return data.get('status') == 'success' and data.get('data', {}).get('status') == 'success'

        except Exception as e:
            print(f"Chapa verification error: {e}")
            return False

class PaymentCancelView(TemplateView):
    """Handle cancelled payment"""
    template_name = 'payments/cancel.html'

    def get(self, request, *args, **kwargs):
        messages.warning(request, _('Payment was cancelled. You can try again anytime.'))
        return super().get(request, *args, **kwargs)

@method_decorator(csrf_exempt, name='dispatch')
class ChapaWebhookView(TemplateView):
    """Handle Chapa webhook notifications"""

    def post(self, request, *args, **kwargs):
        try:
            payload = json.loads(request.body)

            # Store webhook data
            webhook = PaymentWebhook.objects.create(
                provider='chapa',
                webhook_data=payload
            )

            # Process webhook
            self.process_webhook(webhook, payload)

            return JsonResponse({'status': 'success'})

        except Exception as e:
            print(f"Webhook error: {e}")
            return JsonResponse({'status': 'error', 'message': str(e)})

    def process_webhook(self, webhook, payload):
        """Process Chapa webhook data"""
        try:
            tx_ref = payload.get('tx_ref')
            status = payload.get('status')

            if tx_ref:
                try:
                    payment = Payment.objects.get(chapa_reference=tx_ref)
                    webhook.payment = payment

                    if status == 'success':
                        payment.status = 'completed'
                        payment.paid_at = timezone.now()
                        payment.chapa_response = payload
                        payment.save()

                        # Update consultation
                        consultation = payment.consultation
                        consultation.payment_status = 'paid'
                        consultation.status = 'confirmed'
                        consultation.save()

                    elif status == 'failed':
                        payment.status = 'failed'
                        payment.failure_reason = payload.get('message', 'Payment failed')
                        payment.save()

                    webhook.processed = True
                    webhook.processed_at = timezone.now()
                    webhook.save()

                except Payment.DoesNotExist:
                    webhook.processing_error = f"Payment not found for tx_ref: {tx_ref}"
                    webhook.save()

        except Exception as e:
            webhook.processing_error = str(e)
            webhook.save()

class PaymentHistoryView(LoginRequiredMixin, ListView):
    """User's payment history"""
    model = Payment
    template_name = 'payments/payment_history.html'
    context_object_name = 'payments'
    paginate_by = 20

    def get_queryset(self):
        return Payment.objects.filter(
            user=self.request.user
        ).select_related('consultation__psychologist__user').order_by('-created_at')
