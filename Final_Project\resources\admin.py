from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django.utils import timezone
from .models import ResourceCategory, Resource, ResourceView, ResourceLike

@admin.register(ResourceCategory)
class ResourceCategoryAdmin(admin.ModelAdmin):
    """Resource Category admin"""
    list_display = ('name', 'resource_count', 'is_active', 'order', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'description')
    prepopulated_fields = {'name': ('name',)}
    ordering = ['order', 'name']

    def resource_count(self, obj):
        return obj.resources.count()
    resource_count.short_description = _('Resources')

@admin.register(Resource)
class ResourceAdmin(admin.ModelAdmin):
    """Resource admin with comprehensive management"""
    list_display = ('title', 'category', 'resource_type', 'difficulty_level', 'is_published', 'is_featured', 'view_count', 'created_at')
    list_filter = ('resource_type', 'difficulty_level', 'is_published', 'is_featured', 'is_premium', 'category', 'created_at')
    search_fields = ('title', 'description', 'tags', 'content')
    prepopulated_fields = {'slug': ('title',)}
    readonly_fields = ('id', 'view_count', 'download_count', 'like_count', 'created_at', 'updated_at')
    date_hierarchy = 'created_at'

    fieldsets = (
        (_('Basic Information'), {
            'fields': ('title', 'slug', 'description', 'content', 'category', 'resource_type', 'difficulty_level', 'tags')
        }),
        (_('Media Files'), {
            'fields': ('featured_image', 'video_file', 'audio_file', 'document_file'),
            'classes': ('collapse',)
        }),
        (_('External Links'), {
            'fields': ('external_url', 'youtube_url'),
            'classes': ('collapse',)
        }),
        (_('Metadata'), {
            'fields': ('author', 'duration_minutes', 'reading_time_minutes')
        }),
        (_('Status & Visibility'), {
            'fields': ('is_published', 'is_featured', 'is_premium', 'published_at')
        }),
        (_('Statistics'), {
            'fields': ('view_count', 'download_count', 'like_count'),
            'classes': ('collapse',)
        }),
        (_('SEO'), {
            'fields': ('meta_title', 'meta_description'),
            'classes': ('collapse',)
        }),
        (_('Timestamps'), {
            'fields': ('id', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['publish_resources', 'unpublish_resources', 'feature_resources', 'unfeature_resources']

    def publish_resources(self, request, queryset):
        """Publish selected resources"""
        updated = 0
        for resource in queryset:
            if not resource.is_published:
                resource.is_published = True
                resource.published_at = timezone.now()
                resource.save()
                updated += 1
        self.message_user(request, f'{updated} resource(s) published.')
    publish_resources.short_description = _('Publish selected resources')

    def unpublish_resources(self, request, queryset):
        """Unpublish selected resources"""
        updated = queryset.update(is_published=False)
        self.message_user(request, f'{updated} resource(s) unpublished.')
    unpublish_resources.short_description = _('Unpublish selected resources')

    def feature_resources(self, request, queryset):
        """Feature selected resources"""
        updated = queryset.update(is_featured=True)
        self.message_user(request, f'{updated} resource(s) featured.')
    feature_resources.short_description = _('Feature selected resources')

    def unfeature_resources(self, request, queryset):
        """Unfeature selected resources"""
        updated = queryset.update(is_featured=False)
        self.message_user(request, f'{updated} resource(s) unfeatured.')
    unfeature_resources.short_description = _('Unfeature selected resources')

@admin.register(ResourceView)
class ResourceViewAdmin(admin.ModelAdmin):
    """Resource View admin for analytics"""
    list_display = ('resource', 'user', 'ip_address', 'viewed_at')
    list_filter = ('viewed_at', 'resource__category')
    search_fields = ('resource__title', 'user__username', 'ip_address')
    readonly_fields = ('resource', 'user', 'ip_address', 'user_agent', 'viewed_at')
    date_hierarchy = 'viewed_at'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

@admin.register(ResourceLike)
class ResourceLikeAdmin(admin.ModelAdmin):
    """Resource Like admin"""
    list_display = ('resource', 'user', 'created_at')
    list_filter = ('created_at', 'resource__category')
    search_fields = ('resource__title', 'user__username')
    readonly_fields = ('resource', 'user', 'created_at')
    date_hierarchy = 'created_at'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False
