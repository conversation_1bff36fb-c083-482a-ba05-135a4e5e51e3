from django import forms
from django.utils.translation import gettext_lazy as _
from django.utils.text import slugify
from .models import Resource, ResourceCategory

class ResourceForm(forms.ModelForm):
    """Form for creating and editing resources"""
    
    class Meta:
        model = Resource
        fields = [
            'title', 'description', 'content', 'category', 'resource_type',
            'difficulty_level', 'tags', 'featured_image', 'video_file',
            'audio_file', 'document_file', 'external_url', 'youtube_url',
            'duration_minutes', 'reading_time_minutes', 'is_published',
            'is_featured', 'meta_title', 'meta_description'
        ]
        
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('Enter resource title')
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': _('Brief description of the resource')
            }),
            'content': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 10,
                'placeholder': _('Full content (for articles)')
            }),
            'category': forms.Select(attrs={
                'class': 'form-select'
            }),
            'resource_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'difficulty_level': forms.Select(attrs={
                'class': 'form-select'
            }),
            'tags': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('Comma-separated tags (e.g., anxiety, depression, therapy)')
            }),
            'featured_image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            }),
            'video_file': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'video/*'
            }),
            'audio_file': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'audio/*'
            }),
            'document_file': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': '.pdf,.doc,.docx,.ppt,.pptx'
            }),
            'external_url': forms.URLInput(attrs={
                'class': 'form-control',
                'placeholder': _('https://example.com')
            }),
            'youtube_url': forms.URLInput(attrs={
                'class': 'form-control',
                'placeholder': _('https://youtube.com/watch?v=...')
            }),
            'duration_minutes': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'placeholder': _('Duration in minutes')
            }),
            'reading_time_minutes': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'placeholder': _('Estimated reading time')
            }),
            'is_published': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'is_featured': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'meta_title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('SEO title (optional)')
            }),
            'meta_description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': _('SEO description (optional)')
            }),
        }
        
        help_texts = {
            'content': _('Full content is required for articles. For other types, you can leave this blank.'),
            'tags': _('Use relevant keywords to help users find your resource'),
            'duration_minutes': _('For videos and audio files'),
            'reading_time_minutes': _('For articles and documents'),
            'is_published': _('Uncheck to save as draft'),
            'is_featured': _('Featured resources appear at the top of listings'),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Only show active categories
        self.fields['category'].queryset = ResourceCategory.objects.filter(is_active=True)
        
        # Make some fields required based on resource type
        if self.instance and self.instance.pk:
            resource_type = self.instance.resource_type
            self._adjust_required_fields(resource_type)

    def _adjust_required_fields(self, resource_type):
        """Adjust required fields based on resource type"""
        if resource_type == 'article':
            self.fields['content'].required = True
        elif resource_type == 'video':
            self.fields['duration_minutes'].required = True
        elif resource_type == 'audio':
            self.fields['duration_minutes'].required = True
        elif resource_type == 'document':
            self.fields['document_file'].required = True

    def clean_title(self):
        title = self.cleaned_data.get('title')
        if title:
            # Check for duplicate titles (excluding current instance)
            queryset = Resource.objects.filter(title__iexact=title)
            if self.instance and self.instance.pk:
                queryset = queryset.exclude(pk=self.instance.pk)
            
            if queryset.exists():
                raise forms.ValidationError(_('A resource with this title already exists.'))
        
        return title

    def clean(self):
        cleaned_data = super().clean()
        resource_type = cleaned_data.get('resource_type')
        
        # Validate that appropriate files are provided for each resource type
        if resource_type == 'video':
            if not cleaned_data.get('video_file') and not cleaned_data.get('youtube_url'):
                raise forms.ValidationError(_('Video resources must have either a video file or YouTube URL.'))
        
        elif resource_type == 'audio':
            if not cleaned_data.get('audio_file'):
                raise forms.ValidationError(_('Audio resources must have an audio file.'))
        
        elif resource_type == 'document':
            if not cleaned_data.get('document_file'):
                raise forms.ValidationError(_('Document resources must have a document file.'))
        
        elif resource_type == 'article':
            if not cleaned_data.get('content'):
                raise forms.ValidationError(_('Article resources must have content.'))

        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        
        # Auto-generate slug if not provided
        if not instance.slug:
            instance.slug = slugify(instance.title)
            
            # Ensure slug is unique
            original_slug = instance.slug
            counter = 1
            while Resource.objects.filter(slug=instance.slug).exclude(pk=instance.pk).exists():
                instance.slug = f"{original_slug}-{counter}"
                counter += 1
        
        if commit:
            instance.save()
        
        return instance
