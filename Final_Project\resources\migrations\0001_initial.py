# Generated by Django 5.2.4 on 2025-07-25 18:17

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ResourceCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('icon', models.CharField(blank=True, help_text='FontAwesome icon class', max_length=50)),
                ('color', models.CharField(default='#007bff', help_text='Hex color code', max_length=7)),
                ('is_active', models.BooleanField(default=True)),
                ('order', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Resource Category',
                'verbose_name_plural': 'Resource Categories',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Resource',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200)),
                ('slug', models.SlugField(max_length=200, unique=True)),
                ('description', models.TextField()),
                ('content', models.TextField(blank=True)),
                ('resource_type', models.CharField(choices=[('article', 'Article'), ('video', 'Video'), ('audio', 'Audio'), ('document', 'Document'), ('infographic', 'Infographic'), ('quiz', 'Quiz'), ('worksheet', 'Worksheet')], max_length=20)),
                ('difficulty_level', models.CharField(choices=[('beginner', 'Beginner'), ('intermediate', 'Intermediate'), ('advanced', 'Advanced')], default='beginner', max_length=20)),
                ('tags', models.CharField(blank=True, help_text='Comma-separated tags', max_length=500)),
                ('featured_image', models.ImageField(blank=True, upload_to='resources/images/')),
                ('video_file', models.FileField(blank=True, upload_to='resources/videos/')),
                ('audio_file', models.FileField(blank=True, upload_to='resources/audio/')),
                ('document_file', models.FileField(blank=True, upload_to='resources/documents/')),
                ('external_url', models.URLField(blank=True, help_text='External resource URL')),
                ('youtube_url', models.URLField(blank=True, help_text='YouTube video URL')),
                ('duration_minutes', models.PositiveIntegerField(blank=True, help_text='Duration in minutes', null=True)),
                ('reading_time_minutes', models.PositiveIntegerField(blank=True, help_text='Estimated reading time', null=True)),
                ('is_published', models.BooleanField(default=False)),
                ('is_featured', models.BooleanField(default=False)),
                ('is_premium', models.BooleanField(default=False)),
                ('view_count', models.PositiveIntegerField(default=0)),
                ('download_count', models.PositiveIntegerField(default=0)),
                ('like_count', models.PositiveIntegerField(default=0)),
                ('meta_title', models.CharField(blank=True, max_length=200)),
                ('meta_description', models.CharField(blank=True, max_length=300)),
                ('published_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='authored_resources', to=settings.AUTH_USER_MODEL)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resources', to='resources.resourcecategory')),
            ],
            options={
                'verbose_name': 'Resource',
                'verbose_name_plural': 'Resources',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ResourceLike',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('resource', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='likes', to='resources.resource')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='liked_resources', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Resource Like',
                'verbose_name_plural': 'Resource Likes',
                'unique_together': {('resource', 'user')},
            },
        ),
        migrations.CreateModel(
            name='ResourceView',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField(blank=True)),
                ('viewed_at', models.DateTimeField(auto_now_add=True)),
                ('resource', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='views', to='resources.resource')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Resource View',
                'verbose_name_plural': 'Resource Views',
                'unique_together': {('resource', 'user', 'ip_address')},
            },
        ),
    ]
