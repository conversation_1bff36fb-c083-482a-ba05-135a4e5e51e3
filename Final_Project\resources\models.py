from django.db import models
from django.contrib.auth.models import User
from django.utils.translation import gettext_lazy as _
from django.urls import reverse
import uuid

class ResourceCategory(models.Model):
    """Categories for organizing resources"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    icon = models.CharField(max_length=50, blank=True, help_text=_("FontAwesome icon class"))
    color = models.Char<PERSON>ield(max_length=7, default='#007bff', help_text=_("Hex color code"))
    is_active = models.BooleanField(default=True)
    order = models.PositiveIntegerField(default=0)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("Resource Category")
        verbose_name_plural = _("Resource Categories")
        ordering = ['order', 'name']

    def __str__(self):
        return self.name

class Resource(models.Model):
    """Educational resources for mental health"""
    RESOURCE_TYPES = [
        ('article', _('Article')),
        ('video', _('Video')),
        ('audio', _('Audio')),
        ('document', _('Document')),
        ('infographic', _('Infographic')),
        ('quiz', _('Quiz')),
        ('worksheet', _('Worksheet')),
    ]

    DIFFICULTY_LEVELS = [
        ('beginner', _('Beginner')),
        ('intermediate', _('Intermediate')),
        ('advanced', _('Advanced')),
    ]

    # Basic Information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField(max_length=200)
    slug = models.SlugField(max_length=200, unique=True)
    description = models.TextField()
    content = models.TextField(blank=True)

    # Categorization
    category = models.ForeignKey(ResourceCategory, on_delete=models.CASCADE, related_name='resources')
    resource_type = models.CharField(max_length=20, choices=RESOURCE_TYPES)
    difficulty_level = models.CharField(max_length=20, choices=DIFFICULTY_LEVELS, default='beginner')
    tags = models.CharField(max_length=500, blank=True, help_text=_("Comma-separated tags"))

    # Media Files
    featured_image = models.ImageField(upload_to='resources/images/', blank=True)
    video_file = models.FileField(upload_to='resources/videos/', blank=True)
    audio_file = models.FileField(upload_to='resources/audio/', blank=True)
    document_file = models.FileField(upload_to='resources/documents/', blank=True)

    # External Links
    external_url = models.URLField(blank=True, help_text=_("External resource URL"))
    youtube_url = models.URLField(blank=True, help_text=_("YouTube video URL"))

    # Metadata
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='authored_resources')
    duration_minutes = models.PositiveIntegerField(blank=True, null=True, help_text=_("Duration in minutes"))
    reading_time_minutes = models.PositiveIntegerField(blank=True, null=True, help_text=_("Estimated reading time"))

    # Status and Visibility
    is_published = models.BooleanField(default=False)
    is_featured = models.BooleanField(default=False)
    is_premium = models.BooleanField(default=False)

    # Statistics
    view_count = models.PositiveIntegerField(default=0)
    download_count = models.PositiveIntegerField(default=0)
    like_count = models.PositiveIntegerField(default=0)

    # SEO
    meta_title = models.CharField(max_length=200, blank=True)
    meta_description = models.CharField(max_length=300, blank=True)

    # Timestamps
    published_at = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _("Resource")
        verbose_name_plural = _("Resources")
        ordering = ['-created_at']

    def __str__(self):
        return self.title

    def get_absolute_url(self):
        return reverse('resources:resource_detail', kwargs={'slug': self.slug})

class ResourceView(models.Model):
    """Track resource views for analytics"""
    resource = models.ForeignKey(Resource, on_delete=models.CASCADE, related_name='views')
    user = models.ForeignKey(User, on_delete=models.CASCADE, blank=True, null=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)
    viewed_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _("Resource View")
        verbose_name_plural = _("Resource Views")
        unique_together = ['resource', 'user', 'ip_address']

    def __str__(self):
        return f"{self.resource.title} - {self.user or self.ip_address}"

class ResourceLike(models.Model):
    """Resource likes/favorites"""
    resource = models.ForeignKey(Resource, on_delete=models.CASCADE, related_name='likes')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='liked_resources')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _("Resource Like")
        verbose_name_plural = _("Resource Likes")
        unique_together = ['resource', 'user']

    def __str__(self):
        return f"{self.user.username} likes {self.resource.title}"
