from django.urls import path
from . import views

app_name = 'resources'

urlpatterns = [
    path('', views.ResourceListView.as_view(), name='resource_list'),
    path('upload/', views.UploadResourceView.as_view(), name='upload_resource'),
    path('category/<slug:slug>/', views.CategoryResourceListView.as_view(), name='category_resources'),
    path('<slug:slug>/', views.ResourceDetailView.as_view(), name='resource_detail'),
    path('<slug:slug>/edit/', views.EditResourceView.as_view(), name='edit_resource'),
    path('<slug:slug>/delete/', views.DeleteResourceView.as_view(), name='delete_resource'),
    path('<slug:slug>/download/', views.DownloadResourceView.as_view(), name='download_resource'),
    path('<slug:slug>/like/', views.LikeResourceView.as_view(), name='like_resource'),
]
