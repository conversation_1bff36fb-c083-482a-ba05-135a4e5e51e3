from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import List<PERSON>iew, DetailView, CreateView, UpdateView, DeleteView, View
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.urls import reverse_lazy
from django.http import HttpResponse, Http404
from django.utils.translation import gettext_lazy as _
from django.db.models import Q
from django.utils.text import slugify
from django.core.paginator import Paginator

from .models import Resource, ResourceCategory, ResourceView, ResourceLike
from .forms import ResourceForm

class ResourceListView(ListView):
    """List all published resources with filtering and search"""
    model = Resource
    template_name = 'resources/resource_list.html'
    context_object_name = 'resources'
    paginate_by = 12

    def get_queryset(self):
        queryset = Resource.objects.filter(is_published=True).select_related('category', 'author')

        # Search functionality
        search_query = self.request.GET.get('search')
        if search_query:
            queryset = queryset.filter(
                Q(title__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(tags__icontains=search_query)
            )

        # Filter by category
        category_slug = self.request.GET.get('category')
        if category_slug:
            queryset = queryset.filter(category__name__iexact=category_slug)

        # Filter by resource type
        resource_type = self.request.GET.get('type')
        if resource_type:
            queryset = queryset.filter(resource_type=resource_type)

        # Filter by difficulty level
        difficulty = self.request.GET.get('difficulty')
        if difficulty:
            queryset = queryset.filter(difficulty_level=difficulty)

        return queryset.order_by('-is_featured', '-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = ResourceCategory.objects.filter(is_active=True)
        context['resource_types'] = Resource.RESOURCE_TYPES
        context['difficulty_levels'] = Resource.DIFFICULTY_LEVELS
        context['search_query'] = self.request.GET.get('search', '')
        context['selected_category'] = self.request.GET.get('category', '')
        context['selected_type'] = self.request.GET.get('type', '')
        context['selected_difficulty'] = self.request.GET.get('difficulty', '')
        return context

class ResourceDetailView(DetailView):
    """Detailed view of a resource"""
    model = Resource
    template_name = 'resources/resource_detail.html'
    context_object_name = 'resource'
    slug_field = 'slug'
    slug_url_kwarg = 'slug'

    def get_queryset(self):
        return Resource.objects.filter(is_published=True).select_related('category', 'author')

    def get_object(self):
        obj = super().get_object()
        # Track view
        if self.request.user.is_authenticated:
            ResourceView.objects.get_or_create(
                resource=obj,
                user=self.request.user,
                defaults={'ip_address': self.get_client_ip()}
            )
        else:
            ResourceView.objects.get_or_create(
                resource=obj,
                ip_address=self.get_client_ip(),
                defaults={'user': None}
            )

        # Update view count
        obj.view_count += 1
        obj.save(update_fields=['view_count'])
        return obj

    def get_client_ip(self):
        x_forwarded_for = self.request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = self.request.META.get('REMOTE_ADDR')
        return ip

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        resource = self.object

        # Check if user has liked this resource
        if self.request.user.is_authenticated:
            context['user_has_liked'] = ResourceLike.objects.filter(
                resource=resource, user=self.request.user
            ).exists()
        else:
            context['user_has_liked'] = False

        # Get related resources
        context['related_resources'] = Resource.objects.filter(
            category=resource.category,
            is_published=True
        ).exclude(id=resource.id)[:4]

        return context

class UploadResourceView(LoginRequiredMixin, CreateView):
    """Upload a new resource (psychologists only)"""
    model = Resource
    form_class = ResourceForm
    template_name = 'resources/resource_upload.html'

    def dispatch(self, request, *args, **kwargs):
        # Check if user is a psychologist
        if not (request.user.is_staff or hasattr(request.user, 'psychologist_profile')):
            messages.error(request, _('Only psychologists can upload resources.'))
            return redirect('resources:resource_list')
        return super().dispatch(request, *args, **kwargs)

    def form_valid(self, form):
        form.instance.author = self.request.user
        if not form.instance.slug:
            form.instance.slug = slugify(form.instance.title)
        messages.success(self.request, _('Resource uploaded successfully!'))
        return super().form_valid(form)

    def get_success_url(self):
        return reverse_lazy('resources:resource_detail', kwargs={'slug': self.object.slug})

class EditResourceView(LoginRequiredMixin, UpdateView):
    """Edit an existing resource (author or admin only)"""
    model = Resource
    form_class = ResourceForm
    template_name = 'resources/resource_form.html'
    slug_field = 'slug'
    slug_url_kwarg = 'slug'

    def dispatch(self, request, *args, **kwargs):
        resource = self.get_object()
        # Check if user can edit this resource
        if not (request.user == resource.author or request.user.is_staff):
            messages.error(request, _('You can only edit your own resources.'))
            return redirect('resources:resource_detail', slug=resource.slug)
        return super().dispatch(request, *args, **kwargs)

    def form_valid(self, form):
        messages.success(self.request, _('Resource updated successfully!'))
        return super().form_valid(form)

    def get_success_url(self):
        return reverse_lazy('resources:resource_detail', kwargs={'slug': self.object.slug})

class DeleteResourceView(LoginRequiredMixin, DeleteView):
    """Delete a resource (author or admin only)"""
    model = Resource
    template_name = 'resources/resource_confirm_delete.html'
    slug_field = 'slug'
    slug_url_kwarg = 'slug'
    success_url = reverse_lazy('resources:resource_list')

    def dispatch(self, request, *args, **kwargs):
        resource = self.get_object()
        # Check if user can delete this resource
        if not (request.user == resource.author or request.user.is_staff):
            messages.error(request, _('You can only delete your own resources.'))
            return redirect('resources:resource_detail', slug=resource.slug)
        return super().dispatch(request, *args, **kwargs)

    def delete(self, request, *args, **kwargs):
        messages.success(request, _('Resource deleted successfully!'))
        return super().delete(request, *args, **kwargs)

class CategoryResourceListView(ListView):
    """List resources by category"""
    model = Resource
    template_name = 'resources/resource_list.html'
    context_object_name = 'resources'
    paginate_by = 12

    def get_queryset(self):
        self.category = get_object_or_404(ResourceCategory, name__iexact=self.kwargs['slug'], is_active=True)
        return Resource.objects.filter(
            category=self.category,
            is_published=True
        ).select_related('category', 'author').order_by('-is_featured', '-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['category'] = self.category
        context['categories'] = ResourceCategory.objects.filter(is_active=True)
        context['resource_types'] = Resource.RESOURCE_TYPES
        context['difficulty_levels'] = Resource.DIFFICULTY_LEVELS
        return context

class DownloadResourceView(LoginRequiredMixin, View):
    """Download a resource file"""

    def get(self, request, slug):
        resource = get_object_or_404(Resource, slug=slug, is_published=True)

        # Determine which file to download
        file_field = None
        if resource.document_file:
            file_field = resource.document_file
        elif resource.video_file:
            file_field = resource.video_file
        elif resource.audio_file:
            file_field = resource.audio_file

        if not file_field:
            messages.error(request, _('No downloadable file available for this resource.'))
            return redirect('resources:resource_detail', slug=slug)

        # Update download count
        resource.download_count += 1
        resource.save(update_fields=['download_count'])

        # Serve the file
        response = HttpResponse(file_field.read(), content_type='application/octet-stream')
        response['Content-Disposition'] = f'attachment; filename="{file_field.name.split("/")[-1]}"'
        return response

class LikeResourceView(LoginRequiredMixin, View):
    """Like/unlike a resource"""

    def post(self, request, slug):
        resource = get_object_or_404(Resource, slug=slug, is_published=True)

        like, created = ResourceLike.objects.get_or_create(
            resource=resource,
            user=request.user
        )

        if created:
            resource.like_count += 1
            resource.save(update_fields=['like_count'])
            messages.success(request, _('Resource liked!'))
        else:
            like.delete()
            resource.like_count = max(0, resource.like_count - 1)
            resource.save(update_fields=['like_count'])
            messages.success(request, _('Resource unliked!'))

        return redirect('resources:resource_detail', slug=slug)
