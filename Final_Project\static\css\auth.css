/* Authentication Pages Styles */

/* Auth Container */
.auth-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 2rem 0;
}

/* Auth Card */
.auth-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    padding: 3rem;
    border: none;
    max-width: 500px;
    width: 100%;
}

/* Auth Header */
.auth-header {
    margin-bottom: 2rem;
}

.auth-title {
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    color: #6c757d;
    font-size: 1rem;
    line-height: 1.5;
}

/* Form Styles */
.auth-form {
    margin-bottom: 2rem;
}

.form-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-control.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    display: block;
    font-size: 0.875rem;
    color: #dc3545;
    margin-top: 0.25rem;
}

/* Password Input Group */
.password-input-group {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 0.5rem;
    z-index: 10;
}

.password-toggle:hover {
    color: #495057;
}

/* Submit Button */
.auth-submit-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 10px;
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.auth-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.auth-submit-btn:active {
    transform: translateY(0);
}

/* Auth Links */
.auth-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.auth-link:hover {
    color: #5a6fd8;
    text-decoration: underline;
}

/* Auth Divider */
.auth-divider {
    position: relative;
    text-align: center;
    margin: 1.5rem 0;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e9ecef;
}

.auth-divider span {
    background: white;
    padding: 0 1rem;
    color: #6c757d;
    font-size: 0.9rem;
}

/* Social Login */
.btn-social {
    border-radius: 10px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    width: 100%;
}

.btn-social:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* Auth Footer */
.auth-footer {
    border-top: 1px solid #e9ecef;
    padding-top: 1.5rem;
    margin-top: 1.5rem;
}

/* Auth Info */
.auth-info {
    background: rgba(255,255,255,0.9);
    border-radius: 15px;
    padding: 2rem;
    backdrop-filter: blur(10px);
}

.info-item {
    padding: 1rem;
    text-align: center;
}

.info-item i {
    margin-bottom: 1rem;
}

.info-item h6 {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* Demo Accounts */
.demo-accounts {
    background: rgba(255,255,255,0.95);
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.demo-account {
    transition: all 0.3s ease;
    border-radius: 8px;
}

.demo-account:hover {
    background-color: #f8f9fa !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.demo-account h6 {
    margin-bottom: 0.5rem;
}

/* Form Check */
.form-check-input {
    border-radius: 4px;
    border: 2px solid #e9ecef;
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.form-check-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-check-label {
    color: #495057;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Alert Styles */
.alert {
    border-radius: 10px;
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
}

.alert-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
}

.alert-success {
    background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
    color: white;
}

.alert-info {
    background: linear-gradient(135deg, #74c0fc 0%, #339af0 100%);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-container {
        padding: 1rem 0;
    }
    
    .auth-card {
        padding: 2rem 1.5rem;
        margin: 1rem;
        border-radius: 15px;
    }
    
    .auth-title {
        font-size: 1.75rem;
    }
    
    .auth-subtitle {
        font-size: 0.9rem;
    }
    
    .form-control {
        padding: 0.625rem 0.875rem;
    }
    
    .auth-submit-btn {
        padding: 0.625rem 1.5rem;
        font-size: 1rem;
    }
    
    .info-item {
        padding: 0.5rem;
        margin-bottom: 1rem;
    }
    
    .demo-account {
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .auth-card {
        padding: 1.5rem 1rem;
    }
    
    .row .col-md-6 {
        margin-bottom: 1rem;
    }
    
    .btn-social {
        font-size: 0.9rem;
        padding: 0.625rem 1rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading States */
.auth-submit-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.auth-submit-btn.loading {
    position: relative;
    color: transparent;
}

.auth-submit-btn.loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Focus Styles for Accessibility */
.form-control:focus,
.btn:focus,
.auth-link:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .auth-card {
        border: 2px solid #000;
    }
    
    .form-control {
        border: 2px solid #000;
    }
    
    .auth-submit-btn {
        background: #000;
        color: #fff;
    }
}
