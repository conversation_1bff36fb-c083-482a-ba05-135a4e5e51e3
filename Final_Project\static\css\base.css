/* Base styles for ECPI Platform */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--dark-color);
}

/* Navigation styles */
.navbar {
    z-index: 1030;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar.sticky-top {
    position: sticky;
    top: 0;
}

/* Main content area */
.main-content {
    min-height: calc(100vh - 200px);
    padding-top: 0;
}

/* Ensure content doesn't hide behind navbar */
body {
    padding-top: 0;
}

/* Custom button styles */
.btn-custom {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-custom:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Card styles */
.card-custom {
    border: none;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card-custom:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

/* Feature cards */
.feature-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.feature-icon {
    margin-bottom: 1rem;
}

/* Navbar customizations */
.navbar-brand img {
    transition: all 0.3s ease;
}

.navbar-brand:hover img {
    transform: scale(1.1);
}

.notification-dropdown {
    min-width: 300px;
    max-height: 400px;
    overflow-y: auto;
}

/* Chatbot widget */
.chatbot-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

#chatbot-toggle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
}

#chatbot-toggle:hover {
    transform: scale(1.1);
}

.chatbot-container {
    position: absolute;
    bottom: 70px;
    right: 0;
    width: 350px;
    height: 500px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 25px rgba(0,0,0,0.2);
    display: flex;
    flex-direction: column;
}

.chatbot-header {
    background: var(--primary-color);
    color: white;
    padding: 15px;
    border-radius: 15px 15px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chatbot-messages {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
    max-height: 350px;
}

.chatbot-input {
    padding: 15px;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 10px;
}

.chatbot-input input {
    flex: 1;
}

/* Message styles */
.message {
    margin-bottom: 15px;
    padding: 10px 15px;
    border-radius: 15px;
    max-width: 80%;
}

.message.user {
    background: var(--primary-color);
    color: white;
    margin-left: auto;
    text-align: right;
}

.message.bot {
    background: var(--light-color);
    color: var(--dark-color);
}

/* Form styles */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--dark-color);
}

/* Alert styles */
.alert {
    border: none;
    border-radius: 10px;
    padding: 15px 20px;
}

/* Footer styles */
footer {
    margin-top: auto;
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 50%;
    background: rgba(255,255,255,0.1);
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .chatbot-container {
        width: 300px;
        height: 400px;
    }
    
    .chatbot-messages {
        max-height: 250px;
    }
    
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .display-4 {
        font-size: 2rem;
    }
}

/* Loading spinner */
.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility classes */
.text-gradient {
    background: linear-gradient(45deg, var(--primary-color), var(--info-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-custom {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.border-radius-custom {
    border-radius: 15px;
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
