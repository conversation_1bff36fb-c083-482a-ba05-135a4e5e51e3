/* Consultation-specific styles */

/* Psychologist Cards */
.psychologist-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.psychologist-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: var(--primary-color);
}

.psychologist-card .rating .fa-star {
    font-size: 0.9rem;
}

.psychologist-card .specialties .badge {
    font-size: 0.75rem;
}

/* Consultation Type Cards */
.consultation-type-card {
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid #e9ecef;
}

.consultation-type-card:hover {
    border-color: var(--primary-color);
    background-color: #f8f9fa;
}

.consultation-type-card.border-primary {
    border-color: var(--primary-color) !important;
    background-color: #f8f9fa;
}

.consultation-type-option input[type="radio"] {
    margin-right: 0.5rem;
}

/* Time Slot Cards */
.time-slot-card {
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid #e9ecef;
}

.time-slot-card:hover {
    border-color: var(--primary-color);
    background-color: #f8f9fa;
}

.time-slot-card.border-primary {
    border-color: var(--primary-color) !important;
    background-color: #f8f9fa;
}

.time-slot-card label {
    cursor: pointer;
    margin: 0;
}

/* Filters Section */
.filters-section {
    border-bottom: 1px solid #e9ecef;
}

.filters-section .form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

/* Rating Stars */
.rating .fa-star {
    color: #ffc107;
}

.rating .fa-star.text-muted {
    color: #dee2e6 !important;
}

/* Availability Status */
.badge .fa-circle {
    font-size: 0.5rem;
    margin-right: 0.25rem;
}

/* Booking Form Styles */
.booking-form .form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.booking-summary {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    padding: 1rem;
}

/* Psychologist Profile Sidebar */
.sticky-top {
    position: sticky;
    top: 100px;
    z-index: 1020;
}

/* Consultation Status Badges */
.status-pending {
    background-color: #ffc107;
    color: #000;
}

.status-confirmed {
    background-color: #198754;
    color: #fff;
}

.status-in-progress {
    background-color: #0dcaf0;
    color: #000;
}

.status-completed {
    background-color: #6c757d;
    color: #fff;
}

.status-cancelled {
    background-color: #dc3545;
    color: #fff;
}

/* Chat Interface */
.chat-container {
    height: 500px;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    display: flex;
    flex-direction: column;
}

.chat-header {
    background-color: var(--primary-color);
    color: white;
    padding: 1rem;
    border-radius: 0.375rem 0.375rem 0 0;
}

.chat-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    background-color: #f8f9fa;
}

.chat-input {
    padding: 1rem;
    border-top: 1px solid #e9ecef;
    background-color: white;
    border-radius: 0 0 0.375rem 0.375rem;
}

.message {
    margin-bottom: 1rem;
    padding: 0.75rem 1rem;
    border-radius: 1rem;
    max-width: 70%;
    word-wrap: break-word;
}

.message.sent {
    background-color: var(--primary-color);
    color: white;
    margin-left: auto;
    text-align: right;
}

.message.received {
    background-color: white;
    color: var(--dark-color);
    border: 1px solid #e9ecef;
}

.message-time {
    font-size: 0.75rem;
    opacity: 0.7;
    margin-top: 0.25rem;
}

/* Dashboard Cards */
.dashboard-card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.dashboard-stat {
    text-align: center;
    padding: 1.5rem;
}

.dashboard-stat .stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--primary-color);
}

.dashboard-stat .stat-label {
    color: var(--secondary-color);
    font-weight: 500;
}

/* Consultation Summary Form */
.summary-form .form-label {
    font-weight: 600;
    color: var(--dark-color);
}

.summary-form .form-control,
.summary-form .form-select {
    border-radius: 0.5rem;
    border: 1px solid #ced4da;
}

.summary-form .form-control:focus,
.summary-form .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Rating Form */
.rating-section {
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.rating-stars input[type="radio"] {
    display: none;
}

.rating-stars label {
    cursor: pointer;
    font-size: 1.5rem;
    color: #dee2e6;
    transition: color 0.2s ease;
}

.rating-stars label:hover,
.rating-stars label:hover ~ label,
.rating-stars input[type="radio"]:checked ~ label {
    color: #ffc107;
}

/* Responsive Design */
@media (max-width: 768px) {
    .psychologist-card {
        margin-bottom: 1rem;
    }
    
    .filters-section .row {
        gap: 1rem;
    }
    
    .chat-container {
        height: 400px;
    }
    
    .message {
        max-width: 85%;
    }
    
    .sticky-top {
        position: relative;
        top: auto;
    }
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

/* Animation Classes */
.fade-in-up {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Custom Scrollbar for Chat */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
