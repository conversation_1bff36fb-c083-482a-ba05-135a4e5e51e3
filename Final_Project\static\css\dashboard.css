/* Dashboard Styles */

/* Welcome Card */
.welcome-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.psychologist-welcome {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

/* Stat Cards */
.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.stat-icon {
    font-size: 2.5rem;
    margin-right: 1rem;
    opacity: 0.8;
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.stat-content p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Dashboard Cards */
.dashboard-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    border: none;
    overflow: hidden;
}

.dashboard-card .card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 1.5rem;
    border-radius: 15px 15px 0 0;
}

.dashboard-card .card-body {
    padding: 1.5rem;
}

/* Consultation Items */
.consultation-item {
    padding: 1rem;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s ease;
}

.consultation-item:last-child {
    border-bottom: none;
}

.consultation-item:hover {
    background-color: #f8f9fa;
}

.today-item {
    background: linear-gradient(90deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);
    border-left: 4px solid #28a745;
}

/* Date and Time Badges */
.date-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    border-radius: 10px;
    padding: 0.5rem;
    min-width: 60px;
}

.date-badge .day {
    font-size: 1.5rem;
    font-weight: bold;
    line-height: 1;
}

.date-badge .month {
    font-size: 0.8rem;
    text-transform: uppercase;
}

.time-badge {
    background: #17a2b8;
    color: white;
    text-align: center;
    border-radius: 8px;
    padding: 0.5rem;
    font-weight: bold;
    min-width: 60px;
}

/* Status Badges */
.status-badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
}

.bg-pending { background-color: #ffc107 !important; }
.bg-confirmed { background-color: #17a2b8 !important; }
.bg-in_progress { background-color: #28a745 !important; }
.bg-completed { background-color: #6f42c1 !important; }
.bg-cancelled { background-color: #dc3545 !important; }

/* Empty States */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    margin-bottom: 1rem;
}

.empty-state h6 {
    margin-bottom: 0.5rem;
    color: #495057;
}

/* Activity Timeline */
.activity-timeline {
    position: relative;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: white;
    font-size: 0.9rem;
}

.activity-content {
    flex-grow: 1;
}

.activity-content h6 {
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.activity-status {
    margin-left: 1rem;
}

/* Notification Items */
.notification-item {
    display: flex;
    align-items: start;
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-icon {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    color: #6c757d;
    font-size: 0.8rem;
}

.notification-content {
    flex-grow: 1;
}

.notification-content h6 {
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

/* Psychologist Items */
.psychologist-item {
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.psychologist-item:last-child {
    border-bottom: none;
}

/* Rating Display */
.rating-display {
    text-align: right;
}

.rating-stars {
    margin-bottom: 0.25rem;
}

.rating-stars i {
    font-size: 1.1rem;
    margin-right: 2px;
}

/* Weekly Chart */
.weekly-chart {
    display: flex;
    justify-content: space-between;
    align-items: end;
    height: 120px;
    padding: 1rem 0;
}

.day-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.day-name {
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    font-weight: 600;
}

.day-bar {
    height: 60px;
    width: 20px;
    background: #e9ecef;
    border-radius: 10px;
    display: flex;
    align-items: end;
    margin-bottom: 0.5rem;
}

.bar-fill {
    width: 100%;
    background: linear-gradient(to top, #28a745, #20c997);
    border-radius: 10px;
    min-height: 2px;
    transition: height 0.3s ease;
}

.day-count {
    font-size: 0.8rem;
    font-weight: bold;
    color: #495057;
}

/* Feedback Items */
.feedback-item {
    padding: 1rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.feedback-item:last-child {
    border-bottom: none;
}

.feedback-item .rating-stars i {
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .welcome-card {
        padding: 1.5rem;
        text-align: center;
    }
    
    .stat-card {
        text-align: center;
        flex-direction: column;
        padding: 1rem;
    }
    
    .stat-icon {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
    
    .consultation-item .row {
        text-align: center;
    }
    
    .consultation-item .row > div {
        margin-bottom: 0.5rem;
    }
    
    .date-badge, .time-badge {
        margin: 0 auto 0.5rem;
    }
    
    .weekly-chart {
        height: 100px;
    }
    
    .day-bar {
        height: 40px;
        width: 15px;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}
