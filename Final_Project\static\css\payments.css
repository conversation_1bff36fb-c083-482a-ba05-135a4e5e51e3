/* Payment-specific styles */

/* Payment Method Selection */
.payment-method-item {
    border: 2px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-method-item:hover {
    border-color: var(--primary-color);
    background-color: #f8f9fa;
}

.payment-method-item.selected {
    border-color: var(--primary-color);
    background-color: #f8f9fa;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.payment-icon img {
    max-height: 40px;
    width: auto;
}

.payment-badges .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
}

/* Price Breakdown */
.price-breakdown .table td {
    padding: 0.75rem 0;
    border: none;
}

.price-breakdown .table .border-top td {
    border-top: 2px solid #dee2e6 !important;
    padding-top: 1rem;
}

/* Security Notice */
.security-notice .alert {
    border: none;
    background-color: rgba(13, 202, 240, 0.1);
}

/* Payment Form */
.payment-form .form-control {
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.payment-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Payment Button */
.payment-button {
    background: linear-gradient(45deg, var(--success-color), #20c997);
    border: none;
    border-radius: 0.5rem;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(32, 201, 151, 0.3);
}

.payment-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(32, 201, 151, 0.4);
}

.payment-button:disabled {
    background: #6c757d;
    transform: none;
    box-shadow: none;
    cursor: not-allowed;
}

/* Success Page Styles */
.success-icon {
    margin-bottom: 2rem;
}

.success-message {
    color: var(--success-color);
    margin-bottom: 2rem;
}

.next-steps .step-item {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.next-steps .step-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    margin-right: 1rem;
}

/* Error Page Styles */
.error-icon {
    color: var(--danger-color);
    margin-bottom: 2rem;
}

.error-message {
    color: var(--danger-color);
    margin-bottom: 2rem;
}

/* Cancel Page Styles */
.cancel-icon {
    color: var(--warning-color);
    margin-bottom: 2rem;
}

.cancel-message {
    color: var(--warning-color);
    margin-bottom: 2rem;
}

/* Payment History Styles */
.payment-history-item {
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.payment-history-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.payment-status {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
}

.payment-status.completed {
    color: var(--success-color);
}

.payment-status.pending {
    color: var(--warning-color);
}

.payment-status.failed {
    color: var(--danger-color);
}

.payment-status.cancelled {
    color: var(--secondary-color);
}

/* Loading States */
.payment-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.payment-loading .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .payment-method-item {
        padding: 0.75rem;
    }
    
    .payment-method-item .d-flex {
        flex-direction: column;
        text-align: center;
    }
    
    .payment-icon {
        margin-bottom: 1rem;
    }
    
    .payment-badges {
        margin-top: 1rem;
    }
    
    .price-breakdown .table {
        font-size: 0.9rem;
    }
    
    .payment-button {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
    
    .step-number {
        width: 30px;
        height: 30px;
        font-size: 0.9rem;
    }
    
    .next-steps .step-item {
        padding: 0.75rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Payment Security Indicators */
.security-badge {
    display: inline-flex;
    align-items: center;
    background-color: rgba(25, 135, 84, 0.1);
    color: var(--success-color);
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.9rem;
    font-weight: 500;
}

.security-badge i {
    margin-right: 0.5rem;
}

/* Payment Amount Display */
.payment-amount {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    text-align: center;
    margin: 1rem 0;
}

.payment-currency {
    font-size: 1.2rem;
    color: var(--secondary-color);
    margin-left: 0.5rem;
}

/* Terms and Conditions */
.terms-checkbox {
    margin-bottom: 1rem;
}

.terms-checkbox .form-check-input {
    margin-top: 0.25rem;
}

.terms-checkbox .form-check-label {
    font-size: 0.9rem;
    line-height: 1.4;
}

.terms-checkbox a {
    color: var(--primary-color);
    text-decoration: none;
}

.terms-checkbox a:hover {
    text-decoration: underline;
}

/* Payment Progress Indicator */
.payment-progress {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    position: relative;
}

.progress-step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 15px;
    right: -50%;
    width: 100%;
    height: 2px;
    background-color: #dee2e6;
    z-index: 1;
}

.progress-step.active::after {
    background-color: var(--primary-color);
}

.progress-step-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #dee2e6;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 2;
}

.progress-step.active .progress-step-icon {
    background-color: var(--primary-color);
}

.progress-step.completed .progress-step-icon {
    background-color: var(--success-color);
}

.progress-step-label {
    font-size: 0.8rem;
    text-align: center;
    color: var(--secondary-color);
}

.progress-step.active .progress-step-label {
    color: var(--primary-color);
    font-weight: 600;
}

/* Custom Scrollbar for Payment Modals */
.modal-body::-webkit-scrollbar {
    width: 6px;
}

.modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
