// Chatbot functionality for ECPI Platform

class Chatbot {
    constructor() {
        this.isOpen = false;
        this.messages = [];
        this.isTyping = false;
        
        this.initializeElements();
        this.bindEvents();
        this.loadWelcomeMessage();
    }

    initializeElements() {
        this.toggleBtn = document.getElementById('chatbot-toggle');
        this.closeBtn = document.getElementById('chatbot-close');
        this.container = document.getElementById('chatbot-container');
        this.messagesContainer = document.getElementById('chatbot-messages');
        this.input = document.getElementById('chatbot-input');
        this.sendBtn = document.getElementById('chatbot-send');
    }

    bindEvents() {
        this.toggleBtn?.addEventListener('click', () => this.toggle());
        this.closeBtn?.addEventListener('click', () => this.close());
        this.sendBtn?.addEventListener('click', () => this.sendMessage());
        this.input?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendMessage();
            }
        });
    }

    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    open() {
        this.container?.classList.remove('d-none');
        this.isOpen = true;
        this.input?.focus();
    }

    close() {
        this.container?.classList.add('d-none');
        this.isOpen = false;
    }

    loadWelcomeMessage() {
        const welcomeMessage = this.getWelcomeMessage();
        this.addMessage(welcomeMessage, 'bot');
    }

    getWelcomeMessage() {
        const messages = [
            "Hello! I'm your mental health assistant. How can I help you today?",
            "Hi there! I'm here to provide support and information about mental health. What would you like to know?",
            "Welcome! I can help you with mental health resources, coping strategies, and general support. How are you feeling today?"
        ];
        return messages[Math.floor(Math.random() * messages.length)];
    }

    async sendMessage() {
        const message = this.input?.value.trim();
        if (!message) return;

        // Add user message
        this.addMessage(message, 'user');
        this.input.value = '';

        // Show typing indicator
        this.showTyping();

        try {
            // Get bot response
            const response = await this.getBotResponse(message);
            this.hideTyping();
            this.addMessage(response, 'bot');
        } catch (error) {
            this.hideTyping();
            this.addMessage("I'm sorry, I'm having trouble responding right now. Please try again later.", 'bot');
        }
    }

    addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;
        messageDiv.textContent = text;
        
        this.messagesContainer?.appendChild(messageDiv);
        this.scrollToBottom();
        
        // Store message
        this.messages.push({ text, sender, timestamp: new Date() });
    }

    showTyping() {
        if (this.isTyping) return;
        
        this.isTyping = true;
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message bot typing-indicator';
        typingDiv.id = 'typing-indicator';
        typingDiv.innerHTML = '<span class="typing-dots"><span>.</span><span>.</span><span>.</span></span>';
        
        this.messagesContainer?.appendChild(typingDiv);
        this.scrollToBottom();
    }

    hideTyping() {
        this.isTyping = false;
        const typingIndicator = document.getElementById('typing-indicator');
        typingIndicator?.remove();
    }

    scrollToBottom() {
        if (this.messagesContainer) {
            this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
        }
    }

    async getBotResponse(message) {
        // First try to get response from predefined responses
        const predefinedResponse = this.getPredefinedResponse(message.toLowerCase());
        if (predefinedResponse) {
            return predefinedResponse;
        }

        // If no predefined response, try to get from server
        try {
            const response = await fetch('/chatbot/response/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': Utils.getCsrfToken()
                },
                body: JSON.stringify({ message: message })
            });

            if (response.ok) {
                const data = await response.json();
                return data.response;
            }
        } catch (error) {
            console.error('Error getting bot response:', error);
        }

        // Fallback to default response
        return this.getDefaultResponse();
    }

    getPredefinedResponse(message) {
        const responses = {
            // Greetings
            'hello': "Hello! How are you feeling today?",
            'hi': "Hi there! I'm here to help with any mental health questions you might have.",
            'hey': "Hey! What's on your mind today?",
            
            // Mental health topics
            'anxiety': "Anxiety is very common and treatable. Some helpful techniques include deep breathing, mindfulness, and regular exercise. Would you like to know more about any of these?",
            'depression': "I understand you're asking about depression. It's important to know that help is available. Consider speaking with a mental health professional. In the meantime, maintaining routines and staying connected with others can help.",
            'stress': "Stress management is important for mental health. Try techniques like deep breathing, regular exercise, adequate sleep, and time management. Would you like specific stress-reduction exercises?",
            'sleep': "Good sleep is crucial for mental health. Try to maintain a regular sleep schedule, avoid screens before bed, and create a relaxing bedtime routine.",
            
            // Crisis situations
            'suicide': "I'm concerned about you. Please reach out to a mental health professional immediately or contact emergency services. You can also call the National Suicide Prevention Lifeline. Your life has value.",
            'self harm': "I'm worried about you. Please talk to someone you trust or a mental health professional. You deserve support and care.",
            
            // Resources
            'help': "I can provide information about mental health topics, coping strategies, and resources. You can also book a consultation with our psychologists or join our community discussions.",
            'resources': "We have many resources available including articles, videos, and audio content about mental health. You can also connect with certified psychologists on our platform.",
            
            // General wellness
            'meditation': "Meditation can be very helpful for mental health. Start with just 5-10 minutes daily. Focus on your breathing or try guided meditations available in our resources section.",
            'exercise': "Regular physical activity is great for mental health! Even a 10-minute walk can help improve mood and reduce stress.",
            
            // Platform specific
            'psychologist': "You can browse and book consultations with our certified psychologists. We offer both free initial consultations and paid sessions.",
            'discussion': "Our community discussions are a great place to connect with others who understand what you're going through. You can share experiences and get support.",
            
            // Common responses
            'thank you': "You're welcome! I'm here whenever you need support or information.",
            'thanks': "You're welcome! Take care of yourself.",
            'bye': "Take care! Remember, I'm here whenever you need support. Don't hesitate to reach out to our psychologists if you need professional help.",
            'goodbye': "Goodbye! Remember that seeking help is a sign of strength. Take care of yourself."
        };

        // Check for keyword matches
        for (const [keyword, response] of Object.entries(responses)) {
            if (message.includes(keyword)) {
                return response;
            }
        }

        return null;
    }

    getDefaultResponse() {
        const responses = [
            "I understand you're reaching out. While I can provide general information, I'd recommend speaking with one of our certified psychologists for personalized support.",
            "That's an important topic. Our mental health resources section has helpful information, and you can also book a consultation with a professional.",
            "I hear you. Sometimes it helps to talk to others who understand. Consider joining our community discussions or booking a session with a psychologist.",
            "Thank you for sharing that with me. For the best support, I'd recommend connecting with one of our mental health professionals.",
            "I appreciate you reaching out. While I can offer general guidance, our certified psychologists can provide more personalized help."
        ];
        
        return responses[Math.floor(Math.random() * responses.length)];
    }
}

// CSS for typing indicator
const style = document.createElement('style');
style.textContent = `
    .typing-indicator {
        padding: 10px 15px;
    }
    
    .typing-dots {
        display: inline-block;
    }
    
    .typing-dots span {
        opacity: 0.4;
        animation: typing 1.4s infinite;
    }
    
    .typing-dots span:nth-child(2) {
        animation-delay: 0.2s;
    }
    
    .typing-dots span:nth-child(3) {
        animation-delay: 0.4s;
    }
    
    @keyframes typing {
        0%, 60%, 100% {
            opacity: 0.4;
        }
        30% {
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);

// Initialize chatbot when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.chatbot = new Chatbot();
});

// Export for global use
window.Chatbot = Chatbot;
