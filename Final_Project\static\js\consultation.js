// Consultation-specific JavaScript functionality

document.addEventListener('DOMContentLoaded', function() {
    // Initialize consultation features
    initializeFilters();
    initializeBookingForm();
    initializeRatingSystem();
    initializeChatSystem();
});

// Filter functionality for psychologist listing
function initializeFilters() {
    const filterForm = document.querySelector('.filters-section form');
    if (!filterForm) return;
    
    // Auto-submit form when filters change
    const filterInputs = filterForm.querySelectorAll('select, input');
    filterInputs.forEach(input => {
        input.addEventListener('change', function() {
            // Add a small delay to prevent rapid submissions
            setTimeout(() => {
                filterForm.submit();
            }, 300);
        });
    });
    
    // Clear filters functionality
    const clearButton = filterForm.querySelector('a[href*="psychologist_list"]');
    if (clearButton) {
        clearButton.addEventListener('click', function(e) {
            e.preventDefault();
            filterInputs.forEach(input => {
                if (input.type === 'text') {
                    input.value = '';
                } else if (input.tagName === 'SELECT') {
                    input.selectedIndex = 0;
                }
            });
            filterForm.submit();
        });
    }
}

// Booking form functionality
function initializeBookingForm() {
    const bookingForm = document.getElementById('booking-form');
    if (!bookingForm) return;
    
    // Consultation type selection
    const consultationTypeInputs = bookingForm.querySelectorAll('input[name="consultation_type"]');
    consultationTypeInputs.forEach(input => {
        input.addEventListener('change', function() {
            // Update UI based on selected consultation type
            updateConsultationTypeUI(this);
        });
    });
    
    // Date selection with validation
    const dateInput = bookingForm.querySelector('input[type="date"]');
    if (dateInput) {
        // Set minimum date to today
        const today = new Date().toISOString().split('T')[0];
        dateInput.setAttribute('min', today);
        
        // Set maximum date to 3 months from now
        const maxDate = new Date();
        maxDate.setMonth(maxDate.getMonth() + 3);
        dateInput.setAttribute('max', maxDate.toISOString().split('T')[0]);
        
        dateInput.addEventListener('change', function() {
            if (this.value) {
                loadAvailableTimeSlots(this.value);
            }
        });
    }
    
    // Form validation
    bookingForm.addEventListener('submit', function(e) {
        if (!validateBookingForm()) {
            e.preventDefault();
            e.stopPropagation();
        }
        this.classList.add('was-validated');
    });
}

function updateConsultationTypeUI(selectedInput) {
    const consultationCards = document.querySelectorAll('.consultation-type-card');
    consultationCards.forEach(card => {
        card.classList.remove('border-primary', 'bg-light');
    });
    
    const selectedCard = selectedInput.closest('.consultation-type-card');
    if (selectedCard) {
        selectedCard.classList.add('border-primary', 'bg-light');
    }
    
    // Show/hide pricing information
    const isFreeCons = selectedInput.dataset.isFree === 'true';
    updatePricingDisplay(isFreeCons);
}

function updatePricingDisplay(isFree) {
    const pricingInfo = document.getElementById('pricing-info');
    if (pricingInfo) {
        if (isFree) {
            pricingInfo.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-gift me-2"></i>
                    This is a free consultation. No payment required.
                </div>
            `;
        } else {
            pricingInfo.innerHTML = `
                <div class="alert alert-info">
                    <i class="fas fa-credit-card me-2"></i>
                    Payment will be required to confirm this consultation.
                </div>
            `;
        }
    }
}

function loadAvailableTimeSlots(selectedDate) {
    const timeSlotsContainer = document.getElementById('time-slots-container');
    const psychologistId = document.querySelector('[data-psychologist-id]')?.dataset.psychologistId;
    
    if (!timeSlotsContainer || !psychologistId) return;
    
    // Show loading state
    timeSlotsContainer.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">Loading available time slots...</p>
        </div>
    `;
    
    // Fetch available slots
    fetch(`/consultation/api/slots/${psychologistId}/?date=${selectedDate}`)
        .then(response => response.json())
        .then(data => {
            displayTimeSlots(data.slots || []);
        })
        .catch(error => {
            console.error('Error loading time slots:', error);
            timeSlotsContainer.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Error loading time slots. Please try again.
                </div>
            `;
        });
}

function displayTimeSlots(slots) {
    const timeSlotsContainer = document.getElementById('time-slots-container');
    
    if (slots.length === 0) {
        timeSlotsContainer.innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-calendar-times fa-2x text-muted mb-3"></i>
                <h5>No available time slots</h5>
                <p class="text-muted">Please select a different date.</p>
            </div>
        `;
        return;
    }
    
    let slotsHTML = '<div class="row g-3">';
    slots.forEach(slot => {
        slotsHTML += `
            <div class="col-md-4 col-sm-6">
                <div class="card time-slot-card" data-slot-id="${slot.id}">
                    <div class="card-body text-center py-3">
                        <input type="radio" name="time_slot" value="${slot.id}" 
                               id="slot_${slot.id}" class="d-none">
                        <label for="slot_${slot.id}" class="w-100 mb-0 cursor-pointer">
                            <div class="fw-bold">${slot.start_time} - ${slot.end_time}</div>
                            <small class="text-muted">${slot.available_spots} spots available</small>
                        </label>
                    </div>
                </div>
            </div>
        `;
    });
    slotsHTML += '</div>';
    
    timeSlotsContainer.innerHTML = slotsHTML;
    
    // Add click handlers for time slot selection
    const timeSlotCards = timeSlotsContainer.querySelectorAll('.time-slot-card');
    timeSlotCards.forEach(card => {
        card.addEventListener('click', function() {
            selectTimeSlot(this);
        });
    });
}

function selectTimeSlot(selectedCard) {
    // Remove selection from other cards
    document.querySelectorAll('.time-slot-card').forEach(card => {
        card.classList.remove('border-primary', 'bg-light');
    });
    
    // Select the clicked card
    selectedCard.classList.add('border-primary', 'bg-light');
    
    // Check the radio button
    const radio = selectedCard.querySelector('input[type="radio"]');
    if (radio) {
        radio.checked = true;
    }
    
    // Update submit button state
    updateSubmitButtonState();
}

function validateBookingForm() {
    const consultationType = document.querySelector('input[name="consultation_type"]:checked');
    const date = document.querySelector('input[name="preferred_date"]').value;
    const timeSlot = document.querySelector('input[name="time_slot"]:checked');
    
    if (!consultationType) {
        Utils.showToast('Please select a consultation type.', 'warning');
        return false;
    }
    
    if (!date) {
        Utils.showToast('Please select a date.', 'warning');
        return false;
    }
    
    if (!timeSlot) {
        Utils.showToast('Please select a time slot.', 'warning');
        return false;
    }
    
    return true;
}

function updateSubmitButtonState() {
    const submitButton = document.getElementById('submit-booking');
    const consultationType = document.querySelector('input[name="consultation_type"]:checked');
    const timeSlot = document.querySelector('input[name="time_slot"]:checked');
    
    if (submitButton) {
        if (consultationType && timeSlot) {
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="fas fa-calendar-check me-2"></i>Book Consultation';
        } else {
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-calendar-plus me-2"></i>Select consultation type and time';
        }
    }
}

// Rating system functionality
function initializeRatingSystem() {
    const ratingInputs = document.querySelectorAll('.rating-stars input[type="radio"]');
    
    ratingInputs.forEach(input => {
        input.addEventListener('change', function() {
            updateStarDisplay(this);
        });
    });
    
    // Initialize star displays
    document.querySelectorAll('.rating-stars').forEach(ratingGroup => {
        const checkedInput = ratingGroup.querySelector('input:checked');
        if (checkedInput) {
            updateStarDisplay(checkedInput);
        }
    });
}

function updateStarDisplay(selectedInput) {
    const ratingGroup = selectedInput.closest('.rating-stars');
    const allInputs = ratingGroup.querySelectorAll('input[type="radio"]');
    const selectedValue = parseInt(selectedInput.value);
    
    allInputs.forEach((input, index) => {
        const label = input.nextElementSibling;
        if (label) {
            if (index < selectedValue) {
                label.style.color = '#ffc107';
            } else {
                label.style.color = '#dee2e6';
            }
        }
    });
}

// Chat system functionality
function initializeChatSystem() {
    const chatContainer = document.querySelector('.chat-container');
    if (!chatContainer) return;
    
    const messagesContainer = chatContainer.querySelector('.chat-messages');
    const messageInput = chatContainer.querySelector('input[type="text"]');
    const sendButton = chatContainer.querySelector('.btn-send');
    
    if (messageInput && sendButton) {
        // Send message on button click
        sendButton.addEventListener('click', function() {
            sendMessage();
        });
        
        // Send message on Enter key
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    }
    
    // Auto-scroll to bottom of messages
    if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
}

function sendMessage() {
    const messageInput = document.querySelector('.chat-input input[type="text"]');
    const message = messageInput.value.trim();
    
    if (!message) return;
    
    // Clear input
    messageInput.value = '';
    
    // Add message to chat (this would typically be handled by WebSocket)
    addMessageToChat(message, 'sent');
    
    // Here you would typically send the message via WebSocket
    // For now, we'll just simulate a response
    setTimeout(() => {
        addMessageToChat('Thank you for your message. The psychologist will respond shortly.', 'received');
    }, 1000);
}

function addMessageToChat(message, type) {
    const messagesContainer = document.querySelector('.chat-messages');
    if (!messagesContainer) return;
    
    const messageElement = document.createElement('div');
    messageElement.className = `message ${type}`;
    messageElement.innerHTML = `
        <div>${message}</div>
        <div class="message-time">${new Date().toLocaleTimeString()}</div>
    `;
    
    messagesContainer.appendChild(messageElement);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Utility functions
const ConsultationUtils = {
    formatTime: function(timeString) {
        const time = new Date(`2000-01-01T${timeString}`);
        return time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    },
    
    formatDate: function(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString([], { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        });
    },
    
    calculateDuration: function(startTime, endTime) {
        const start = new Date(`2000-01-01T${startTime}`);
        const end = new Date(`2000-01-01T${endTime}`);
        const diffMs = end - start;
        const diffMins = Math.floor(diffMs / 60000);
        return diffMins;
    }
};

// Export for global use
window.ConsultationUtils = ConsultationUtils;
