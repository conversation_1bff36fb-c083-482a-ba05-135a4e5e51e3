// Dashboard JavaScript functionality

document.addEventListener('DOMContentLoaded', function() {
    // Initialize dashboard features
    initializeDashboard();
    initializeNotifications();
    initializeQuickStats();
    initializeAnimations();
});

function initializeDashboard() {
    // Add fade-in animation to dashboard cards
    const cards = document.querySelectorAll('.dashboard-card, .stat-card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in');
        }, index * 100);
    });
    
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

function initializeNotifications() {
    // Auto-refresh notifications every 5 minutes
    setInterval(refreshNotifications, 300000);
    
    // Mark notifications as read when clicked
    const notificationItems = document.querySelectorAll('.notification-item');
    notificationItems.forEach(item => {
        item.addEventListener('click', function() {
            const notificationId = this.dataset.notificationId;
            if (notificationId) {
                markNotificationAsRead(notificationId);
            }
        });
    });
}

function initializeQuickStats() {
    // Refresh quick stats every 2 minutes
    setInterval(refreshQuickStats, 120000);
    
    // Add hover effects to stat cards
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

function initializeAnimations() {
    // Animate numbers in stat cards
    const statNumbers = document.querySelectorAll('.stat-content h3');
    statNumbers.forEach(number => {
        animateNumber(number);
    });
    
    // Animate progress bars
    const progressBars = document.querySelectorAll('.bar-fill');
    progressBars.forEach(bar => {
        const height = bar.style.height;
        bar.style.height = '2px';
        setTimeout(() => {
            bar.style.height = height;
        }, 500);
    });
}

function animateNumber(element) {
    const finalNumber = parseInt(element.textContent);
    const duration = 1000;
    const increment = finalNumber / (duration / 16);
    let currentNumber = 0;
    
    const timer = setInterval(() => {
        currentNumber += increment;
        if (currentNumber >= finalNumber) {
            currentNumber = finalNumber;
            clearInterval(timer);
        }
        element.textContent = Math.floor(currentNumber);
    }, 16);
}

function refreshNotifications() {
    // Fetch latest notifications
    fetch('/api/notifications/')
        .then(response => response.json())
        .then(data => {
            updateNotificationBadge(data.unread_count);
        })
        .catch(error => {
            console.error('Error refreshing notifications:', error);
        });
}

function refreshQuickStats() {
    // Fetch latest quick stats
    fetch('/api/quick-stats/')
        .then(response => response.json())
        .then(data => {
            updateQuickStats(data);
        })
        .catch(error => {
            console.error('Error refreshing quick stats:', error);
        });
}

function updateNotificationBadge(count) {
    const badges = document.querySelectorAll('.notification-badge');
    badges.forEach(badge => {
        badge.textContent = count;
        badge.style.display = count > 0 ? 'inline' : 'none';
    });
}

function updateQuickStats(stats) {
    // Update stat cards with new data
    Object.keys(stats).forEach(key => {
        const element = document.querySelector(`[data-stat="${key}"]`);
        if (element) {
            const currentValue = parseInt(element.textContent);
            const newValue = stats[key];
            
            if (currentValue !== newValue) {
                element.textContent = newValue;
                element.parentElement.classList.add('updated');
                setTimeout(() => {
                    element.parentElement.classList.remove('updated');
                }, 2000);
            }
        }
    });
}

function markNotificationAsRead(notificationId) {
    fetch(`/notifications/${notificationId}/read/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCookie('csrftoken'),
            'Content-Type': 'application/json',
        },
    })
    .then(response => {
        if (response.ok) {
            // Update UI to show notification as read
            const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
            if (notificationElement) {
                notificationElement.classList.add('read');
            }
        }
    })
    .catch(error => {
        console.error('Error marking notification as read:', error);
    });
}

// Utility functions
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Dashboard-specific utilities
const DashboardUtils = {
    // Format time for display
    formatTime: function(timeString) {
        const time = new Date(`2000-01-01T${timeString}`);
        return time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    },
    
    // Format date for display
    formatDate: function(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString([], { 
            weekday: 'short', 
            month: 'short', 
            day: 'numeric' 
        });
    },
    
    // Get status color class
    getStatusColor: function(status) {
        const statusColors = {
            'pending': 'warning',
            'confirmed': 'info',
            'in_progress': 'success',
            'completed': 'primary',
            'cancelled': 'danger'
        };
        return statusColors[status] || 'secondary';
    },
    
    // Show loading state
    showLoading: function(element) {
        element.innerHTML = `
            <div class="d-flex justify-content-center align-items-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        `;
    },
    
    // Show error state
    showError: function(element, message = 'An error occurred') {
        element.innerHTML = `
            <div class="alert alert-danger text-center">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
            </div>
        `;
    },
    
    // Show empty state
    showEmpty: function(element, message = 'No data available', icon = 'inbox') {
        element.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-${icon} fa-3x text-muted mb-3"></i>
                <h6>${message}</h6>
            </div>
        `;
    }
};

// Real-time updates for consultation status
function initializeRealTimeUpdates() {
    // Check for consultation status updates every 30 seconds
    setInterval(() => {
        const consultationItems = document.querySelectorAll('[data-consultation-id]');
        consultationItems.forEach(item => {
            const consultationId = item.dataset.consultationId;
            checkConsultationStatus(consultationId);
        });
    }, 30000);
}

function checkConsultationStatus(consultationId) {
    fetch(`/api/consultation/${consultationId}/status/`)
        .then(response => response.json())
        .then(data => {
            updateConsultationStatus(consultationId, data.status);
        })
        .catch(error => {
            console.error('Error checking consultation status:', error);
        });
}

function updateConsultationStatus(consultationId, newStatus) {
    const statusElement = document.querySelector(`[data-consultation-id="${consultationId}"] .status-badge`);
    if (statusElement) {
        const currentStatus = statusElement.textContent.toLowerCase().replace(' ', '_');
        if (currentStatus !== newStatus) {
            statusElement.className = `badge bg-${DashboardUtils.getStatusColor(newStatus)} status-badge`;
            statusElement.textContent = newStatus.replace('_', ' ').toUpperCase();
            
            // Show notification
            showStatusUpdateNotification(consultationId, newStatus);
        }
    }
}

function showStatusUpdateNotification(consultationId, status) {
    // Create and show a toast notification
    const toast = document.createElement('div');
    toast.className = 'toast position-fixed top-0 end-0 m-3';
    toast.innerHTML = `
        <div class="toast-header">
            <i class="fas fa-bell text-primary me-2"></i>
            <strong class="me-auto">Consultation Update</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body">
            Consultation status updated to: ${status.replace('_', ' ')}
        </div>
    `;
    
    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', () => {
        document.body.removeChild(toast);
    });
}

// Initialize real-time updates if on dashboard
if (window.location.pathname.includes('dashboard')) {
    initializeRealTimeUpdates();
}

// Export utilities for global use
window.DashboardUtils = DashboardUtils;
