{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "My Profile" %} - ECPI{% endblock %}

{% block extra_css %}
<link href="{% static 'css/dashboard.css' %}" rel="stylesheet">
<style>
.profile-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.profile-card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 15px;
    margin-bottom: 1.5rem;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border: 4px solid white;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.info-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
}

.info-value {
    color: #6c757d;
}

.badge-language {
    background: #e3f2fd;
    color: #1976d2;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.stats-card {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    margin-bottom: 1rem;
}

.stats-number {
    font-size: 2rem;
    font-weight: bold;
    color: #495057;
}

.stats-label {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.verification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #28a745;
    color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
}

.privacy-setting {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.privacy-setting:last-child {
    border-bottom: none;
}
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Profile Header -->
    <div class="profile-header">
        <div class="row align-items-center">
            <div class="col-md-3 text-center">
                <div class="position-relative d-inline-block">
                    <img src="{% if user.profile.avatar %}{{ user.profile.avatar.url }}{% else %}{% static 'images/default-avatar.png' %}{% endif %}" 
                         alt="Profile Picture" class="rounded-circle profile-avatar">
                    {% if user.profile.is_verified %}
                        <div class="verification-badge">
                            <i class="fas fa-check"></i>
                        </div>
                    {% endif %}
                </div>
            </div>
            <div class="col-md-6">
                <h2 class="mb-1">{{ user.get_full_name|default:user.username }}</h2>
                <p class="mb-2 opacity-75">
                    {% if user.profile.is_psychologist %}
                        <i class="fas fa-user-md me-2"></i>{% trans "Psychologist" %}
                    {% else %}
                        <i class="fas fa-user me-2"></i>{% trans "Patient" %}
                    {% endif %}
                </p>
                <p class="mb-0 opacity-75">
                    <i class="fas fa-envelope me-2"></i>{{ user.email }}
                    {% if user.profile.show_email %}
                        <span class="badge bg-light text-dark ms-2">{% trans "Public" %}</span>
                    {% else %}
                        <span class="badge bg-secondary ms-2">{% trans "Private" %}</span>
                    {% endif %}
                </p>
                {% if user.profile.phone %}
                    <p class="mb-0 opacity-75">
                        <i class="fas fa-phone me-2"></i>{{ user.profile.phone }}
                        {% if user.profile.show_phone %}
                            <span class="badge bg-light text-dark ms-2">{% trans "Public" %}</span>
                        {% else %}
                            <span class="badge bg-secondary ms-2">{% trans "Private" %}</span>
                        {% endif %}
                    </p>
                {% endif %}
            </div>
            <div class="col-md-3 text-end">
                <a href="{% url 'accounts:edit_profile' %}" class="btn btn-light btn-lg">
                    <i class="fas fa-edit me-2"></i>{% trans "Edit Profile" %}
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Left Column - Profile Information -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="profile-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>{% trans "Basic Information" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <div class="info-label">{% trans "Full Name" %}</div>
                                <div class="info-value">{{ user.get_full_name|default:user.username }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">{% trans "Username" %}</div>
                                <div class="info-value">{{ user.username }}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">{% trans "Email" %}</div>
                                <div class="info-value">{{ user.email }}</div>
                            </div>
                            {% if user.profile.phone %}
                                <div class="info-item">
                                    <div class="info-label">{% trans "Phone" %}</div>
                                    <div class="info-value">{{ user.profile.phone }}</div>
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            {% if user.profile.birth_date %}
                                <div class="info-item">
                                    <div class="info-label">{% trans "Date of Birth" %}</div>
                                    <div class="info-value">
                                        {{ user.profile.birth_date|date:"F d, Y" }}
                                        {% if user.profile.age %}
                                            <span class="text-muted">({{ user.profile.age }} {% trans "years old" %})</span>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endif %}
                            {% if user.profile.gender %}
                                <div class="info-item">
                                    <div class="info-label">{% trans "Gender" %}</div>
                                    <div class="info-value">{{ user.profile.get_gender_display }}</div>
                                </div>
                            {% endif %}
                            <div class="info-item">
                                <div class="info-label">{% trans "Language Preference" %}</div>
                                <div class="info-value">
                                    {% if user.profile.preferred_language == 'en' %}English{% else %}አማርኛ{% endif %}
                                </div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">{% trans "Member Since" %}</div>
                                <div class="info-value">{{ user.date_joined|date:"F Y" }}</div>
                            </div>
                        </div>
                    </div>
                    
                    {% if user.profile.bio %}
                        <div class="mt-3">
                            <div class="info-label">{% trans "Bio" %}</div>
                            <div class="info-value">{{ user.profile.bio|linebreaks }}</div>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Location Information -->
            {% if user.profile.address or user.profile.city %}
                <div class="profile-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-map-marker-alt me-2"></i>{% trans "Location" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if user.profile.address %}
                            <div class="info-item">
                                <div class="info-label">{% trans "Address" %}</div>
                                <div class="info-value">{{ user.profile.address }}</div>
                            </div>
                        {% endif %}
                        <div class="row">
                            {% if user.profile.city %}
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <div class="info-label">{% trans "City" %}</div>
                                        <div class="info-value">{{ user.profile.city }}</div>
                                    </div>
                                </div>
                            {% endif %}
                            <div class="col-md-6">
                                <div class="info-item">
                                    <div class="info-label">{% trans "Country" %}</div>
                                    <div class="info-value">{{ user.profile.country }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Psychologist Specific Information -->
            {% if user.profile.is_psychologist and user.psychologist_profile %}
                <div class="profile-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-user-md me-2"></i>{% trans "Professional Information" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-item">
                                    <div class="info-label">{% trans "Years of Experience" %}</div>
                                    <div class="info-value">{{ user.psychologist_profile.years_of_experience }} {% trans "years" %}</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">{% trans "License Number" %}</div>
                                    <div class="info-value">{{ user.psychologist_profile.license_number|default:"Not provided" }}</div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">{% trans "Consultation Fee" %}</div>
                                    <div class="info-value">{{ user.psychologist_profile.consultation_fee }} ETB</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-item">
                                    <div class="info-label">{% trans "Approval Status" %}</div>
                                    <div class="info-value">
                                        <span class="badge bg-{{ user.psychologist_profile.approval_status }}">
                                            {{ user.psychologist_profile.get_approval_status_display }}
                                        </span>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">{% trans "Available Languages" %}</div>
                                    <div class="info-value">
                                        {% for lang in user.psychologist_profile.language_list %}
                                            <span class="badge-language">
                                                {% if lang == 'en' %}English{% elif lang == 'am' %}አማርኛ{% else %}{{ lang }}{% endif %}
                                            </span>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        {% if user.psychologist_profile.education %}
                            <div class="mt-3">
                                <div class="info-label">{% trans "Education" %}</div>
                                <div class="info-value">{{ user.psychologist_profile.education|linebreaks }}</div>
                            </div>
                        {% endif %}
                        
                        {% if user.psychologist_profile.specializations %}
                            <div class="mt-3">
                                <div class="info-label">{% trans "Specializations" %}</div>
                                <div class="info-value">{{ user.psychologist_profile.specializations|linebreaks }}</div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Right Column - Statistics and Settings -->
        <div class="col-lg-4">
            <!-- Account Statistics -->
            <div class="profile-card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>{% trans "Account Statistics" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if user.profile.is_psychologist %}
                        <!-- Psychologist Stats -->
                        <div class="stats-card">
                            <div class="stats-number">{{ total_consultations|default:0 }}</div>
                            <div class="stats-label">{% trans "Total Consultations" %}</div>
                        </div>
                        <div class="stats-card">
                            <div class="stats-number">{{ average_rating|default:0|floatformat:1 }}</div>
                            <div class="stats-label">{% trans "Average Rating" %}</div>
                        </div>
                        <div class="stats-card">
                            <div class="stats-number">{{ total_ratings|default:0 }}</div>
                            <div class="stats-label">{% trans "Total Reviews" %}</div>
                        </div>
                    {% else %}
                        <!-- Patient Stats -->
                        <div class="stats-card">
                            <div class="stats-number">{{ total_consultations|default:0 }}</div>
                            <div class="stats-label">{% trans "Total Consultations" %}</div>
                        </div>
                        <div class="stats-card">
                            <div class="stats-number">{{ completed_consultations|default:0 }}</div>
                            <div class="stats-label">{% trans "Completed Sessions" %}</div>
                        </div>
                        <div class="stats-card">
                            <div class="stats-number">{{ upcoming_consultations|default:0 }}</div>
                            <div class="stats-label">{% trans "Upcoming Sessions" %}</div>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Privacy Settings -->
            <div class="profile-card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>{% trans "Privacy Settings" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="privacy-setting">
                        <div>
                            <div class="info-label">{% trans "Show Email Publicly" %}</div>
                            <small class="text-muted">{% trans "Allow others to see your email address" %}</small>
                        </div>
                        <div>
                            {% if user.profile.show_email %}
                                <span class="badge bg-success">{% trans "Enabled" %}</span>
                            {% else %}
                                <span class="badge bg-secondary">{% trans "Disabled" %}</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="privacy-setting">
                        <div>
                            <div class="info-label">{% trans "Show Phone Publicly" %}</div>
                            <small class="text-muted">{% trans "Allow others to see your phone number" %}</small>
                        </div>
                        <div>
                            {% if user.profile.show_phone %}
                                <span class="badge bg-success">{% trans "Enabled" %}</span>
                            {% else %}
                                <span class="badge bg-secondary">{% trans "Disabled" %}</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="privacy-setting">
                        <div>
                            <div class="info-label">{% trans "Allow Messages" %}</div>
                            <small class="text-muted">{% trans "Allow other users to send you messages" %}</small>
                        </div>
                        <div>
                            {% if user.profile.allow_messages %}
                                <span class="badge bg-success">{% trans "Enabled" %}</span>
                            {% else %}
                                <span class="badge bg-secondary">{% trans "Disabled" %}</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="profile-card">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>{% trans "Quick Actions" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'accounts:edit_profile' %}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>{% trans "Edit Profile" %}
                        </a>
                        <a href="{% url 'accounts:change_password' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-key me-2"></i>{% trans "Change Password" %}
                        </a>
                        <a href="{% url 'core:dashboard' %}" class="btn btn-outline-info">
                            <i class="fas fa-tachometer-alt me-2"></i>{% trans "Dashboard" %}
                        </a>
                        {% if user.profile.is_psychologist %}
                            <a href="{% url 'consultation:manage_schedule' %}" class="btn btn-outline-success">
                                <i class="fas fa-calendar me-2"></i>{% trans "Manage Schedule" %}
                            </a>
                        {% else %}
                            <a href="{% url 'consultation:psychologist_list' %}" class="btn btn-outline-success">
                                <i class="fas fa-search me-2"></i>{% trans "Find Psychologist" %}
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add any profile-specific JavaScript here
    console.log('Profile page loaded');
});
</script>
{% endblock %}
