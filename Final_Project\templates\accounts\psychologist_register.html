{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Join as Psychologist" %} - ECPI{% endblock %}

{% block extra_css %}
<link href="{% static 'css/auth.css' %}" rel="stylesheet">
<style>
.psychologist-auth-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 2rem 0;
}

.step-indicator {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
}

.step {
    display: flex;
    align-items: center;
    margin: 0 1rem;
}

.step-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 0.5rem;
}

.step.active .step-number {
    background: #667eea;
    color: white;
}

.step.completed .step-number {
    background: #764ba2;
    color: white;
}

.form-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid #667eea;
}

.form-section h5 {
    color: #667eea;
    margin-bottom: 1rem;
    font-weight: 600;
}

.file-upload-area {
    border: 2px dashed #667eea;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    background: rgba(102, 126, 234, 0.05);
    transition: all 0.3s ease;
}

.file-upload-area:hover {
    background: rgba(102, 126, 234, 0.1);
}

.file-upload-area.dragover {
    border-color: #764ba2;
    background: rgba(118, 75, 162, 0.1);
}

.requirements-list {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.requirements-list h6 {
    color: #856404;
    margin-bottom: 0.5rem;
}

.requirements-list ul {
    margin: 0;
    padding-left: 1.5rem;
    color: #856404;
}
</style>
{% endblock %}

{% block content %}
<div class="psychologist-auth-container d-flex align-items-center justify-content-center" style="min-height: 100vh;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 d-flex justify-content-center">
                <div class="auth-card w-100" style="max-width: 700px;">
                    <!-- Header -->
                    <div class="auth-header text-center mb-4">
                        <img src="{% static 'images/logo.png' %}" alt="ECPI Logo" height="60" class="mb-3"
                             onerror="this.style.display='none';">
                        <h2 class="auth-title">{% trans "Join as a Psychologist" %}</h2>
                        <p class="auth-subtitle text-muted">
                            {% trans "Help people on their mental health journey" %}
                        </p>
                    </div>

                    <!-- Step Indicator -->
                    <div class="step-indicator">
                        <div class="step active">
                            <div class="step-number">1</div>
                            <span>{% trans "Registration" %}</span>
                        </div>
                        <div class="step">
                            <div class="step-number">2</div>
                            <span>{% trans "Review" %}</span>
                        </div>
                        <div class="step">
                            <div class="step-number">3</div>
                            <span>{% trans "Approval" %}</span>
                        </div>
                    </div>

                    <!-- Requirements -->
                    <div class="requirements-list">
                        <h6><i class="fas fa-info-circle me-2"></i>{% trans "Requirements to Join" %}</h6>
                        <ul>
                            <li>{% trans "Valid psychology license" %}</li>
                            <li>{% trans "Minimum 1 year of experience" %}</li>
                            <li>{% trans "Educational credentials" %}</li>
                            <li>{% trans "Professional references" %}</li>
                        </ul>
                    </div>

                    <!-- Registration Form -->
                    <form method="post" enctype="multipart/form-data" class="auth-form" id="psychologist-form">
                        {% csrf_token %}
                        
                        <!-- Display form errors -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}

                        <!-- Personal Information Section -->
                        <div class="form-section">
                            <h5><i class="fas fa-user me-2"></i>{% trans "Personal Information" %}</h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                            {% trans "First Name" %} <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" 
                                               class="form-control {% if form.first_name.errors %}is-invalid{% endif %}" 
                                               id="{{ form.first_name.id_for_label }}"
                                               name="{{ form.first_name.name }}" 
                                               value="{{ form.first_name.value|default:'' }}"
                                               required>
                                        {% if form.first_name.errors %}
                                            <div class="invalid-feedback">{{ form.first_name.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                            {% trans "Last Name" %} <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" 
                                               class="form-control {% if form.last_name.errors %}is-invalid{% endif %}" 
                                               id="{{ form.last_name.id_for_label }}"
                                               name="{{ form.last_name.name }}" 
                                               value="{{ form.last_name.value|default:'' }}"
                                               required>
                                        {% if form.last_name.errors %}
                                            <div class="invalid-feedback">{{ form.last_name.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="{{ form.username.id_for_label }}" class="form-label">
                                            {% trans "Username" %} <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" 
                                               class="form-control {% if form.username.errors %}is-invalid{% endif %}" 
                                               id="{{ form.username.id_for_label }}"
                                               name="{{ form.username.name }}" 
                                               value="{{ form.username.value|default:'' }}"
                                               required>
                                        {% if form.username.errors %}
                                            <div class="invalid-feedback">{{ form.username.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="{{ form.email.id_for_label }}" class="form-label">
                                            {% trans "Email Address" %} <span class="text-danger">*</span>
                                        </label>
                                        <input type="email" 
                                               class="form-control {% if form.email.errors %}is-invalid{% endif %}" 
                                               id="{{ form.email.id_for_label }}"
                                               name="{{ form.email.name }}" 
                                               value="{{ form.email.value|default:'' }}"
                                               required>
                                        {% if form.email.errors %}
                                            <div class="invalid-feedback">{{ form.email.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="{{ form.password1.id_for_label }}" class="form-label">
                                            {% trans "Password" %} <span class="text-danger">*</span>
                                        </label>
                                        <input type="password" 
                                               class="form-control {% if form.password1.errors %}is-invalid{% endif %}" 
                                               id="{{ form.password1.id_for_label }}"
                                               name="{{ form.password1.name }}" 
                                               required>
                                        {% if form.password1.errors %}
                                            <div class="invalid-feedback">{{ form.password1.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="{{ form.password2.id_for_label }}" class="form-label">
                                            {% trans "Confirm Password" %} <span class="text-danger">*</span>
                                        </label>
                                        <input type="password" 
                                               class="form-control {% if form.password2.errors %}is-invalid{% endif %}" 
                                               id="{{ form.password2.id_for_label }}"
                                               name="{{ form.password2.name }}" 
                                               required>
                                        {% if form.password2.errors %}
                                            <div class="invalid-feedback">{{ form.password2.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Professional Information Section -->
                        <div class="form-section">
                            <h5><i class="fas fa-graduation-cap me-2"></i>{% trans "Professional Information" %}</h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="{{ form.license_number.id_for_label }}" class="form-label">
                                            {% trans "License Number" %} <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" 
                                               class="form-control {% if form.license_number.errors %}is-invalid{% endif %}" 
                                               id="{{ form.license_number.id_for_label }}"
                                               name="{{ form.license_number.name }}" 
                                               value="{{ form.license_number.value|default:'' }}"
                                               required>
                                        {% if form.license_number.errors %}
                                            <div class="invalid-feedback">{{ form.license_number.errors.0 }}</div>
                                        {% endif %}
                                        <small class="form-text text-muted">{{ form.license_number.help_text }}</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="{{ form.years_of_experience.id_for_label }}" class="form-label">
                                            {% trans "Years of Experience" %} <span class="text-danger">*</span>
                                        </label>
                                        <input type="number" 
                                               class="form-control {% if form.years_of_experience.errors %}is-invalid{% endif %}" 
                                               id="{{ form.years_of_experience.id_for_label }}"
                                               name="{{ form.years_of_experience.name }}" 
                                               value="{{ form.years_of_experience.value|default:'' }}"
                                               min="0" max="50"
                                               required>
                                        {% if form.years_of_experience.errors %}
                                            <div class="invalid-feedback">{{ form.years_of_experience.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <div class="form-group mb-3">
                                <label for="{{ form.education.id_for_label }}" class="form-label">
                                    {% trans "Educational Background" %} <span class="text-danger">*</span>
                                </label>
                                <textarea class="form-control {% if form.education.errors %}is-invalid{% endif %}" 
                                          id="{{ form.education.id_for_label }}"
                                          name="{{ form.education.name }}" 
                                          rows="3"
                                          required>{{ form.education.value|default:'' }}</textarea>
                                {% if form.education.errors %}
                                    <div class="invalid-feedback">{{ form.education.errors.0 }}</div>
                                {% endif %}
                                <small class="form-text text-muted">{{ form.education.help_text }}</small>
                            </div>

                            <div class="form-group mb-3">
                                <label for="{{ form.specializations.id_for_label }}" class="form-label">
                                    {% trans "Specializations" %} <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control {% if form.specializations.errors %}is-invalid{% endif %}" 
                                       id="{{ form.specializations.id_for_label }}"
                                       name="{{ form.specializations.name }}" 
                                       value="{{ form.specializations.value|default:'' }}"
                                       placeholder="{% trans 'e.g., Clinical Psychology, CBT, Trauma Therapy' %}"
                                       required>
                                {% if form.specializations.errors %}
                                    <div class="invalid-feedback">{{ form.specializations.errors.0 }}</div>
                                {% endif %}
                                <small class="form-text text-muted">{{ form.specializations.help_text }}</small>
                            </div>
                        </div>

                        <!-- Documents Section -->
                        <div class="form-section">
                            <h5><i class="fas fa-file-upload me-2"></i>{% trans "Required Documents" %}</h5>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="{{ form.license_document.id_for_label }}" class="form-label">
                                            {% trans "License Document" %} <span class="text-danger">*</span>
                                        </label>
                                        <div class="file-upload-area">
                                            <i class="fas fa-cloud-upload-alt fa-2x text-success mb-2"></i>
                                            <p class="mb-2">{% trans "Upload your license document" %}</p>
                                            <input type="file"
                                                   class="form-control {% if form.license_document.errors %}is-invalid{% endif %}"
                                                   id="{{ form.license_document.id_for_label }}"
                                                   name="{{ form.license_document.name }}"
                                                   accept=".pdf,.jpg,.jpeg,.png"
                                                   required>
                                            <small class="text-muted">PDF, JPG, PNG (Max 5MB)</small>
                                        </div>
                                        {% if form.license_document.errors %}
                                            <div class="invalid-feedback d-block">{{ form.license_document.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label for="{{ form.cv_document.id_for_label }}" class="form-label">
                                            {% trans "CV/Resume" %} <span class="text-muted">({% trans "Optional" %})</span>
                                        </label>
                                        <div class="file-upload-area">
                                            <i class="fas fa-file-pdf fa-2x text-info mb-2"></i>
                                            <p class="mb-2">{% trans "Upload your CV or resume" %}</p>
                                            <input type="file"
                                                   class="form-control {% if form.cv_document.errors %}is-invalid{% endif %}"
                                                   id="{{ form.cv_document.id_for_label }}"
                                                   name="{{ form.cv_document.name }}"
                                                   accept=".pdf">
                                            <small class="text-muted">PDF only (Max 5MB)</small>
                                        </div>
                                        {% if form.cv_document.errors %}
                                            <div class="invalid-feedback d-block">{{ form.cv_document.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>



                        <!-- Terms Agreement -->
                        <div class="form-group mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="terms-agreement" required>
                                <label class="form-check-label" for="terms-agreement">
                                    {% trans "I agree to the" %}
                                    <a href="{% url 'core:terms' %}" target="_blank" class="auth-link">{% trans "Terms of Service" %}</a>
                                    {% trans "and" %}
                                    <a href="{% url 'core:privacy' %}" target="_blank" class="auth-link">{% trans "Privacy Policy" %}</a>
                                    <span class="text-danger">*</span>
                                </label>
                            </div>
                            <div class="form-check mt-2">
                                <input class="form-check-input" type="checkbox" id="professional-agreement" required>
                                <label class="form-check-label" for="professional-agreement">
                                    {% trans "I confirm that all information provided is accurate and I am a licensed mental health professional" %}
                                    <span class="text-danger">*</span>
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid mb-4">
                            <button type="submit" class="btn btn-success btn-lg auth-submit-btn">
                                <i class="fas fa-user-plus me-2"></i>{% trans "Submit Application" %}
                            </button>
                        </div>
                    </form>

                    <!-- Login Link -->
                    <div class="auth-footer text-center">
                        <p class="mb-0">
                            {% trans "Already have an account?" %} 
                            <a href="{% url 'accounts:login' %}" class="auth-link">{% trans "Sign In" %}</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
