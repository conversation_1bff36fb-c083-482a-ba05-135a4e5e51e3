{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Edit Profile" %} - ECPI{% endblock %}

{% block extra_css %}
<link href="{% static 'css/dashboard.css' %}" rel="stylesheet">
<style>
.edit-profile-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.form-card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 15px;
    margin-bottom: 1.5rem;
}

.form-section {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.avatar-upload {
    position: relative;
    display: inline-block;
}

.avatar-preview {
    width: 120px;
    height: 120px;
    border: 3px solid #dee2e6;
    border-radius: 50%;
    object-fit: cover;
}

.avatar-upload-btn {
    position: absolute;
    bottom: 0;
    right: 0;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.avatar-upload-btn:hover {
    background: #0056b3;
}

.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-save {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    color: white;
    font-weight: 500;
    padding: 0.75rem 2rem;
    border-radius: 25px;
}

.btn-save:hover {
    background: linear-gradient(45deg, #218838, #1ea085);
    color: white;
    transform: translateY(-1px);
}

.privacy-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    margin-bottom: 1rem;
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.help-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="edit-profile-header">
        <div class="d-flex align-items-center justify-content-between">
            <div>
                <h2 class="mb-1">
                    <i class="fas fa-user-edit me-2"></i>{% trans "Edit Profile" %}
                </h2>
                <p class="mb-0 opacity-75">{% trans "Update your personal information and preferences" %}</p>
            </div>
            <div class="text-end">
                <a href="{% url 'accounts:profile' %}" class="btn btn-light">
                    <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Profile" %}
                </a>
            </div>
        </div>
    </div>

    <form method="post" enctype="multipart/form-data" id="profile-form">
        {% csrf_token %}
        
        <div class="row">
            <!-- Left Column - Basic Information -->
            <div class="col-lg-8">
                <!-- Avatar and Basic Info -->
                <div class="form-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>{% trans "Basic Information" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 text-center mb-3">
                                <div class="avatar-upload">
                                    <img src="{% if user.profile.avatar %}{{ user.profile.avatar.url }}{% else %}{% static 'images/default-avatar.png' %}{% endif %}" 
                                         alt="Profile Picture" class="avatar-preview" id="avatar-preview">
                                    <label for="id_avatar" class="avatar-upload-btn">
                                        <i class="fas fa-camera"></i>
                                    </label>
                                    <input type="file" name="avatar" id="id_avatar" class="d-none" accept="image/*">
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">{% trans "Click camera icon to change" %}</small>
                                </div>
                            </div>
                            <div class="col-md-9">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="id_first_name" class="form-label">{% trans "First Name" %}</label>
                                        <input type="text" name="first_name" id="id_first_name" 
                                               class="form-control" value="{{ user.first_name }}" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="id_last_name" class="form-label">{% trans "Last Name" %}</label>
                                        <input type="text" name="last_name" id="id_last_name" 
                                               class="form-control" value="{{ user.last_name }}" required>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="id_email" class="form-label">{% trans "Email Address" %}</label>
                                        <input type="email" name="email" id="id_email" 
                                               class="form-control" value="{{ user.email }}" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="id_phone" class="form-label">{% trans "Phone Number" %}</label>
                                        <input type="tel" name="phone" id="id_phone" 
                                               class="form-control" value="{{ user.profile.phone }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="id_bio" class="form-label">{% trans "Bio" %}</label>
                            <textarea name="bio" id="id_bio" class="form-control" rows="4" 
                                      placeholder="{% trans 'Tell us about yourself...' %}">{{ user.profile.bio }}</textarea>
                            <div class="help-text">{% trans "Brief description about yourself (max 500 characters)" %}</div>
                        </div>
                    </div>
                </div>

                <!-- Personal Details -->
                <div class="form-card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-id-card me-2"></i>{% trans "Personal Details" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="id_birth_date" class="form-label">{% trans "Date of Birth" %}</label>
                                <input type="date" name="birth_date" id="id_birth_date" 
                                       class="form-control" value="{{ user.profile.birth_date|date:'Y-m-d' }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="id_gender" class="form-label">{% trans "Gender" %}</label>
                                <select name="gender" id="id_gender" class="form-select">
                                    <option value="">{% trans "Select gender..." %}</option>
                                    <option value="M" {% if user.profile.gender == 'M' %}selected{% endif %}>{% trans "Male" %}</option>
                                    <option value="F" {% if user.profile.gender == 'F' %}selected{% endif %}>{% trans "Female" %}</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="id_address" class="form-label">{% trans "Address" %}</label>
                            <textarea name="address" id="id_address" class="form-control" rows="2" 
                                      placeholder="{% trans 'Your full address...' %}">{{ user.profile.address }}</textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="id_city" class="form-label">{% trans "City" %}</label>
                                <input type="text" name="city" id="id_city" 
                                       class="form-control" value="{{ user.profile.city }}">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="id_country" class="form-label">{% trans "Country" %}</label>
                                <input type="text" name="country" id="id_country" 
                                       class="form-control" value="{{ user.profile.country }}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Preferences -->
                <div class="form-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>{% trans "Preferences" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="id_preferred_language" class="form-label">{% trans "Preferred Language" %}</label>
                            <select name="preferred_language" id="id_preferred_language" class="form-select">
                                <option value="en" {% if user.profile.preferred_language == 'en' %}selected{% endif %}>English</option>
                                <option value="am" {% if user.profile.preferred_language == 'am' %}selected{% endif %}>አማርኛ (Amharic)</option>
                            </select>
                            <div class="help-text">{% trans "Choose your preferred language for the interface" %}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column - Privacy & Actions -->
            <div class="col-lg-4">
                <!-- Privacy Settings -->
                <div class="form-section">
                    <h5 class="mb-3">
                        <i class="fas fa-shield-alt me-2"></i>{% trans "Privacy Settings" %}
                    </h5>
                    
                    <div class="privacy-toggle">
                        <div>
                            <div class="fw-bold">{% trans "Show Email Publicly" %}</div>
                            <small class="text-muted">{% trans "Allow others to see your email" %}</small>
                        </div>
                        <div class="form-check form-switch">
                            <input type="checkbox" name="show_email" id="id_show_email" 
                                   class="form-check-input" {% if user.profile.show_email %}checked{% endif %}>
                        </div>
                    </div>
                    
                    <div class="privacy-toggle">
                        <div>
                            <div class="fw-bold">{% trans "Show Phone Publicly" %}</div>
                            <small class="text-muted">{% trans "Allow others to see your phone" %}</small>
                        </div>
                        <div class="form-check form-switch">
                            <input type="checkbox" name="show_phone" id="id_show_phone" 
                                   class="form-check-input" {% if user.profile.show_phone %}checked{% endif %}>
                        </div>
                    </div>
                    
                    <div class="privacy-toggle">
                        <div>
                            <div class="fw-bold">{% trans "Allow Messages" %}</div>
                            <small class="text-muted">{% trans "Allow users to message you" %}</small>
                        </div>
                        <div class="form-check form-switch">
                            <input type="checkbox" name="allow_messages" id="id_allow_messages" 
                                   class="form-check-input" {% if user.profile.allow_messages %}checked{% endif %}>
                        </div>
                    </div>
                </div>

                <!-- Account Verification -->
                <div class="form-section">
                    <h5 class="mb-3">
                        <i class="fas fa-certificate me-2"></i>{% trans "Account Verification" %}
                    </h5>
                    
                    <div class="mb-3">
                        <label for="id_id_certificate" class="form-label">{% trans "ID Certificate" %}</label>
                        <input type="file" name="id_certificate" id="id_id_certificate" 
                               class="form-control" accept=".pdf,.jpg,.jpeg,.png">
                        <div class="help-text">{% trans "Upload ID card or birth certificate for verification" %}</div>
                        {% if user.profile.id_certificate %}
                            <div class="mt-2">
                                <small class="text-success">
                                    <i class="fas fa-check me-1"></i>{% trans "Document uploaded" %}
                                </small>
                            </div>
                        {% endif %}
                    </div>
                    
                    {% if user.profile.is_verified %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>{% trans "Your account is verified" %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>{% trans "Upload documents to verify your account" %}
                        </div>
                    {% endif %}
                </div>

                <!-- Save Actions -->
                <div class="form-section">
                    <h5 class="mb-3">
                        <i class="fas fa-save me-2"></i>{% trans "Save Changes" %}
                    </h5>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-save">
                            <i class="fas fa-save me-2"></i>{% trans "Save Profile" %}
                        </button>
                        <a href="{% url 'accounts:profile' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>{% trans "Cancel" %}
                        </a>
                        <a href="{% url 'accounts:change_password' %}" class="btn btn-outline-warning">
                            <i class="fas fa-key me-2"></i>{% trans "Change Password" %}
                        </a>
                    </div>
                </div>

                <!-- Help -->
                <div class="alert alert-info">
                    <h6 class="alert-heading">
                        <i class="fas fa-lightbulb me-2"></i>{% trans "Tips" %}
                    </h6>
                    <small>
                        <ul class="mb-0 ps-3">
                            <li>{% trans "Keep your profile updated for better experience" %}</li>
                            <li>{% trans "Verify your account for additional features" %}</li>
                            <li>{% trans "Review privacy settings regularly" %}</li>
                        </ul>
                    </small>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Avatar preview functionality
    const avatarInput = document.getElementById('id_avatar');
    const avatarPreview = document.getElementById('avatar-preview');
    
    if (avatarInput && avatarPreview) {
        avatarInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    avatarPreview.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
    }
    
    // Form validation
    const form = document.getElementById('profile-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const firstName = document.getElementById('id_first_name').value.trim();
            const lastName = document.getElementById('id_last_name').value.trim();
            const email = document.getElementById('id_email').value.trim();
            
            if (!firstName || !lastName || !email) {
                e.preventDefault();
                alert('{% trans "Please fill in all required fields." %}');
                return false;
            }
            
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>{% trans "Saving..." %}';
                submitBtn.disabled = true;
            }
        });
    }
    
    // Character counter for bio
    const bioField = document.getElementById('id_bio');
    if (bioField) {
        const maxLength = 500;
        const counter = document.createElement('div');
        counter.className = 'text-muted small mt-1';
        bioField.parentNode.appendChild(counter);
        
        function updateCounter() {
            const remaining = maxLength - bioField.value.length;
            counter.textContent = `${remaining} characters remaining`;
            counter.className = remaining < 50 ? 'text-warning small mt-1' : 'text-muted small mt-1';
        }
        
        bioField.addEventListener('input', updateCounter);
        updateCounter();
    }
});
</script>
{% endblock %}
