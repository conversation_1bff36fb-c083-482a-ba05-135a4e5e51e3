{% extends "admin/change_form.html" %}
{% load i18n %}

{% block submit_buttons_bottom %}
    {{ block.super }}
    
    {% if original %}
        <div class="verification-actions" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
            <h3 style="margin-bottom: 15px; color: #495057;">
                <i class="fas fa-shield-check"></i> {% trans "Verification Actions" %}
            </h3>
            
            {% if original.user.profile.is_verified %}
                <div class="alert alert-success" style="margin-bottom: 15px;">
                    <strong>✓ {% trans "This psychologist is currently VERIFIED" %}</strong>
                </div>
                
                <form method="post" style="display: inline-block; margin-right: 10px;">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="unverify">
                    <button type="submit" class="btn btn-warning" 
                            onclick="return confirm('{% trans "Are you sure you want to remove verification from this psychologist?" %}')"
                            style="background: #ffc107; border: none; color: #212529; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                        <i class="fas fa-times-circle"></i> {% trans "Remove Verification" %}
                    </button>
                </form>
            {% else %}
                <div class="alert alert-warning" style="margin-bottom: 15px;">
                    <strong>⚠ {% trans "This psychologist is NOT VERIFIED" %}</strong>
                </div>
                
                <form method="post" style="display: inline-block; margin-right: 10px;">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="verify">
                    <button type="submit" class="btn btn-success"
                            onclick="return confirm('{% trans "Are you sure you want to verify this psychologist? This confirms their identity and credentials." %}')"
                            style="background: #28a745; border: none; color: white; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                        <i class="fas fa-check-circle"></i> {% trans "Verify Psychologist" %}
                    </button>
                </form>
            {% endif %}
            
            <div style="margin-top: 15px; padding: 10px; background: #e9ecef; border-radius: 4px;">
                <h4 style="margin-bottom: 10px; color: #495057; font-size: 14px;">
                    <i class="fas fa-info-circle"></i> {% trans "Verification Guidelines" %}
                </h4>
                <ul style="margin: 0; padding-left: 20px; font-size: 13px; color: #6c757d;">
                    <li>{% trans "Verify identity documents (ID, passport)" %}</li>
                    <li>{% trans "Confirm educational credentials" %}</li>
                    <li>{% trans "Validate professional license" %}</li>
                    <li>{% trans "Check work experience claims" %}</li>
                    <li>{% trans "Review uploaded certificates" %}</li>
                </ul>
            </div>
        </div>
    {% endif %}
{% endblock %}

{% block extrahead %}
    {{ block.super }}
    <style>
        .verification-actions {
            border: 2px solid #dee2e6;
        }
        
        .verification-actions h3 {
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 10px;
        }
        
        .alert {
            padding: 10px 15px;
            border-radius: 4px;
            border: 1px solid transparent;
        }
        
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        
        .alert-warning {
            color: #856404;
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        
        .btn {
            display: inline-block;
            font-weight: 400;
            text-align: center;
            vertical-align: middle;
            cursor: pointer;
            border: 1px solid transparent;
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            line-height: 1.5;
            border-radius: 0.25rem;
            transition: all 0.15s ease-in-out;
            text-decoration: none;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .btn-success {
            background-color: #28a745;
            border-color: #28a745;
            color: #fff;
        }
        
        .btn-success:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }
        
        .btn-warning {
            background-color: #ffc107;
            border-color: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background-color: #e0a800;
            border-color: #d39e00;
        }
    </style>
{% endblock %}
