{% extends 'admin/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Consultation Overview" %} - ECPI Admin{% endblock %}

{% block extra_css %}
<style>
.stats-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stats-label {
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.chart-container {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
}

.recent-consultations {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.consultation-item {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.consultation-item:hover {
    background: #f8f9fa;
}

.consultation-item:last-child {
    border-bottom: none;
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-confirmed {
    background: #d1ecf1;
    color: #0c5460;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">
                <i class="fas fa-chart-line me-2"></i>{% trans "Consultation Overview" %}
            </h1>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="stats-card text-center">
                <div class="stats-number text-primary">{{ total_consultations }}</div>
                <div class="stats-label">{% trans "Total Consultations" %}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card text-center">
                <div class="stats-number text-success">{{ completed_consultations }}</div>
                <div class="stats-label">{% trans "Completed" %}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card text-center">
                <div class="stats-number text-warning">{{ pending_consultations }}</div>
                <div class="stats-label">{% trans "Pending" %}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="stats-card text-center">
                <div class="stats-number text-info">{{ active_psychologists }}</div>
                <div class="stats-label">{% trans "Active Psychologists" %}</div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row">
        <div class="col-lg-8">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-chart-bar me-2"></i>{% trans "Consultations This Month" %}
                </h5>
                <canvas id="consultationsChart" height="100"></canvas>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-chart-pie me-2"></i>{% trans "Status Distribution" %}
                </h5>
                <canvas id="statusChart" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- Recent Consultations -->
    <div class="row">
        <div class="col-12">
            <div class="recent-consultations">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>{% trans "Recent Consultations" %}
                    </h5>
                    <a href="{% url 'admin:consultation_consultation_changelist' %}" class="btn btn-outline-primary btn-sm">
                        {% trans "View All" %}
                    </a>
                </div>
                
                {% for consultation in recent_consultations %}
                <div class="consultation-item">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <strong>{{ consultation.user.get_full_name|default:consultation.user.username }}</strong><br>
                            <small class="text-muted">{{ consultation.user.email }}</small>
                        </div>
                        <div class="col-md-3">
                            <strong>{{ consultation.psychologist.user.get_full_name }}</strong><br>
                            <small class="text-muted">{{ consultation.consultation_type.name }}</small>
                        </div>
                        <div class="col-md-2">
                            {{ consultation.scheduled_datetime|date:"M d, Y" }}<br>
                            <small class="text-muted">{{ consultation.scheduled_datetime|date:"g:i A" }}</small>
                        </div>
                        <div class="col-md-2">
                            <span class="status-badge status-{{ consultation.status }}">
                                {{ consultation.get_status_display }}
                            </span>
                        </div>
                        <div class="col-md-2 text-end">
                            <a href="{% url 'admin:consultation_consultation_change' consultation.pk %}" 
                               class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-edit"></i>
                            </a>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="text-center py-4">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">{% trans "No recent consultations" %}</h6>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="stats-card">
                <h5 class="mb-3">
                    <i class="fas fa-tools me-2"></i>{% trans "Quick Actions" %}
                </h5>
                <div class="row">
                    <div class="col-md-3">
                        <a href="{% url 'admin:consultation_consultation_changelist' %}" class="btn btn-outline-primary w-100 mb-2">
                            <i class="fas fa-list me-2"></i>{% trans "Manage Consultations" %}
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{% url 'admin:accounts_psychologistprofile_changelist' %}" class="btn btn-outline-success w-100 mb-2">
                            <i class="fas fa-user-md me-2"></i>{% trans "Manage Psychologists" %}
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{% url 'admin:consultation_consultationtype_changelist' %}" class="btn btn-outline-info w-100 mb-2">
                            <i class="fas fa-tags me-2"></i>{% trans "Consultation Types" %}
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="{% url 'admin:consultation_timeslot_changelist' %}" class="btn btn-outline-warning w-100 mb-2">
                            <i class="fas fa-calendar me-2"></i>{% trans "Time Slots" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Consultations Chart
    const consultationsCtx = document.getElementById('consultationsChart').getContext('2d');
    new Chart(consultationsCtx, {
        type: 'line',
        data: {
            labels: {{ monthly_labels|safe }},
            datasets: [{
                label: '{% trans "Consultations" %}',
                data: {{ monthly_data|safe }},
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });

    // Status Chart
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: ['{% trans "Pending" %}', '{% trans "Confirmed" %}', '{% trans "Completed" %}', '{% trans "Cancelled" %}'],
            datasets: [{
                data: {{ status_data|safe }},
                backgroundColor: [
                    '#ffc107',
                    '#17a2b8',
                    '#28a745',
                    '#dc3545'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
});
</script>
{% endblock %}
