{% extends "admin/base_site.html" %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "ECPI Admin Dashboard" %}{% endblock %}

{% block extrahead %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
.dashboard-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.stat-card {
    text-align: center;
    padding: 20px;
    border-radius: 8px;
    color: white;
    margin-bottom: 20px;
}

.stat-card.primary { background: linear-gradient(45deg, #007bff, #0056b3); }
.stat-card.success { background: linear-gradient(45deg, #28a745, #1e7e34); }
.stat-card.warning { background: linear-gradient(45deg, #ffc107, #e0a800); }
.stat-card.danger { background: linear-gradient(45deg, #dc3545, #c82333); }
.stat-card.info { background: linear-gradient(45deg, #17a2b8, #138496); }

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

.recent-activity {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.activity-item:last-child {
    border-bottom: none;
}

.health-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.health-good { background-color: #28a745; }
.health-warning { background-color: #ffc107; }
.health-danger { background-color: #dc3545; }
</style>
{% endblock %}

{% block content %}
<div class="dashboard">
    <h1>{% trans "ECPI Admin Dashboard" %}</h1>
    
    <!-- Key Statistics -->
    <div class="row">
        <div class="col-md-2">
            <div class="stat-card primary">
                <div class="stat-number">{{ total_users }}</div>
                <div class="stat-label">{% trans "Total Users" %}</div>
                <small>+{{ new_users_this_month }} {% trans "this month" %}</small>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stat-card success">
                <div class="stat-number">{{ approved_psychologists }}</div>
                <div class="stat-label">{% trans "Active Psychologists" %}</div>
                <small>{{ pending_psychologists }} {% trans "pending" %}</small>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stat-card info">
                <div class="stat-number">{{ total_consultations }}</div>
                <div class="stat-label">{% trans "Total Consultations" %}</div>
                <small>+{{ consultations_this_month }} {% trans "this month" %}</small>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stat-card warning">
                <div class="stat-number">{{ total_revenue }}</div>
                <div class="stat-label">{% trans "Total Revenue (ETB)" %}</div>
                <small>{{ revenue_this_month }} {% trans "this month" %}</small>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stat-card success">
                <div class="stat-number">{{ average_rating }}</div>
                <div class="stat-label">{% trans "Avg Rating" %}</div>
                <small>{{ total_ratings }} {% trans "reviews" %}</small>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stat-card {% if pending_reports > 0 %}danger{% else %}success{% endif %}">
                <div class="stat-number">{{ pending_reports }}</div>
                <div class="stat-label">{% trans "Pending Reports" %}</div>
                <small>{% trans "Need attention" %}</small>
            </div>
        </div>
    </div>
    
    <!-- Charts Row -->
    <div class="row">
        <div class="col-md-6">
            <div class="dashboard-card">
                <h3>{% trans "User Growth" %}</h3>
                <div class="chart-container">
                    <canvas id="userGrowthChart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="dashboard-card">
                <h3>{% trans "Consultation Trends" %}</h3>
                <div class="chart-container">
                    <canvas id="consultationChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <!-- System Health & Quick Actions -->
    <div class="row">
        <div class="col-md-4">
            <div class="dashboard-card">
                <h3>{% trans "System Health" %}</h3>
                <div class="health-check">
                    <p>
                        <span class="health-indicator health-good"></span>
                        {% trans "Database Connection" %}
                    </p>
                    <p>
                        <span class="health-indicator {% if system_health.pending_approvals > 0 %}health-warning{% else %}health-good{% endif %}"></span>
                        {{ system_health.pending_approvals }} {% trans "Pending Approvals" %}
                    </p>
                    <p>
                        <span class="health-indicator {% if system_health.unresolved_reports > 0 %}health-danger{% else %}health-good{% endif %}"></span>
                        {{ system_health.unresolved_reports }} {% trans "Unresolved Reports" %}
                    </p>
                    <p>
                        <span class="health-indicator {% if system_health.unread_messages > 0 %}health-warning{% else %}health-good{% endif %}"></span>
                        {{ system_health.unread_messages }} {% trans "Unread Messages" %}
                    </p>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="dashboard-card">
                <h3>{% trans "Quick Actions" %}</h3>
                <div class="quick-actions">
                    <a href="{% url 'admin:accounts_psychologistprofile_changelist' %}?approval_status=pending" class="btn btn-warning btn-block mb-2">
                        {% trans "Review Pending Psychologists" %} ({{ pending_psychologists }})
                    </a>
                    <a href="{% url 'admin:accounts_userreport_changelist' %}?status=pending" class="btn btn-danger btn-block mb-2">
                        {% trans "Handle Reports" %} ({{ pending_reports }})
                    </a>
                    <a href="{% url 'admin:core_contactmessage_changelist' %}?is_read__exact=0" class="btn btn-info btn-block mb-2">
                        {% trans "Read Messages" %} ({{ unread_messages }})
                    </a>
                    <a href="{% url 'admin:consultation_consultation_changelist' %}" class="btn btn-primary btn-block">
                        {% trans "Manage Consultations" %}
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="dashboard-card">
                <h3>{% trans "Top Psychologists" %}</h3>
                <div class="top-psychologists">
                    {% for psychologist in top_psychologists %}
                        <div class="activity-item">
                            <strong>Dr. {{ psychologist.user.get_full_name }}</strong><br>
                            <small>
                                ⭐ {{ psychologist.calculated_avg_rating|floatformat:1|default:psychologist.average_rating|floatformat:1 }}
                                ({{ psychologist.consultation_count|default:psychologist.total_consultations }} {% trans "consultations" %})
                            </small>
                        </div>
                    {% empty %}
                        <p class="text-muted">{% trans "No rated psychologists yet" %}</p>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Activity -->
    <div class="row">
        <div class="col-md-6">
            <div class="dashboard-card">
                <h3>{% trans "Recent Consultations" %}</h3>
                <div class="recent-activity">
                    {% for consultation in recent_consultations %}
                        <div class="activity-item">
                            <strong>{{ consultation.consultation_type.display_name }}</strong><br>
                            <small>
                                {{ consultation.user.get_full_name|default:consultation.user.username }} 
                                → Dr. {{ consultation.psychologist.user.get_full_name }}
                                <br>
                                {{ consultation.created_at|date:"M d, H:i" }} - 
                                <span class="badge badge-{{ consultation.status }}">{{ consultation.get_status_display }}</span>
                            </small>
                        </div>
                    {% empty %}
                        <p class="text-muted">{% trans "No recent consultations" %}</p>
                    {% endfor %}
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="dashboard-card">
                <h3>{% trans "Recent Payments" %}</h3>
                <div class="recent-activity">
                    {% for payment in recent_payments %}
                        <div class="activity-item">
                            <strong>{{ payment.amount }} {{ payment.currency }}</strong><br>
                            <small>
                                {{ payment.user.get_full_name|default:payment.user.username }}
                                <br>
                                {{ payment.created_at|date:"M d, H:i" }} - 
                                <span class="badge badge-{{ payment.status }}">{{ payment.get_status_display }}</span>
                            </small>
                        </div>
                    {% empty %}
                        <p class="text-muted">{% trans "No recent payments" %}</p>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// User Growth Chart
const userCtx = document.getElementById('userGrowthChart').getContext('2d');
new Chart(userCtx, {
    type: 'line',
    data: {
        labels: {{ monthly_users|safe }}.map(item => item.month),
        datasets: [{
            label: '{% trans "New Users" %}',
            data: {{ monthly_users|safe }}.map(item => item.count),
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Consultation Chart
const consultationCtx = document.getElementById('consultationChart').getContext('2d');
new Chart(consultationCtx, {
    type: 'bar',
    data: {
        labels: {{ monthly_consultations|safe }}.map(item => item.month),
        datasets: [{
            label: '{% trans "Consultations" %}',
            data: {{ monthly_consultations|safe }}.map(item => item.count),
            backgroundColor: 'rgba(40, 167, 69, 0.8)',
            borderColor: '#28a745',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
</script>
{% endblock %}
