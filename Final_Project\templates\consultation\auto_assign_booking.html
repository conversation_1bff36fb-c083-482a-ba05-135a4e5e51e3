{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Book Consultation - Auto Assign" %} - ECPI{% endblock %}

{% block extra_css %}
<link href="{% static 'css/consultation.css' %}" rel="stylesheet">
<link href="{% static 'css/booking.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h3 class="mb-0">
                        <i class="fas fa-magic me-2"></i>{% trans "Auto-Assign Consultation" %}
                    </h3>
                </div>
                <div class="card-body p-4">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>{% trans "How Auto-Assignment Works" %}</h6>
                        <p class="mb-0">{% trans "We'll automatically match you with the best available psychologist based on your consultation type and their specialties. This ensures you get the most appropriate care for your needs." %}</p>
                    </div>

                    <!-- Display form errors -->
                    {% if form.errors %}
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>{% trans "Please fix the following errors:" %}</h6>
                            <ul class="mb-0">
                                {% for field, errors in form.errors.items %}
                                    {% for error in errors %}
                                        <li><strong>{{ field }}:</strong> {{ error }}</li>
                                    {% endfor %}
                                {% endfor %}
                            </ul>
                        </div>
                    {% endif %}

                    <form method="post" id="auto-booking-form" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <!-- Consultation Type Selection -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">{% trans "Select Consultation Type" %}</h5>
                                <button type="button" class="btn btn-outline-info btn-sm" data-bs-toggle="modal" data-bs-target="#consultationHelpModal">
                                    <i class="fas fa-question-circle me-1"></i>{% trans "Need help choosing?" %}
                                </button>
                            </div>
                            <div class="consultation-types">
                                {% for choice in form.consultation_type %}
                                    <div class="consultation-type-option mb-3">
                                        <div class="card consultation-type-card">
                                            <div class="card-body">
                                                <div class="form-check">
                                                    {{ choice.tag }}
                                                    <label class="form-check-label w-100" for="{{ choice.id_for_label }}">
                                                        <div class="d-flex justify-content-between align-items-start">
                                                            <div>
                                                                <h6 class="mb-1">{{ choice.choice_label }}</h6>
                                                                <p class="text-muted small mb-0">
                                                                    {% if choice.choice_value.is_free %}
                                                                        {% trans "Free consultation - once per user" %}
                                                                    {% else %}
                                                                        {% trans "Paid consultation" %} - {{ choice.choice_value.default_price }} ETB
                                                                    {% endif %}
                                                                </p>
                                                            </div>
                                                            <div class="text-end">
                                                                {% if choice.choice_value.is_free %}
                                                                    <span class="badge bg-success">{% trans "Free" %}</span>
                                                                {% else %}
                                                                    <span class="badge bg-primary">{{ choice.choice_value.default_price }} ETB</span>
                                                                {% endif %}
                                                                <div class="text-muted small">{{ choice.choice_value.default_duration }} {% trans "min" %}</div>
                                                            </div>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                        
                        <!-- Date Selection -->
                        <div class="mb-4">
                            <h5 class="mb-3">{% trans "Preferred Date" %}</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    {{ form.preferred_date.label_tag }}
                                    {{ form.preferred_date }}
                                    <div class="form-text">{% trans "We'll find the best available time slot for your chosen date" %}</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- User Notes -->
                        <div class="mb-4">
                            <h5 class="mb-3">{% trans "Tell us about your concerns" %}</h5>
                            {{ form.user_notes.label_tag }}
                            {{ form.user_notes }}
                            <div class="form-text">{% trans "This helps us match you with the most suitable psychologist" %}</div>
                        </div>
                        
                        <!-- Auto-Assignment Info -->
                        <div class="alert alert-light border">
                            <h6 class="text-primary"><i class="fas fa-robot me-2"></i>{% trans "What happens next?" %}</h6>
                            <ol class="mb-0">
                                <li>{% trans "We'll analyze your consultation type and needs" %}</li>
                                <li>{% trans "Our system will match you with the best available psychologist" %}</li>
                                <li>{% trans "You'll be automatically assigned a suitable time slot" %}</li>
                                <li>{% trans "You'll receive confirmation with your psychologist's details" %}</li>
                            </ol>
                        </div>
                        
                        <!-- Submit Button -->
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-info btn-lg" id="submit-booking">
                                <i class="fas fa-magic me-2"></i>
                                {% trans "Find My Perfect Match" %}
                            </button>
                            <a href="{% url 'consultation:psychologist_list' %}" class="btn btn-outline-secondary">
                                {% trans "Browse Psychologists Instead" %}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include the same help modal from the regular booking form -->
<div class="modal fade" id="consultationHelpModal" tabindex="-1" aria-labelledby="consultationHelpModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="consultationHelpModalLabel">
                    <i class="fas fa-question-circle me-2"></i>{% trans "Consultation Types Guide" %}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <h6 class="text-primary mb-3">{% trans "Choose the consultation type that best matches your needs:" %}</h6>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h6 class="card-title text-success">
                                            <i class="fas fa-search me-1"></i>{% trans "Mental Health Screening" %}
                                            <span class="badge bg-success ms-1">{% trans "FREE" %}</span>
                                        </h6>
                                        <p class="card-text small">{% trans "Perfect for first-time users or general mental health assessment" %}</p>
                                        <small class="text-muted">30 min • 2 free sessions per user</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h6 class="card-title text-primary">
                                            <i class="fas fa-comments me-1"></i>{% trans "General Counseling" %}
                                            <span class="badge bg-primary ms-1">400 ETB</span>
                                        </h6>
                                        <p class="card-text small">{% trans "For emotional support, stress management, and relationship issues" %}</p>
                                        <small class="text-muted">45 min • 400 ETB</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h6 class="card-title text-primary">
                                            <i class="fas fa-exclamation-triangle me-1"></i>{% trans "Crisis Intervention" %}
                                            <span class="badge bg-primary ms-1">600 ETB</span>
                                        </h6>
                                        <p class="card-text small">{% trans "For urgent situations requiring immediate support" %}</p>
                                        <small class="text-muted">60 min • 600 ETB</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h6 class="card-title text-success">
                                            <i class="fas fa-question me-1"></i>{% trans "Other / Not Sure" %}
                                            <span class="badge bg-success ms-1">{% trans "FREE" %}</span>
                                        </h6>
                                        <p class="card-text small">{% trans "When you're not sure what type of help you need" %}</p>
                                        <small class="text-muted">30 min • 1 free session per user</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle consultation type selection
    document.querySelectorAll('input[name="consultation_type"]').forEach(radio => {
        radio.addEventListener('change', function() {
            // Highlight selected card
            document.querySelectorAll('.consultation-type-card').forEach(card => {
                card.classList.remove('border-primary', 'bg-light');
            });
            this.closest('.consultation-type-card').classList.add('border-primary', 'bg-light');
        });
    });
});
</script>
{% endblock %}
