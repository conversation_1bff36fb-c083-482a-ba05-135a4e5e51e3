{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Book Consultation" %} - ECPI{% endblock %}

{% block extra_css %}
<link href="{% static 'css/consultation.css' %}" rel="stylesheet">
<link href="{% static 'css/booking.css' %}" rel="stylesheet">
<style>
/* Weekly Availability Schedule Styles */
.availability-schedule {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 0.75rem;
}

.availability-day {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.availability-day:last-child {
    border-bottom: none;
}

.day-name {
    font-size: 0.875rem;
    min-width: 60px;
}

.time-info {
    font-size: 0.8rem;
}

/* Weekly Calendar Widget Styles */
.weekly-calendar-widget {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 1rem;
    border: 1px solid #dee2e6;
}

.day-card {
    background: white;
    border-radius: 8px;
    padding: 0.75rem 0.5rem;
    text-align: center;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    min-height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.day-card.available {
    border-color: #28a745;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.day-card.unavailable {
    border-color: #dc3545;
    background: linear-gradient(135deg, #f8d7da 0%, #f1b0b7 100%);
    opacity: 0.7;
}

.day-header {
    margin-bottom: 0.5rem;
}

.day-name {
    font-weight: bold;
    font-size: 0.875rem;
    color: #495057;
}

.day-card.available .day-name {
    color: #155724;
}

.day-card.unavailable .day-name {
    color: #721c24;
}

.day-body {
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.time-range {
    font-size: 0.75rem;
    line-height: 1.2;
    color: #155724;
    font-weight: 500;
}

.unavailable-text {
    font-size: 0.7rem;
    color: #721c24;
    font-weight: 500;
}

/* Quick date selection buttons */
.quick-slots {
    margin-bottom: 0.5rem;
}

.quick-date-btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
}

.quick-date-btn:hover {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.upcoming-slots-preview {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 0.75rem;
    border: 1px solid #e9ecef;
}

/* Direct Time Slot Selection Styles */
.time-slot-card {
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid #dee2e6 !important;
    background: white;
}

.time-slot-card:hover {
    border-color: #007bff !important;
    box-shadow: 0 4px 8px rgba(0,123,255,0.15);
    transform: translateY(-2px);
}

.time-slot-card.selected {
    border-color: #007bff !important;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    box-shadow: 0 4px 12px rgba(0,123,255,0.25);
}

.time-slot-card.selected .text-primary {
    color: #0056b3 !important;
}

.time-slot-card.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: #f8f9fa;
}

.time-slot-card.disabled:hover {
    transform: none;
    box-shadow: none;
    border-color: #dee2e6 !important;
}

.cursor-pointer {
    cursor: pointer;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .time-slot-card {
        margin-bottom: 1rem;
    }

    .time-slot-card .h5 {
        font-size: 1.1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <!-- Psychologist Info Sidebar -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow-sm sticky-top" style="top: 80px; z-index: 1020;">
                <div class="card-body">
                    <!-- Psychologist Header -->
                    <div class="text-center mb-4">
                        <img src="{% if psychologist.user.profile.avatar %}{{ psychologist.user.profile.avatar.url }}{% else %}{% static 'images/default-avatar.png' %}{% endif %}" 
                             alt="{{ psychologist.user.get_full_name }}" 
                             class="rounded-circle mb-3" width="100" height="100">
                        <h4 class="mb-1">Dr. {{ psychologist.user.get_full_name|default:psychologist.user.username }}</h4>
                        <div class="rating mb-2">
                            {% for i in "12345" %}
                                <i class="fas fa-star {% if psychologist.average_rating >= i|add:0 %}text-warning{% else %}text-muted{% endif %}"></i>
                            {% endfor %}
                            <span class="text-muted ms-1">({{ psychologist.total_ratings }})</span>
                        </div>
                        <p class="text-muted">{{ psychologist.years_of_experience }} {% trans "years experience" %}</p>
                    </div>
                    
                    <!-- Specialties -->
                    <div class="mb-4">
                        <h6 class="fw-bold mb-2">{% trans "Specialties" %}</h6>
                        {% for specialty in psychologist.specialties.all %}
                            <span class="badge bg-light text-dark me-1 mb-1">{{ specialty.name }}</span>
                        {% endfor %}
                    </div>
                    
                    <!-- Languages -->
                    <div class="mb-4">
                        <h6 class="fw-bold mb-2">{% trans "Languages" %}</h6>
                        <p class="text-muted">
                            {% for lang in psychologist.language_list %}
                                {% if lang == 'en' %}English{% elif lang == 'am' %}አማርኛ{% else %}{{ lang }}{% endif %}{% if not forloop.last %}, {% endif %}
                            {% endfor %}
                        </p>
                    </div>
                    


                    <!-- Pricing -->
                    <div class="mb-4">
                        <h6 class="fw-bold mb-2">{% trans "Pricing" %}</h6>
                        {% if psychologist.offers_free_consultation %}
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>{% trans "Free Consultation" %}</span>
                                <span class="badge bg-success">{% trans "Available" %}</span>
                            </div>
                        {% endif %}
                        {% if psychologist.consultation_fee > 0 %}
                            <div class="d-flex justify-content-between align-items-center">
                                <span>{% trans "Paid Consultation" %}</span>
                                <span class="fw-bold text-primary">{{ psychologist.consultation_fee }} ETB</span>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Booking Form -->
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">
                        <i class="fas fa-calendar-plus me-2"></i>{% trans "Book Your Consultation" %}
                    </h3>
                </div>
                <div class="card-body p-4">
                    <!-- Display form errors -->
                    {% if form.errors %}
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>{% trans "Please fix the following errors:" %}</h6>
                            <ul class="mb-0">
                                {% for field, errors in form.errors.items %}
                                    {% for error in errors %}
                                        <li><strong>{{ field }}:</strong> {{ error }}</li>
                                    {% endfor %}
                                {% endfor %}
                            </ul>
                        </div>
                    {% endif %}

                    <form method="post" id="booking-form" class="needs-validation" novalidate>
                        {% csrf_token %}
                        {{ form.time_slot }}
                        
                        <!-- Consultation Type Selection -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5 class="mb-0">{% trans "Select Consultation Type" %}</h5>
                                <button type="button" class="btn btn-outline-info btn-sm" data-bs-toggle="modal" data-bs-target="#consultationHelpModal">
                                    <i class="fas fa-question-circle me-1"></i>{% trans "Don't know where to start?" %}
                                </button>
                            </div>
                            <div class="consultation-types">
                                {% for choice in form.consultation_type %}
                                    <div class="consultation-type-option mb-3">
                                        <div class="card consultation-type-card">
                                            <div class="card-body">
                                                <div class="form-check">
                                                    {{ choice.tag }}
                                                    <label class="form-check-label w-100" for="{{ choice.id_for_label }}">
                                                        <div class="d-flex justify-content-between align-items-start">
                                                            <div>
                                                                <h6 class="mb-1">{{ choice.choice_label }}</h6>
                                                                <p class="text-muted small mb-0">
                                                                    {% if choice.choice_value.is_free %}
                                                                        {% trans "Free consultation - once per user" %}
                                                                    {% else %}
                                                                        {% trans "Paid consultation" %} - {{ choice.choice_value.default_price }} ETB
                                                                    {% endif %}
                                                                </p>
                                                            </div>
                                                            <div class="text-end">
                                                                {% if choice.choice_value.is_free %}
                                                                    <span class="badge bg-success">{% trans "Free" %}</span>
                                                                {% else %}
                                                                    <span class="badge bg-primary">{{ choice.choice_value.default_price }} ETB</span>
                                                                {% endif %}
                                                                <div class="text-muted small">{{ choice.choice_value.default_duration }} {% trans "min" %}</div>
                                                            </div>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                            {% if form.consultation_type.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.consultation_type.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        


                        <!-- Available Time Slots -->
                        <div class="mb-4">
                            <h5 class="mb-3">{% trans "Select Available Time Slot" %}</h5>
                            {% if available_time_slots %}
                                <div class="row g-3">
                                    {% for slot in available_time_slots %}
                                        <div class="col-md-6 col-lg-4">
                                            <div class="time-slot-card border rounded p-3 h-100 cursor-pointer" data-slot-id="{{ slot.pk }}">
                                                <input type="radio" name="time_slot_selection" value="{{ slot.pk }}" class="d-none" id="slot_{{ slot.pk }}">
                                                <div class="text-center">
                                                    <div class="fw-bold text-primary mb-2">
                                                        {{ slot.date|date:"l, F d, Y" }}
                                                    </div>
                                                    <div class="h5 mb-2">
                                                        {{ slot.start_time|time:"g:i A" }} - {{ slot.end_time|time:"g:i A" }}
                                                    </div>
                                                    <div class="small text-muted">
                                                        <i class="fas fa-users me-1"></i>
                                                        {{ slot.current_bookings }}/{{ slot.max_bookings }} {% trans "booked" %}
                                                    </div>
                                                    {% if slot.current_bookings < slot.max_bookings %}
                                                        <div class="mt-2">
                                                            <span class="badge bg-success">{% trans "Available" %}</span>
                                                        </div>
                                                    {% else %}
                                                        <div class="mt-2">
                                                            <span class="badge bg-secondary">{% trans "Full" %}</span>
                                                        </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        {% trans "Click on a time slot to select it for your consultation." %}
                                    </small>
                                </div>
                            {% else %}
                                <div class="text-center py-5">
                                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                    <h6 class="text-muted">{% trans "No Available Time Slots" %}</h6>
                                    <p class="text-muted">
                                        {% trans "This psychologist hasn't set up any available time slots yet." %}<br>
                                        {% trans "Please try again later or contact them directly." %}
                                    </p>
                                    <a href="{% url 'consultation:psychologist_list' %}" class="btn btn-outline-primary">
                                        <i class="fas fa-search me-2"></i>{% trans "Find Another Psychologist" %}
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- User Notes -->
                        <div class="mb-4">
                            <h5 class="mb-3">{% trans "Additional Information" %}</h5>
                            {{ form.user_notes.label_tag }}
                            {{ form.user_notes }}
                            <div class="form-text">{{ form.user_notes.help_text }}</div>
                            {% if form.user_notes.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.user_notes.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Submit Button -->
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg" id="submit-booking" disabled>
                                <i class="fas fa-calendar-check me-2"></i>
                                <span id="submit-text">{% trans "Select consultation type and time" %}</span>
                            </button>
                            <a href="{% url 'consultation:psychologist_detail' psychologist.pk %}" class="btn btn-outline-secondary">
                                {% trans "Back to Profile" %}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Consultation Help Modal -->
<div class="modal fade" id="consultationHelpModal" tabindex="-1" aria-labelledby="consultationHelpModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="consultationHelpModalLabel">
                    <i class="fas fa-question-circle me-2"></i>{% trans "Consultation Types Guide" %}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <h6 class="text-primary mb-3">{% trans "Not sure which consultation type to choose? Here's a guide to help you:" %}</h6>

                        <div class="accordion" id="consultationTypesAccordion">
                            <!-- Mental Health Screening -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="screeningHeading">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#screeningCollapse">
                                        <i class="fas fa-search me-2 text-success"></i>
                                        <strong>{% trans "Mental Health Screening" %}</strong>
                                        <span class="badge bg-success ms-2">{% trans "FREE" %}</span>
                                    </button>
                                </h2>
                                <div id="screeningCollapse" class="accordion-collapse collapse show" data-bs-parent="#consultationTypesAccordion">
                                    <div class="accordion-body">
                                        <p><strong>{% trans "Best for:" %}</strong> {% trans "First-time users or anyone unsure about their mental health needs" %}</p>
                                        <p><strong>{% trans "What you'll get:" %}</strong></p>
                                        <ul>
                                            <li>{% trans "Initial assessment of your mental health" %}</li>
                                            <li>{% trans "Understanding of your concerns and symptoms" %}</li>
                                            <li>{% trans "Recommendations for next steps" %}</li>
                                            <li>{% trans "Referrals to appropriate specialists if needed" %}</li>
                                        </ul>
                                        <p class="text-success"><i class="fas fa-gift me-1"></i>{% trans "This is completely free and available once per user!" %}</p>
                                    </div>
                                </div>
                            </div>

                            <!-- General Counseling -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="counselingHeading">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#counselingCollapse">
                                        <i class="fas fa-comments me-2 text-primary"></i>
                                        <strong>{% trans "General Counseling" %}</strong>
                                        <span class="badge bg-primary ms-2">400 ETB</span>
                                    </button>
                                </h2>
                                <div id="counselingCollapse" class="accordion-collapse collapse" data-bs-parent="#consultationTypesAccordion">
                                    <div class="accordion-body">
                                        <p><strong>{% trans "Best for:" %}</strong> {% trans "Emotional support, stress management, relationship issues" %}</p>
                                        <p><strong>{% trans "What you'll get:" %}</strong></p>
                                        <ul>
                                            <li>{% trans "Emotional support and guidance" %}</li>
                                            <li>{% trans "Coping strategies for daily challenges" %}</li>
                                            <li>{% trans "Safe space to discuss your feelings" %}</li>
                                            <li>{% trans "Basic therapeutic techniques" %}</li>
                                        </ul>
                                        <p class="text-primary"><i class="fas fa-clock me-1"></i>45 {% trans "minutes" %} • <i class="fas fa-money-bill me-1"></i>400 ETB</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Crisis Intervention -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="crisisHeading">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#crisisCollapse">
                                        <i class="fas fa-exclamation-triangle me-2 text-danger"></i>
                                        <strong>{% trans "Crisis Intervention" %}</strong>
                                        <span class="badge bg-primary ms-2">600 ETB</span>
                                    </button>
                                </h2>
                                <div id="crisisCollapse" class="accordion-collapse collapse" data-bs-parent="#consultationTypesAccordion">
                                    <div class="accordion-body">
                                        <p><strong>{% trans "Best for:" %}</strong> {% trans "Urgent mental health situations, suicidal thoughts, severe anxiety or panic" %}</p>
                                        <p><strong>{% trans "What you'll get:" %}</strong></p>
                                        <ul>
                                            <li>{% trans "Immediate professional support" %}</li>
                                            <li>{% trans "Crisis de-escalation techniques" %}</li>
                                            <li>{% trans "Safety planning" %}</li>
                                            <li>{% trans "Emergency referrals if needed" %}</li>
                                        </ul>
                                        <div class="alert alert-warning">
                                            <i class="fas fa-phone me-1"></i>{% trans "Available 24/7 for urgent situations" %}
                                        </div>
                                        <p class="text-primary"><i class="fas fa-clock me-1"></i>60 {% trans "minutes" %} • <i class="fas fa-money-bill me-1"></i>600 ETB</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Medication Consultation -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="medicationHeading">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#medicationCollapse">
                                        <i class="fas fa-pills me-2 text-primary"></i>
                                        <strong>{% trans "Medication Consultation" %}</strong>
                                        <span class="badge bg-primary ms-2">500 ETB</span>
                                    </button>
                                </h2>
                                <div id="medicationCollapse" class="accordion-collapse collapse" data-bs-parent="#consultationTypesAccordion">
                                    <div class="accordion-body">
                                        <p><strong>{% trans "Best for:" %}</strong> {% trans "Questions about psychiatric medications, side effects, and treatment options" %}</p>
                                        <p><strong>{% trans "What you'll get:" %}</strong></p>
                                        <ul>
                                            <li>{% trans "Professional medication advice" %}</li>
                                            <li>{% trans "Side effects discussion" %}</li>
                                            <li>{% trans "Treatment options review" %}</li>
                                            <li>{% trans "Medication management guidance" %}</li>
                                        </ul>
                                        <p class="text-primary"><i class="fas fa-clock me-1"></i>30 {% trans "minutes" %} • <i class="fas fa-money-bill me-1"></i>500 ETB</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Facility Referral -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="facilityHeading">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#facilityCollapse">
                                        <i class="fas fa-hospital me-2 text-primary"></i>
                                        <strong>{% trans "Facility Referral" %}</strong>
                                        <span class="badge bg-primary ms-2">300 ETB</span>
                                    </button>
                                </h2>
                                <div id="facilityCollapse" class="accordion-collapse collapse" data-bs-parent="#consultationTypesAccordion">
                                    <div class="accordion-body">
                                        <p><strong>{% trans "Best for:" %}</strong> {% trans "Need recommendations for specialized mental health facilities" %}</p>
                                        <p><strong>{% trans "What you'll get:" %}</strong></p>
                                        <ul>
                                            <li>{% trans "Specialized facility recommendations" %}</li>
                                            <li>{% trans "Treatment center referrals" %}</li>
                                            <li>{% trans "Facility comparison and guidance" %}</li>
                                            <li>{% trans "Referral letter if needed" %}</li>
                                        </ul>
                                        <p class="text-primary"><i class="fas fa-clock me-1"></i>20 {% trans "minutes" %} • <i class="fas fa-money-bill me-1"></i>300 ETB</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Other/Not Sure -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="otherHeading">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#otherCollapse">
                                        <i class="fas fa-question me-2 text-info"></i>
                                        <strong>{% trans "Other / Not Sure" %}</strong>
                                        <span class="badge bg-success ms-2">{% trans "FREE" %}</span>
                                    </button>
                                </h2>
                                <div id="otherCollapse" class="accordion-collapse collapse" data-bs-parent="#consultationTypesAccordion">
                                    <div class="accordion-body">
                                        <p><strong>{% trans "Best for:" %}</strong> {% trans "When you're not sure what type of help you need" %}</p>
                                        <p><strong>{% trans "What you'll get:" %}</strong></p>
                                        <ul>
                                            <li>{% trans "Help identifying your needs" %}</li>
                                            <li>{% trans "Guidance on the right type of consultation" %}</li>
                                            <li>{% trans "Automatic assignment to an appropriate psychologist" %}</li>
                                            <li>{% trans "Personalized recommendations" %}</li>
                                        </ul>
                                        <p class="text-info"><i class="fas fa-lightbulb me-1"></i>{% trans "We'll help you figure out the best path forward!" %}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4 p-3 bg-light rounded">
                            <h6 class="text-primary">{% trans "Still not sure?" %}</h6>
                            <p class="mb-2">{% trans "We recommend starting with a" %} <strong>{% trans "Mental Health Screening" %}</strong> {% trans "or" %} <strong>{% trans "Other / Not Sure" %}</strong> {% trans "option. Our psychologists will help guide you to the right type of care." %}</p>
                            <p class="mb-0 text-muted small">{% trans "Mental Health Screening is free (2 sessions per user) and helps determine the best path forward!" %}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal" onclick="selectRecommendedType()">
                    {% trans "Select Mental Health Screening" %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Booking form JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('booking-form');
    const submitButton = document.getElementById('submit-booking');
    const submitText = document.getElementById('submit-text');
    
    let selectedConsultationType = null;
    let selectedTimeSlot = null;
    
    // Handle consultation type selection
    document.querySelectorAll('input[name="consultation_type"]').forEach(radio => {
        radio.addEventListener('change', function() {
            selectedConsultationType = this.value;
            updateSubmitButton();
            
            // Highlight selected card
            document.querySelectorAll('.consultation-type-card').forEach(card => {
                card.classList.remove('border-primary', 'bg-light');
            });
            this.closest('.consultation-type-card').classList.add('border-primary', 'bg-light');
        });
    });
    
    // Handle direct time slot selection
    document.querySelectorAll('.time-slot-card').forEach(card => {
        card.addEventListener('click', function() {
            // Check if this slot is disabled
            if (this.classList.contains('disabled')) {
                return;
            }

            const radio = this.querySelector('input[type="radio"]');
            if (radio) {
                radio.checked = true;
                selectedTimeSlot = radio.value;

                // Update the hidden form field
                const timeSlotField = document.querySelector('input[name="time_slot"]');
                if (timeSlotField) {
                    timeSlotField.value = selectedTimeSlot;
                }

                // Update visual selection
                document.querySelectorAll('.time-slot-card').forEach(c => {
                    c.classList.remove('selected');
                });
                this.classList.add('selected');

                updateSubmitButton();
            }
        });
    });
    
    function updateSubmitButton() {
        if (selectedConsultationType && selectedTimeSlot) {
            submitButton.disabled = false;
            submitText.textContent = '{% trans "Book Consultation" %}';
        } else {
            submitButton.disabled = true;
            if (!selectedConsultationType) {
                submitText.textContent = '{% trans "Select consultation type" %}';
            } else if (!selectedTimeSlot) {
                submitText.textContent = '{% trans "Select time slot" %}';
            }
        }
    }



    // Function to select recommended consultation type from modal
    window.selectRecommendedType = function() {
        // Find and select the screening consultation type
        const screeningRadio = document.querySelector('input[name="consultation_type"][value*="screening"]');
        if (screeningRadio) {
            screeningRadio.checked = true;
            screeningRadio.dispatchEvent(new Event('change'));
        }
    };

    // Initialize submit button state
    updateSubmitButton();
});
</script>
{% endblock %}
