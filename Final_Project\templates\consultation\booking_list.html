{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Find a Psychologist" %} - ECPI{% endblock %}

{% block extra_css %}
<link href="{% static 'css/consultation.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section bg-primary text-white py-4">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold mb-3">{% trans "Find Your Psychologist" %}</h1>
                <p class="lead">{% trans "Connect with certified mental health professionals who understand your needs" %}</p>
            </div>
            <div class="col-lg-4 text-end">
                {% if not user.is_authenticated %}
                    <a href="{% url 'accounts:psychologist_signup' %}" class="btn btn-outline-light">
                        <i class="fas fa-user-plus me-2"></i>{% trans "Join as Psychologist" %}
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
</section>

<!-- Filters Section -->
<section class="filters-section bg-light py-4">
    <div class="container">
        <form method="GET" class="row g-3 align-items-end">
            <!-- Search -->
            <div class="col-md-3">
                <label class="form-label">{% trans "Search" %}</label>
                <input type="text" name="search" class="form-control" 
                       placeholder="{% trans 'Name or specialty...' %}"
                       value="{{ current_filters.search }}">
            </div>
            
            <!-- Consultation Type -->
            <div class="col-md-2">
                <label class="form-label">{% trans "Consultation Type" %}</label>
                <select name="consultation_type" class="form-select">
                    <option value="">{% trans "All Types" %}</option>
                    {% for type in consultation_types %}
                        <option value="{{ type.name }}" 
                                {% if current_filters.consultation_type == type.name %}selected{% endif %}>
                            {{ type.display_name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <!-- Specialty -->
            <div class="col-md-2">
                <label class="form-label">{% trans "Specialty" %}</label>
                <select name="specialty" class="form-select">
                    <option value="">{% trans "All Specialties" %}</option>
                    {% for specialty in specialties %}
                        <option value="{{ specialty.name }}" 
                                {% if current_filters.specialty == specialty.name %}selected{% endif %}>
                            {{ specialty.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <!-- Price Range -->
            <div class="col-md-2">
                <label class="form-label">{% trans "Price" %}</label>
                <select name="price_range" class="form-select">
                    <option value="">{% trans "All Prices" %}</option>
                    <option value="free" {% if current_filters.price_range == 'free' %}selected{% endif %}>
                        {% trans "Free Consultation" %}
                    </option>
                    <option value="paid" {% if current_filters.price_range == 'paid' %}selected{% endif %}>
                        {% trans "Paid Consultation" %}
                    </option>
                </select>
            </div>
            
            <!-- Filter Button -->
            <div class="col-md-3">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-filter me-1"></i>{% trans "Filter" %}
                </button>
                <a href="{% url 'consultation:psychologist_list' %}" class="btn btn-outline-secondary">
                    {% trans "Clear" %}
                </a>
            </div>
        </form>
    </div>
</section>

<!-- Results Section -->
<section class="results-section py-5">
    <div class="container">
        <div class="row mb-4">
            <div class="col-md-6">
                <h3>{% trans "Available Psychologists" %}</h3>
                <p class="text-muted">
                    {% blocktrans count counter=psychologists|length %}
                        {{ counter }} psychologist found
                    {% plural %}
                        {{ counter }} psychologists found
                    {% endblocktrans %}
                </p>
            </div>
        </div>
        
        {% if psychologists %}
            <div class="row" id="psychologists-grid">
                {% for psychologist in psychologists %}
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card psychologist-card h-100 shadow-sm">
                            <div class="card-body">
                                <!-- Psychologist Header -->
                                <div class="d-flex align-items-center mb-3">
                                    <img src="{% if psychologist.user.profile.avatar %}{{ psychologist.user.profile.avatar.url }}{% else %}{% static 'images/default-avatar.png' %}{% endif %}" 
                                         alt="{{ psychologist.user.get_full_name }}" 
                                         class="rounded-circle me-3" width="60" height="60">
                                    <div>
                                        <h5 class="card-title mb-1">
                                            Dr. {{ psychologist.user.get_full_name|default:psychologist.user.username }}
                                        </h5>
                                        <div class="rating mb-1">
                                            {% for i in "12345" %}
                                                <i class="fas fa-star {% if psychologist.average_rating >= i|add:0 %}text-warning{% else %}text-muted{% endif %}"></i>
                                            {% endfor %}
                                            <span class="text-muted ms-1">
                                                ({{ psychologist.total_ratings }} {% trans "reviews" %})
                                            </span>
                                        </div>
                                        <small class="text-muted">
                                            {{ psychologist.years_of_experience }} {% trans "years experience" %}
                                        </small>
                                    </div>
                                </div>
                                
                                <!-- Specialties -->
                                <div class="mb-3">
                                    <h6 class="text-muted mb-2">{% trans "Specialties" %}</h6>
                                    <div class="specialties">
                                        {% for specialty in psychologist.specialties.all|slice:":3" %}
                                            <span class="badge bg-light text-dark me-1 mb-1">{{ specialty.name }}</span>
                                        {% endfor %}
                                        {% if psychologist.specialties.count > 3 %}
                                            <span class="badge bg-secondary">+{{ psychologist.specialties.count|add:"-3" }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <!-- Languages -->
                                <div class="mb-3">
                                    <small class="text-muted">
                                        <i class="fas fa-language me-1"></i>
                                        {% for lang in psychologist.language_list %}
                                            {% if lang == 'en' %}English{% elif lang == 'am' %}አማርኛ{% else %}{{ lang }}{% endif %}{% if not forloop.last %}, {% endif %}
                                        {% endfor %}
                                    </small>
                                </div>
                                
                                <!-- Pricing -->
                                <div class="mb-3">
                                    {% if psychologist.offers_free_consultation %}
                                        <span class="badge bg-success me-2">{% trans "Free Consultation" %}</span>
                                    {% endif %}
                                    {% if psychologist.consultation_fee > 0 %}
                                        <span class="text-primary fw-bold">
                                            {{ psychologist.consultation_fee }} ETB/{% trans "session" %}
                                        </span>
                                    {% endif %}
                                </div>
                                
                                <!-- Availability Status -->
                                <div class="mb-3">
                                    {% if psychologist.is_available %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-circle me-1"></i>{% trans "Available" %}
                                        </span>
                                    {% else %}
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-circle me-1"></i>{% trans "Busy" %}
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <!-- Card Footer -->
                            <div class="card-footer bg-transparent">
                                <div class="d-grid gap-2">
                                    <a href="{% url 'consultation:psychologist_detail' psychologist.pk %}" 
                                       class="btn btn-outline-primary">
                                        {% trans "View Profile" %}
                                    </a>
                                    {% if user.is_authenticated and psychologist.is_available %}
                                        <a href="{% url 'consultation:book_consultation' psychologist.pk %}" 
                                           class="btn btn-primary">
                                            <i class="fas fa-calendar-plus me-1"></i>{% trans "Book Session" %}
                                        </a>
                                    {% elif not user.is_authenticated %}
                                        <a href="{% url 'accounts:login' %}" class="btn btn-primary">
                                            {% trans "Login to Book" %}
                                        </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
                <nav aria-label="{% trans 'Psychologists pagination' %}">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">
                                    {% trans "Previous" %}
                                </a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">
                                {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">
                                    {% trans "Next" %}
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <!-- No Results -->
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h3>{% trans "No psychologists found" %}</h3>
                <p class="text-muted">{% trans "Try adjusting your filters or search terms." %}</p>
                <a href="{% url 'consultation:psychologist_list' %}" class="btn btn-primary">
                    {% trans "View All Psychologists" %}
                </a>
            </div>
        {% endif %}
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/consultation.js' %}"></script>
{% endblock %}
