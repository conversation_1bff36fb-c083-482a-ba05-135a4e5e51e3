{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}Chat - {{ consultation.psychologist.user.get_full_name }}{% endblock %}

{% block extra_css %}
<style>
.chat-container {
    height: 80vh;
    display: flex;
    flex-direction: column;
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.chat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: between;
    align-items: center;
}

.chat-status {
    background: #f8f9fa;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    text-align: center;
}

.chat-status.active {
    background: #d4edda;
    color: #155724;
}

.chat-status.inactive {
    background: #f8d7da;
    color: #721c24;
}

.chat-status.waiting {
    background: #fff3cd;
    color: #856404;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background: #f8f9fa;
}

.message {
    margin-bottom: 1rem;
    display: flex;
    align-items: flex-start;
}

.message.own {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    margin: 0 0.5rem;
}

.message-content {
    max-width: 70%;
    background: white;
    padding: 0.75rem 1rem;
    border-radius: 15px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.message.own .message-content {
    background: #667eea;
    color: white;
}

.message-meta {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.message.own .message-meta {
    color: rgba(255,255,255,0.8);
}

.chat-input {
    padding: 1rem;
    background: white;
    border-top: 1px solid #e9ecef;
}

.chat-input-group {
    display: flex;
    gap: 0.5rem;
}

.chat-input-field {
    flex: 1;
    border: 1px solid #ddd;
    border-radius: 25px;
    padding: 0.75rem 1rem;
    outline: none;
}

.chat-input-field:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.chat-send-btn {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chat-send-btn:hover {
    background: #5a6fd8;
    transform: scale(1.05);
}

.chat-send-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.system-message {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    margin: 1rem 0;
    padding: 0.5rem;
    background: rgba(108, 117, 125, 0.1);
    border-radius: 10px;
}

.connection-status {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    z-index: 1000;
}

.connection-status.connected {
    background: #d4edda;
    color: #155724;
}

.connection-status.disconnected {
    background: #f8d7da;
    color: #721c24;
}

.typing-indicator {
    display: none;
    padding: 0.5rem 1rem;
    color: #6c757d;
    font-style: italic;
}

.consultation-info {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

@media (max-width: 768px) {
    .chat-container {
        height: 85vh;
    }
    
    .message-content {
        max-width: 85%;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Consultation Info -->
    <div class="consultation-info">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h5 class="mb-1">
                    {% if is_patient %}
                        Consultation with Dr. {{ consultation.psychologist.user.get_full_name }}
                    {% else %}
                        Consultation with {{ consultation.user.get_full_name|default:consultation.user.username }}
                    {% endif %}
                </h5>
                <p class="mb-0 text-muted">
                    {{ consultation.consultation_type.display_name }} - 
                    {{ consultation.scheduled_date|date:"F d, Y" }} at 
                    {{ consultation.scheduled_start_time|time:"g:i A" }}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'consultation:consultation_detail' consultation.pk %}"
                   class="btn btn-primary btn-lg"
                   style="z-index: 1000; position: relative; box-shadow: 0 4px 8px rgba(0,0,0,0.2);"
                   title="Return to consultation details page">
                    <i class="fas fa-arrow-left me-2"></i>Back to Consultation Details
                </a>
            </div>
        </div>
    </div>

    <!-- Chat Container -->
    <div class="chat-container">
        <!-- Chat Header -->
        <div class="chat-header">
            <div>
                <h6 class="mb-0">
                    <i class="fas fa-comments me-2"></i>
                    Secure Chat Session
                </h6>
                <small class="opacity-75">End-to-end encrypted</small>
            </div>
            <div class="text-end">
                <small>{{ time_window.date }}</small><br>
                <small>{{ time_window.formatted_start }} - {{ time_window.formatted_end }}</small>
            </div>
        </div>

        <!-- Chat Status -->
        <div class="chat-status {% if is_chat_active %}active{% elif 'will be available' in chat_status_message %}waiting{% else %}inactive{% endif %}">
            <i class="fas fa-{% if is_chat_active %}check-circle{% elif 'will be available' in chat_status_message %}clock{% else %}times-circle{% endif %} me-2"></i>
            {{ chat_status_message }}
        </div>

        <!-- Chat Messages -->
        <div class="chat-messages" id="chatMessages">
            {% for message in chat_messages %}
                <div class="message {% if message.sender == request.user %}own{% endif %}">
                    <div class="message-avatar">
                        {{ message.sender.first_name|first|default:message.sender.username|first|upper }}
                    </div>
                    <div class="message-content">
                        <div class="message-text">{{ message.message }}</div>
                        <div class="message-meta">
                            {{ message.sender.get_full_name|default:message.sender.username }} • 
                            {{ message.timestamp|date:"g:i A" }}
                        </div>
                    </div>
                </div>
            {% empty %}
                <div class="system-message">
                    <i class="fas fa-info-circle me-2"></i>
                    No messages yet. Start the conversation!
                </div>
            {% endfor %}
        </div>

        <!-- Typing Indicator -->
        <div class="typing-indicator" id="typingIndicator">
            <i class="fas fa-ellipsis-h"></i> Someone is typing...
        </div>

        <!-- Chat Input -->
        <div class="chat-input">
            <div class="chat-input-group">
                <input type="text" 
                       class="chat-input-field" 
                       id="messageInput" 
                       placeholder="{% if is_chat_active %}Type your message...{% else %}Chat is not available right now{% endif %}"
                       {% if not is_chat_active %}disabled{% endif %}>
                <button class="chat-send-btn" 
                        id="sendButton" 
                        {% if not is_chat_active %}disabled{% endif %}>
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
            {% if not is_chat_active %}
                <small class="text-muted mt-2 d-block">
                    <i class="fas fa-lock me-1"></i>
                    Messaging is only available during your scheduled consultation time.
                </small>
            {% endif %}
        </div>
    </div>

    <!-- Connection Status -->
    <div class="connection-status disconnected" id="connectionStatus">
        <i class="fas fa-wifi"></i> Connecting...
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const consultationId = '{{ consultation.pk }}';
    const websocketUrl = '{{ websocket_url }}';
    const isPatient = {{ is_patient|yesno:"true,false" }};
    const isChatActive = {{ is_chat_active|yesno:"true,false" }};
    
    let chatSocket = null;
    let reconnectAttempts = 0;
    const maxReconnectAttempts = 5;
    
    // DOM elements
    const messagesContainer = document.getElementById('chatMessages');
    const messageInput = document.getElementById('messageInput');
    const sendButton = document.getElementById('sendButton');
    const connectionStatus = document.getElementById('connectionStatus');
    const typingIndicator = document.getElementById('typingIndicator');
    
    // Initialize WebSocket connection
    function connectWebSocket() {
        if (chatSocket) {
            chatSocket.close();
        }
        
        chatSocket = new WebSocket(websocketUrl);
        
        chatSocket.onopen = function(e) {
            console.log('Chat WebSocket connected');
            connectionStatus.textContent = 'Connected';
            connectionStatus.className = 'connection-status connected';
            reconnectAttempts = 0;
        };
        
        chatSocket.onmessage = function(e) {
            const data = JSON.parse(e.data);
            handleWebSocketMessage(data);
        };
        
        chatSocket.onclose = function(e) {
            console.log('Chat WebSocket disconnected');
            connectionStatus.textContent = 'Disconnected';
            connectionStatus.className = 'connection-status disconnected';
            
            // Attempt to reconnect
            if (reconnectAttempts < maxReconnectAttempts) {
                reconnectAttempts++;
                setTimeout(connectWebSocket, 2000 * reconnectAttempts);
            }
        };
        
        chatSocket.onerror = function(e) {
            console.error('Chat WebSocket error:', e);
        };
    }
    
    // Handle WebSocket messages
    function handleWebSocketMessage(data) {
        switch(data.type) {
            case 'chat_message':
                addMessageToChat(data);
                break;
            case 'connection_status':
                console.log('Connection status:', data.message);
                break;
            case 'error':
                showError(data.message);
                break;
        }
    }
    
    // Add message to chat
    function addMessageToChat(messageData) {
        const messageDiv = document.createElement('div');
        const isOwnMessage = messageData.sender_type === (isPatient ? 'patient' : 'psychologist');
        
        messageDiv.className = `message ${isOwnMessage ? 'own' : ''}`;
        messageDiv.innerHTML = `
            <div class="message-avatar">
                ${messageData.sender.charAt(0).toUpperCase()}
            </div>
            <div class="message-content">
                <div class="message-text">${escapeHtml(messageData.message)}</div>
                <div class="message-meta">
                    ${messageData.sender} • ${formatTime(messageData.timestamp)}
                </div>
            </div>
        `;
        
        messagesContainer.appendChild(messageDiv);
        scrollToBottom();
    }
    
    // Send message
    function sendMessage() {
        const message = messageInput.value.trim();
        if (message && chatSocket && chatSocket.readyState === WebSocket.OPEN) {
            chatSocket.send(JSON.stringify({
                'type': 'chat_message',
                'message': message
            }));
            messageInput.value = '';
        }
    }
    
    // Event listeners
    sendButton.addEventListener('click', sendMessage);
    
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });
    
    // Utility functions
    function scrollToBottom() {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    function formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    }
    
    function showError(message) {
        // You can implement a toast notification here
        console.error('Chat error:', message);
    }
    
    // Initialize connection if chat is active
    if (isChatActive) {
        connectWebSocket();
    }
    
    // Auto-scroll to bottom on page load
    scrollToBottom();
    
    // Check chat status periodically
    setInterval(function() {
        fetch(`/consultation/api/chat/${consultationId}/status/`)
            .then(response => response.json())
            .then(data => {
                if (data.is_active !== isChatActive) {
                    location.reload(); // Reload page if chat status changed
                }
            })
            .catch(error => console.error('Error checking chat status:', error));
    }, 30000); // Check every 30 seconds
});
</script>
{% endblock %}
