{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}
    {% trans "Complete Consultation" %} - {{ consultation.consultation_type.display_name }} - ECPI
{% endblock %}

{% block extra_css %}
<link href="{% static 'css/consultation.css' %}" rel="stylesheet">
<style>
    .completion-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 2rem 0;
    }
    
    .completion-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-top: -50px;
        position: relative;
        z-index: 10;
    }
    
    .consultation-summary {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border-left: 4px solid #28a745;
    }
    
    .completion-actions {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-top: 2rem;
    }
    
    .btn-complete {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        border-radius: 25px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-complete:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
    }
    
    .session-stats {
        display: flex;
        justify-content: space-around;
        text-align: center;
        margin: 2rem 0;
    }
    
    .stat-item {
        flex: 1;
        padding: 1rem;
    }
    
    .stat-value {
        font-size: 2rem;
        font-weight: bold;
        color: #28a745;
        display: block;
    }
    
    .stat-label {
        color: #6c757d;
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .completion-checklist {
        list-style: none;
        padding: 0;
    }
    
    .completion-checklist li {
        padding: 0.5rem 0;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        align-items: center;
    }
    
    .completion-checklist li:last-child {
        border-bottom: none;
    }
    
    .completion-checklist .check-icon {
        color: #28a745;
        margin-right: 0.75rem;
        font-size: 1.1rem;
    }
    
    .next-steps {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border-radius: 10px;
        padding: 1.5rem;
        margin-top: 2rem;
    }
    
    .alert-completion {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        border: none;
        border-radius: 10px;
        color: #155724;
    }
</style>
{% endblock %}

{% block content %}
<!-- Completion Header -->
<section class="completion-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="mb-2">
                    <i class="fas fa-check-circle me-3"></i>
                    {% trans "Complete Consultation" %}
                </h1>
                <p class="mb-0 opacity-75">
                    {% trans "Finalize the consultation session and add summary" %}
                </p>
            </div>
            <div class="col-lg-4 text-end">
                <a href="{% url 'consultation:chat_room' consultation.pk %}"
                   class="btn btn-outline-light">
                    <i class="fas fa-arrow-left me-2"></i>
                    {% trans "Back to Chat" %}
                </a>
            </div>
        </div>
    </div>
</section>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Completion Card -->
            <div class="completion-card">
                <!-- Consultation Summary -->
                <div class="consultation-summary">
                    <h5 class="mb-3">
                        <i class="fas fa-info-circle me-2"></i>
                        {% trans "Session Information" %}
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-2">
                                <strong>{% trans "Client:" %}</strong><br>
                                {{ consultation.user.get_full_name|default:consultation.user.username }}
                            </p>
                            <p class="mb-2">
                                <strong>{% trans "Type:" %}</strong><br>
                                {{ consultation.consultation_type.display_name }}
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-2">
                                <strong>{% trans "Date:" %}</strong><br>
                                {{ consultation.scheduled_date|date:"M d, Y" }}
                            </p>
                            <p class="mb-2">
                                <strong>{% trans "Duration:" %}</strong><br>
                                {% if consultation.actual_start_time %}
                                    {% load mathfilters %}
                                    {% now "U" as current_time %}
                                    {% with start_time=consultation.actual_start_time|date:"U"|add:"0" %}
                                        {% with duration_seconds=current_time|sub:start_time %}
                                            {% with duration_minutes=duration_seconds|div:60 %}
                                                {{ duration_minutes|floatformat:0 }} {% trans "minutes" %}
                                            {% endwith %}
                                        {% endwith %}
                                    {% endwith %}
                                {% else %}
                                    {{ consultation.consultation_type.default_duration }} {% trans "minutes" %}
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- Session Statistics -->
                <div class="session-stats">
                    <div class="stat-item">
                        <span class="stat-value">{{ consultation.messages.count }}</span>
                        <span class="stat-label">{% trans "Messages" %}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">
                            {% if consultation.actual_start_time %}
                                {% now "U" as current_time %}
                                {% with start_time=consultation.actual_start_time|date:"U"|add:"0" %}
                                    {% with duration_seconds=current_time|sub:start_time %}
                                        {% with duration_minutes=duration_seconds|div:60 %}
                                            {{ duration_minutes|floatformat:0 }}
                                        {% endwith %}
                                    {% endwith %}
                                {% endwith %}
                            {% else %}
                                0
                            {% endif %}
                        </span>
                        <span class="stat-label">{% trans "Minutes" %}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">
                            {% if consultation.status == 'in_progress' %}1{% else %}0{% endif %}
                        </span>
                        <span class="stat-label">{% trans "Active" %}</span>
                    </div>
                </div>
                
                <!-- Completion Checklist -->
                <div class="alert alert-completion">
                    <h6 class="mb-3">
                        <i class="fas fa-clipboard-check me-2"></i>
                        {% trans "Session Completion Checklist" %}
                    </h6>
                    
                    <ul class="completion-checklist">
                        <li>
                            <i class="fas fa-check check-icon"></i>
                            {% trans "Consultation session conducted successfully" %}
                        </li>
                        <li>
                            <i class="fas fa-check check-icon"></i>
                            {% trans "Client concerns addressed and discussed" %}
                        </li>
                        <li>
                            <i class="fas fa-check check-icon"></i>
                            {% trans "Appropriate recommendations provided" %}
                        </li>
                        <li>
                            <i class="fas fa-check check-icon"></i>
                            {% trans "Follow-up plan discussed if needed" %}
                        </li>
                    </ul>
                </div>
                
                <!-- Completion Form -->
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="completion-actions">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-2">{% trans "Ready to complete?" %}</h6>
                                <small class="text-muted">
                                    {% trans "This will mark the consultation as completed and allow you to add a detailed summary." %}
                                </small>
                            </div>
                            <div>
                                <button type="submit" class="btn btn-success btn-complete">
                                    <i class="fas fa-check me-2"></i>
                                    {% trans "Complete Session" %}
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
                
                <!-- Next Steps -->
                <div class="next-steps">
                    <h6 class="mb-3">
                        <i class="fas fa-arrow-right me-2"></i>
                        {% trans "What happens next?" %}
                    </h6>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-start mb-3">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
                                         style="width: 30px; height: 30px; font-size: 0.875rem;">1</div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-1">{% trans "Add Summary" %}</h6>
                                    <small class="text-muted">
                                        {% trans "Document session outcomes and recommendations" %}
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-start mb-3">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
                                         style="width: 30px; height: 30px; font-size: 0.875rem;">2</div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-1">{% trans "Client Notification" %}</h6>
                                    <small class="text-muted">
                                        {% trans "Client will be notified of session completion" %}
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-start mb-3">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
                                         style="width: 30px; height: 30px; font-size: 0.875rem;">3</div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-1">{% trans "Follow-up Planning" %}</h6>
                                    <small class="text-muted">
                                        {% trans "Schedule follow-up sessions if recommended" %}
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-start mb-3">
                                <div class="flex-shrink-0">
                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
                                         style="width: 30px; height: 30px; font-size: 0.875rem;">4</div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-1">{% trans "Client Feedback" %}</h6>
                                    <small class="text-muted">
                                        {% trans "Client can rate and provide feedback" %}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-update session duration
    const startTime = new Date('{{ consultation.actual_start_time|date:"c" }}');
    const durationElement = document.querySelector('.stat-value');
    
    if (startTime && durationElement) {
        setInterval(function() {
            const now = new Date();
            const durationMinutes = Math.floor((now - startTime) / (1000 * 60));
            durationElement.textContent = durationMinutes;
        }, 60000); // Update every minute
    }
    
    // Confirmation dialog for completion
    const completeForm = document.querySelector('form');
    completeForm.addEventListener('submit', function(e) {
        if (!confirm('{% trans "Are you sure you want to complete this consultation session? This action cannot be undone." %}')) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
