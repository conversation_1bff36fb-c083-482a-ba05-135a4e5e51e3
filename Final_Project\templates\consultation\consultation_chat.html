{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}
    {% trans "Consultation Chat" %} - {{ consultation.consultation_type.display_name }} - ECPI
{% endblock %}

{% block extra_css %}
<link href="{% static 'css/consultation.css' %}" rel="stylesheet">
<style>
    .chat-session {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
    }
    
    .chat-container {
        height: 70vh;
        min-height: 500px;
        border: none;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .chat-header {
        background: #2c3e50;
        color: white;
        padding: 1.5rem;
        border-radius: 15px 15px 0 0;
    }
    
    .chat-messages {
        flex: 1;
        padding: 1rem;
        overflow-y: auto;
        background: #f8f9fa;
        max-height: calc(70vh - 200px);
    }
    
    .message {
        margin-bottom: 1rem;
        display: flex;
        align-items: flex-start;
    }
    
    .message.own {
        justify-content: flex-end;
    }
    
    .message-bubble {
        max-width: 70%;
        padding: 0.75rem 1rem;
        border-radius: 18px;
        word-wrap: break-word;
        position: relative;
    }
    
    .message.own .message-bubble {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-bottom-right-radius: 5px;
    }
    
    .message:not(.own) .message-bubble {
        background: white;
        border: 1px solid #e9ecef;
        color: #333;
        border-bottom-left-radius: 5px;
    }
    
    .message-info {
        font-size: 0.75rem;
        opacity: 0.7;
        margin-top: 0.25rem;
    }
    
    .message.own .message-info {
        text-align: right;
    }
    
    .chat-input-container {
        padding: 1.5rem;
        background: white;
        border-top: 1px solid #e9ecef;
        border-radius: 0 0 15px 15px;
    }
    
    .typing-indicator {
        padding: 0.5rem 1rem;
        font-style: italic;
        color: #6c757d;
        font-size: 0.875rem;
    }
    
    .consultation-info {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .status-indicator {
        display: inline-flex;
        align-items: center;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 600;
    }
    
    .status-in_progress {
        background: #d4edda;
        color: #155724;
    }
    
    .status-confirmed {
        background: #cce5ff;
        color: #004085;
    }
    
    .file-attachment {
        display: flex;
        align-items: center;
        padding: 0.5rem;
        background: rgba(255,255,255,0.1);
        border-radius: 8px;
        margin-top: 0.5rem;
    }
    
    .system-message {
        text-align: center;
        margin: 1rem 0;
    }
    
    .system-message .message-bubble {
        background: #e9ecef;
        color: #6c757d;
        font-size: 0.875rem;
        border-radius: 15px;
        padding: 0.5rem 1rem;
        display: inline-block;
    }
    
    .consultation-actions {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-top: 2rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .btn-end-session {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        border: none;
        border-radius: 25px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-end-session:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
    }
    
    .connection-status {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 600;
        z-index: 1000;
    }
    
    .connection-connected {
        background: #d4edda;
        color: #155724;
    }
    
    .connection-disconnected {
        background: #f8d7da;
        color: #721c24;
    }
    
    .message-input {
        border: 2px solid #e9ecef;
        border-radius: 25px;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }
    
    .message-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-send {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 50%;
        width: 45px;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }
    
    .btn-send:hover {
        transform: scale(1.1);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }
</style>
{% endblock %}

{% block content %}
<!-- Connection Status Indicator -->
<div id="connectionStatus" class="connection-status connection-disconnected">
    <i class="fas fa-circle me-1"></i>
    <span id="connectionText">{% trans "Connecting..." %}</span>
</div>

<!-- Chat Session Header -->
<section class="chat-session">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="mb-2">
                    <i class="fas fa-comments me-3"></i>
                    {% trans "Live Consultation" %}
                </h1>
                <p class="mb-0 opacity-75">
                    {{ consultation.consultation_type.display_name }} - 
                    {{ consultation.scheduled_date|date:"M d, Y" }} at {{ consultation.scheduled_start_time|time:"H:i" }}
                </p>
            </div>
            <div class="col-lg-4 text-end">
                <div class="status-indicator status-{{ consultation.status }}">
                    <i class="fas fa-circle me-2"></i>
                    {{ consultation.get_status_display }}
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container py-4">
    <div class="row">
        <!-- Main Chat Area -->
        <div class="col-lg-8">
            <!-- Consultation Info -->
            <div class="consultation-info">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-2">
                            {% if is_psychologist %}
                                <i class="fas fa-user me-2"></i>{% trans "Client" %}
                            {% else %}
                                <i class="fas fa-user-md me-2"></i>{% trans "Psychologist" %}
                            {% endif %}
                        </h6>
                        <p class="mb-0">
                            {% if is_psychologist %}
                                {{ consultation.user.get_full_name|default:consultation.user.username }}
                            {% else %}
                                {{ consultation.psychologist.user.get_full_name }}
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-2">
                            <i class="fas fa-clock me-2"></i>{% trans "Duration" %}
                        </h6>
                        <p class="mb-0">{{ consultation.consultation_type.default_duration }} {% trans "minutes" %}</p>
                    </div>
                </div>
                
                {% if consultation.user_notes %}
                    <div class="mt-3">
                        <h6 class="fw-bold mb-2">
                            <i class="fas fa-sticky-note me-2"></i>{% trans "Client Notes" %}
                        </h6>
                        <p class="mb-0 text-muted">{{ consultation.user_notes }}</p>
                    </div>
                {% endif %}
            </div>
            
            <!-- Chat Container -->
            <div class="chat-container d-flex flex-column">
                <div class="chat-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-lock me-2"></i>
                            {% trans "Secure Chat Session" %}
                        </h5>
                        <div class="d-flex align-items-center">
                            <span id="sessionTimer" class="me-3">00:00</span>
                            <div id="participantStatus" class="d-flex align-items-center">
                                <i class="fas fa-circle text-success me-1"></i>
                                <small>{% trans "Online" %}</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div id="chatMessages" class="chat-messages">
                    <!-- System welcome message -->
                    <div class="system-message">
                        <div class="message-bubble">
                            <i class="fas fa-shield-alt me-1"></i>
                            {% trans "Secure consultation session started. All messages are encrypted and confidential." %}
                        </div>
                    </div>
                    
                    <!-- Previous messages will be loaded here -->
                    {% for message in consultation.messages.all %}
                        <div class="message {% if message.sender == request.user %}own{% endif %}">
                            <div class="message-bubble">
                                {% if message.message_type == 'file' and message.attachment %}
                                    <div class="file-attachment">
                                        <i class="fas fa-paperclip me-2"></i>
                                        <a href="{{ message.attachment.url }}" target="_blank" class="text-decoration-none">
                                            {{ message.attachment.name|slice:"20:" }}
                                        </a>
                                    </div>
                                {% endif %}
                                {{ message.message }}
                                <div class="message-info">
                                    {{ message.sender.get_full_name|default:message.sender.username }} • 
                                    {{ message.created_at|date:"H:i" }}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
                
                <div id="typingIndicator" class="typing-indicator d-none">
                    <i class="fas fa-ellipsis-h"></i>
                    <span id="typingText">{% trans "Typing..." %}</span>
                </div>
                
                <div class="chat-input-container">
                    <form id="messageForm" class="d-flex align-items-center">
                        <div class="flex-grow-1 me-3">
                            <input type="text" 
                                   id="messageInput" 
                                   class="form-control message-input" 
                                   placeholder="{% trans 'Type your message...' %}"
                                   maxlength="1000"
                                   autocomplete="off">
                        </div>
                        <div class="d-flex align-items-center">
                            <label for="fileInput" class="btn btn-outline-secondary me-2" title="{% trans 'Attach file' %}">
                                <i class="fas fa-paperclip"></i>
                            </label>
                            <input type="file" id="fileInput" class="d-none" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt">
                            <button type="submit" class="btn btn-primary btn-send" title="{% trans 'Send message' %}">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Session Info -->
            <div class="consultation-actions">
                <h6 class="fw-bold mb-3">
                    <i class="fas fa-cog me-2"></i>{% trans "Session Controls" %}
                </h6>
                
                <div class="d-grid gap-2">
                    {% if is_psychologist %}
                        <button id="endSessionBtn" class="btn btn-end-session">
                            <i class="fas fa-stop me-2"></i>
                            {% trans "End Session" %}
                        </button>
                        <a href="{% url 'consultation:consultation_summary' consultation.pk %}" 
                           class="btn btn-outline-primary">
                            <i class="fas fa-clipboard me-2"></i>
                            {% trans "Add Summary" %}
                        </a>
                    {% else %}
                        <button id="leaveSessionBtn" class="btn btn-outline-secondary">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            {% trans "Leave Session" %}
                        </button>
                    {% endif %}
                    
                    <a href="{% url 'consultation:consultation_detail' consultation.pk %}" 
                       class="btn btn-outline-info">
                        <i class="fas fa-info-circle me-2"></i>
                        {% trans "Session Details" %}
                    </a>
                </div>
                
                <hr class="my-3">
                
                <div class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        {% trans "This session is encrypted and confidential" %}
                    </small>
                </div>
            </div>
            
            <!-- Quick Resources -->
            <div class="consultation-actions mt-3">
                <h6 class="fw-bold mb-3">
                    <i class="fas fa-book me-2"></i>{% trans "Quick Resources" %}
                </h6>
                
                <div class="d-grid gap-2">
                    <a href="{% url 'resources:resource_list' %}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-external-link-alt me-1"></i>
                        {% trans "Browse Resources" %}
                    </a>
                    {% if is_psychologist %}
                        <button class="btn btn-outline-info btn-sm" onclick="shareResource()">
                            <i class="fas fa-share me-1"></i>
                            {% trans "Share Resource" %}
                        </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// WebSocket connection
let chatSocket = null;
let sessionStartTime = new Date();
let typingTimer = null;
let isTyping = false;

// Make socket available globally for notifications
window.consultationSocket = null;

// Initialize chat
document.addEventListener('DOMContentLoaded', function() {
    initializeChat();
    startSessionTimer();
    setupEventListeners();
});

function initializeChat() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws/consultation/{{ consultation.pk }}/`;
    
    chatSocket = new WebSocket(wsUrl);

    // Make socket available globally for notifications
    window.consultationSocket = chatSocket;

    chatSocket.onopen = function(e) {
        updateConnectionStatus(true);
        console.log('Chat connection established');

        // Show connection notification
        if (window.notificationManager) {
            notificationManager.success(
                '{% trans "Connected" %}',
                '{% trans "Chat connection established successfully" %}'
            );
        }
    };
    
    chatSocket.onmessage = function(e) {
        const data = JSON.parse(e.data);
        displayMessage(data);

        // Show notification for new messages from other user
        if (data.type === 'consultation_message' && data.user_id != {{ request.user.id }}) {
            // Only show notification if page is not in focus
            if (!document.hasFocus() && window.notificationManager) {
                showNewMessage(data.user, data.message);
            }
        }
    };
    
    chatSocket.onclose = function(e) {
        updateConnectionStatus(false);
        console.log('Chat connection closed');
        
        // Attempt to reconnect after 3 seconds
        setTimeout(function() {
            if (chatSocket.readyState === WebSocket.CLOSED) {
                initializeChat();
            }
        }, 3000);
    };
    
    chatSocket.onerror = function(e) {
        console.error('Chat connection error:', e);
        updateConnectionStatus(false);
    };
}

function setupEventListeners() {
    const messageForm = document.getElementById('messageForm');
    const messageInput = document.getElementById('messageInput');
    const fileInput = document.getElementById('fileInput');
    
    // Message form submission
    messageForm.addEventListener('submit', function(e) {
        e.preventDefault();
        sendMessage();
    });
    
    // Enter key to send message
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
    
    // Typing indicator
    messageInput.addEventListener('input', function() {
        if (!isTyping) {
            isTyping = true;
            sendTypingStatus(true);
        }
        
        clearTimeout(typingTimer);
        typingTimer = setTimeout(function() {
            isTyping = false;
            sendTypingStatus(false);
        }, 1000);
    });
    
    // File upload
    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            uploadFile(e.target.files[0]);
        }
    });
    
    // End session button
    const endSessionBtn = document.getElementById('endSessionBtn');
    if (endSessionBtn) {
        endSessionBtn.addEventListener('click', function() {
            if (confirm('{% trans "Are you sure you want to end this consultation session?" %}')) {
                endSession();
            }
        });
    }
    
    // Leave session button
    const leaveSessionBtn = document.getElementById('leaveSessionBtn');
    if (leaveSessionBtn) {
        leaveSessionBtn.addEventListener('click', function() {
            if (confirm('{% trans "Are you sure you want to leave this consultation session?" %}')) {
                window.location.href = '{% url "consultation:consultation_detail" consultation.pk %}';
            }
        });
    }
}

function sendMessage() {
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();
    
    if (message && chatSocket.readyState === WebSocket.OPEN) {
        chatSocket.send(JSON.stringify({
            'message': message,
            'type': 'text'
        }));
        
        messageInput.value = '';
        messageInput.focus();
    }
}

function displayMessage(data) {
    const messagesContainer = document.getElementById('chatMessages');
    const isOwnMessage = data.user_id === {{ request.user.id }};
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${isOwnMessage ? 'own' : ''}`;
    
    const timestamp = new Date(data.timestamp).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
    });
    
    messageDiv.innerHTML = `
        <div class="message-bubble">
            ${data.message}
            <div class="message-info">
                ${data.user} • ${timestamp}
            </div>
        </div>
    `;
    
    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function sendTypingStatus(typing) {
    if (chatSocket.readyState === WebSocket.OPEN) {
        chatSocket.send(JSON.stringify({
            'type': 'typing',
            'typing': typing
        }));
    }
}

function updateConnectionStatus(connected) {
    const statusElement = document.getElementById('connectionStatus');
    const textElement = document.getElementById('connectionText');
    
    if (connected) {
        statusElement.className = 'connection-status connection-connected';
        textElement.textContent = '{% trans "Connected" %}';
    } else {
        statusElement.className = 'connection-status connection-disconnected';
        textElement.textContent = '{% trans "Disconnected" %}';
    }
}

function startSessionTimer() {
    const timerElement = document.getElementById('sessionTimer');
    
    setInterval(function() {
        const now = new Date();
        const elapsed = Math.floor((now - sessionStartTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        
        timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }, 1000);
}

function uploadFile(file) {
    // File upload implementation would go here
    console.log('File upload:', file.name);
    alert('{% trans "File upload feature coming soon!" %}');
}

function endSession() {
    if (chatSocket) {
        chatSocket.send(JSON.stringify({
            'type': 'end_session'
        }));
    }
    
    // Redirect to completion page
    window.location.href = '{% url "consultation:complete_consultation" consultation.pk %}';
}

function shareResource() {
    // Resource sharing implementation
    alert('{% trans "Resource sharing feature coming soon!" %}');
}

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (chatSocket) {
        chatSocket.close();
    }
});
</script>
{% endblock %}
