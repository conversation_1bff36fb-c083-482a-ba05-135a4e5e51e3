{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Consultation Details" %} - ECPI{% endblock %}

{% block extra_css %}
<link href="{% static 'css/consultation.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2">{% trans "Consultation Details" %}</h1>
                <span class="badge bg-{{ consultation.status }} fs-6 p-2">
                    {% if consultation.status == 'pending' %}
                        <i class="fas fa-clock me-1"></i>{% trans "Pending" %}
                    {% elif consultation.status == 'confirmed' %}
                        <i class="fas fa-check me-1"></i>{% trans "Confirmed" %}
                    {% elif consultation.status == 'in_progress' %}
                        <i class="fas fa-play me-1"></i>{% trans "In Progress" %}
                    {% elif consultation.status == 'completed' %}
                        <i class="fas fa-check-circle me-1"></i>{% trans "Completed" %}
                    {% elif consultation.status == 'cancelled' %}
                        <i class="fas fa-times me-1"></i>{% trans "Cancelled" %}
                    {% endif %}
                </span>
            </div>
            
            <!-- Main Card -->
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <!-- Consultation Info -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5 class="mb-3">{% trans "Consultation Information" %}</h5>
                            <div class="info-item mb-3">
                                <strong>{% trans "Type:" %}</strong>
                                <span class="ms-2">{{ consultation.consultation_type.display_name }}</span>
                                {% if consultation.is_free_consultation %}
                                    <span class="badge bg-success ms-2">{% trans "Free" %}</span>
                                {% endif %}
                            </div>
                            <div class="info-item mb-3">
                                <strong>{% trans "Date & Time:" %}</strong>
                                <span class="ms-2">
                                    {{ consultation.scheduled_date|date:"l, F d, Y" }} 
                                    at {{ consultation.scheduled_start_time|time:"H:i" }}
                                </span>
                            </div>
                            <div class="info-item mb-3">
                                <strong>{% trans "Duration:" %}</strong>
                                <span class="ms-2">{{ consultation.duration_minutes }} {% trans "minutes" %}</span>
                            </div>
                            {% if not consultation.is_free_consultation %}
                                <div class="info-item mb-3">
                                    <strong>{% trans "Price:" %}</strong>
                                    <span class="ms-2">{{ consultation.price }} {{ consultation.currency }}</span>
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6">
                            <h5 class="mb-3">
                                {% if consultation.user == user %}
                                    {% trans "Your Psychologist" %}
                                {% else %}
                                    {% trans "Client Information" %}
                                {% endif %}
                            </h5>
                            
                            {% if consultation.user == user %}
                                <!-- Show psychologist info to user -->
                                <div class="d-flex align-items-center mb-3">
                                    <img src="{% if consultation.psychologist.user.profile.avatar %}{{ consultation.psychologist.user.profile.avatar.url }}{% else %}{% static 'images/default-avatar.png' %}{% endif %}" 
                                         alt="Dr. {{ consultation.psychologist.user.get_full_name }}" 
                                         class="rounded-circle me-3" width="60" height="60">
                                    <div>
                                        <h6 class="mb-1">Dr. {{ consultation.psychologist.user.get_full_name }}</h6>
                                        <small class="text-muted">{{ consultation.psychologist.years_of_experience }} {% trans "years experience" %}</small>
                                    </div>
                                </div>
                            {% else %}
                                <!-- Show client info to psychologist -->
                                <div class="d-flex align-items-center mb-3">
                                    <img src="{% if consultation.user.profile.avatar %}{{ consultation.user.profile.avatar.url }}{% else %}{% static 'images/default-avatar.png' %}{% endif %}" 
                                         alt="{{ consultation.user.get_full_name }}" 
                                         class="rounded-circle me-3" width="60" height="60">
                                    <div>
                                        <h6 class="mb-1">{{ consultation.user.get_full_name|default:consultation.user.username }}</h6>
                                        <small class="text-muted">{% trans "Client" %}</small>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- User Notes -->
                    {% if consultation.user_notes %}
                        <div class="mb-4">
                            <h5 class="mb-3">{% trans "Client's Concerns" %}</h5>
                            <div class="card bg-light">
                                <div class="card-body">
                                    <p class="mb-0">{{ consultation.user_notes }}</p>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                    
                    <!-- Payment Status -->
                    {% if not consultation.is_free_consultation %}
                        <div class="mb-4">
                            <h5 class="mb-3">{% trans "Payment Status" %}</h5>
                            <div class="d-flex align-items-center">
                                {% if consultation.payment_status == 'paid' %}
                                    <span class="badge bg-success fs-6 p-2">
                                        <i class="fas fa-check-circle me-1"></i>{% trans "Payment Completed" %}
                                    </span>
                                {% elif consultation.payment_status == 'pending' %}
                                    <span class="badge bg-warning fs-6 p-2">
                                        <i class="fas fa-clock me-1"></i>{% trans "Payment Pending" %}
                                    </span>
                                    {% if consultation.user == user %}
                                        <a href="{% url 'payments:checkout' consultation_id=consultation.pk %}" 
                                           class="btn btn-primary btn-sm ms-3">
                                            {% trans "Complete Payment" %}
                                        </a>
                                    {% endif %}
                                {% elif consultation.payment_status == 'failed' %}
                                    <span class="badge bg-danger fs-6 p-2">
                                        <i class="fas fa-times me-1"></i>{% trans "Payment Failed" %}
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                    {% endif %}
                    
                    <!-- Action Buttons -->
                    <div class="mb-4">
                        <h5 class="mb-3">{% trans "Actions" %}</h5>
                        <div class="d-flex gap-2 flex-wrap">
                            <!-- Start/Join Consultation -->
                            {% if can_start and consultation.status == 'confirmed' %}
                                <a href="{% url 'consultation:chat_room' consultation.pk %}"
                                   class="btn btn-success btn-lg">
                                    <i class="fas fa-comments me-2"></i>{% trans "Start Chat Session" %}
                                </a>
                            {% elif consultation.status == 'in_progress' %}
                                <a href="{% url 'consultation:chat_room' consultation.pk %}"
                                   class="btn btn-primary btn-lg">
                                    <i class="fas fa-comments me-2"></i>{% trans "Join Chat" %}
                                </a>
                            {% elif consultation.status == 'confirmed' %}
                                <a href="{% url 'consultation:chat_room' consultation.pk %}"
                                   class="btn btn-outline-primary btn-lg">
                                    <i class="fas fa-comments me-2"></i>{% trans "View Chat Room" %}
                                </a>
                            {% endif %}
                            
                            <!-- Complete Consultation (Psychologist only) -->
                            {% if consultation.status == 'in_progress' and consultation.psychologist.user == user %}
                                <a href="{% url 'consultation:complete_consultation' consultation.pk %}" 
                                   class="btn btn-warning">
                                    <i class="fas fa-check me-2"></i>{% trans "Complete Session" %}
                                </a>
                            {% endif %}
                            
                            <!-- Rate Consultation (User only, after completion) -->
                            {% if can_rate %}
                                <a href="{% url 'consultation:rate_consultation' consultation.pk %}"
                                   class="btn btn-warning btn-lg">
                                    <i class="fas fa-star me-2"></i>{% trans "Rate This Session" %}
                                </a>
                                <small class="d-block text-muted mt-1">
                                    {% trans "Your feedback helps improve our services" %}
                                </small>
                            {% endif %}
                            
                            <!-- Fill Summary (Psychologist only, after completion) -->
                            {% if can_fill_summary %}
                                <a href="{% url 'consultation:consultation_summary' consultation.pk %}"
                                   class="btn btn-secondary">
                                    <i class="fas fa-file-medical me-2"></i>{% trans "Fill Summary" %}
                                </a>
                            {% endif %}

                            <!-- Payment Action (if payment is pending) -->
                            {% if consultation.payment_status == 'pending' and consultation.user == user %}
                                <a href="{% url 'payments:checkout' consultation.pk %}"
                                   class="btn btn-warning">
                                    <i class="fas fa-credit-card me-2"></i>{% trans "Complete Payment" %}
                                </a>
                            {% endif %}

                            <!-- Cancel Consultation (if pending or confirmed) -->
                            {% if consultation.status in 'pending,confirmed' and consultation.user == user %}
                                <form method="post" action="{% url 'consultation:cancel_consultation' consultation.pk %}"
                                      class="d-inline" onsubmit="return confirm('{% trans "Are you sure you want to cancel this consultation?" %}')">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-outline-danger">
                                        <i class="fas fa-times me-2"></i>{% trans "Cancel Consultation" %}
                                    </button>
                                </form>
                            {% endif %}

                            <!-- Reschedule Consultation (if pending or confirmed) -->
                            {% if consultation.status in 'pending,confirmed' and consultation.user == user %}
                                <a href="{% url 'consultation:book_consultation' consultation.psychologist.id %}?reschedule={{ consultation.pk }}"
                                   class="btn btn-outline-primary">
                                    <i class="fas fa-calendar-alt me-2"></i>{% trans "Reschedule" %}
                                </a>
                            {% endif %}

                            <!-- Contact Support -->
                            <a href="mailto:<EMAIL>?subject=Consultation {{ consultation.pk }}"
                               class="btn btn-outline-info">
                                <i class="fas fa-headset me-2"></i>{% trans "Contact Support" %}
                            </a>
                        </div>
                    </div>
                    
                    <!-- Consultation Summary (if available) -->
                    {% if consultation.summary %}
                        <div class="mb-4">
                            <h5 class="mb-3">{% trans "Consultation Summary" %}</h5>
                            <div class="card border-info">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6 class="fw-bold">{% trans "Assessment" %}</h6>
                                            <p>{{ consultation.summary.assessment_notes }}</p>
                                            
                                            {% if consultation.summary.diagnosis %}
                                                <h6 class="fw-bold">{% trans "Diagnosis" %}</h6>
                                                <p>{{ consultation.summary.diagnosis }}</p>
                                            {% endif %}
                                        </div>
                                        <div class="col-md-6">
                                            <h6 class="fw-bold">{% trans "Recommendations" %}</h6>
                                            <p>{{ consultation.summary.get_recommendations_display }}</p>
                                            <p>{{ consultation.summary.recommendation_details }}</p>
                                            
                                            {% if consultation.summary.follow_up_needed %}
                                                <div class="alert alert-info">
                                                    <i class="fas fa-calendar me-2"></i>
                                                    {% trans "Follow-up recommended in" %} {{ consultation.summary.follow_up_timeframe }}
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    
                                    {% if consultation.summary.report_file %}
                                        <div class="mt-3">
                                            <a href="{{ consultation.summary.report_file.url }}" 
                                               class="btn btn-outline-primary btn-sm" target="_blank">
                                                <i class="fas fa-download me-2"></i>{% trans "Download Report" %}
                                            </a>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endif %}
                    
                    <!-- Rating (if available) -->
                    {% if consultation.rating %}
                        <div class="mb-4">
                            <h5 class="mb-3">
                                <i class="fas fa-star me-2 text-warning"></i>
                                {% trans "Your Session Rating" %}
                            </h5>
                            <div class="card border-warning">
                                <div class="card-header bg-warning bg-opacity-10">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <div class="d-flex align-items-center">
                                            <div class="rating-display me-3">
                                                {% for i in "12345" %}
                                                    <i class="fas fa-star {% if consultation.rating.overall_rating >= i|add:0 %}text-warning{% else %}text-muted{% endif %}" style="font-size: 1.2rem;"></i>
                                                {% endfor %}
                                            </div>
                                            <div>
                                                <span class="fw-bold fs-5">{{ consultation.rating.overall_rating }}/5</span>
                                                <small class="text-muted d-block">{% trans "Overall Rating" %}</small>
                                            </div>
                                        </div>
                                        <div class="text-end">
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                {% trans "Rated on" %} {{ consultation.rating.created_at|date:"M d, Y" }}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="rating-item mb-3 p-2 bg-light rounded">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <strong class="text-primary">{% trans "Communication Quality" %}</strong>
                                                    <div class="rating">
                                                        {% for i in "12345" %}
                                                            <i class="fas fa-star {% if consultation.rating.communication_rating >= i|add:0 %}text-warning{% else %}text-muted{% endif %}"></i>
                                                        {% endfor %}
                                                        <span class="ms-1 fw-bold">{{ consultation.rating.communication_rating }}/5</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="rating-item mb-3 p-2 bg-light rounded">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <strong class="text-primary">{% trans "Session Helpfulness" %}</strong>
                                                    <div class="rating">
                                                        {% for i in "12345" %}
                                                            <i class="fas fa-star {% if consultation.rating.helpfulness_rating >= i|add:0 %}text-warning{% else %}text-muted{% endif %}"></i>
                                                        {% endfor %}
                                                        <span class="ms-1 fw-bold">{{ consultation.rating.helpfulness_rating }}/5</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="rating-item mb-3 p-2 bg-light rounded">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <strong class="text-primary">{% trans "Psychologist Performance" %}</strong>
                                                    <div class="rating">
                                                        {% for i in "12345" %}
                                                            <i class="fas fa-star {% if consultation.rating.psychologist_rating >= i|add:0 %}text-warning{% else %}text-muted{% endif %}"></i>
                                                        {% endfor %}
                                                        <span class="ms-1 fw-bold">{{ consultation.rating.psychologist_rating }}/5</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="rating-item mb-3 p-2 bg-light rounded">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <strong class="text-primary">{% trans "Platform Experience" %}</strong>
                                                    <div class="rating">
                                                        {% for i in "12345" %}
                                                            <i class="fas fa-star {% if consultation.rating.platform_rating >= i|add:0 %}text-warning{% else %}text-muted{% endif %}"></i>
                                                        {% endfor %}
                                                        <span class="ms-1 fw-bold">{{ consultation.rating.platform_rating }}/5</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row mt-3">
                                        <div class="col-md-6">
                                            <div class="recommendation-status p-3 rounded" style="background: {% if consultation.rating.would_recommend %}#d4edda{% else %}#f8d7da{% endif %};">
                                                <div class="d-flex align-items-center">
                                                    {% if consultation.rating.would_recommend %}
                                                        <i class="fas fa-thumbs-up text-success fa-2x me-3"></i>
                                                        <div>
                                                            <strong class="text-success">{% trans "Recommends this psychologist" %}</strong>
                                                            <small class="d-block text-muted">{% trans "Would recommend to others" %}</small>
                                                        </div>
                                                    {% else %}
                                                        <i class="fas fa-thumbs-down text-danger fa-2x me-3"></i>
                                                        <div>
                                                            <strong class="text-danger">{% trans "Doesn't recommend" %}</strong>
                                                            <small class="d-block text-muted">{% trans "Would not recommend to others" %}</small>
                                                        </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {% if consultation.rating.feedback %}
                                        <div class="mt-4 pt-3 border-top">
                                            <h6 class="mb-2">
                                                <i class="fas fa-comment me-2"></i>
                                                {% trans "Your Feedback" %}
                                            </h6>
                                            <div class="feedback-content p-3 bg-light rounded">
                                                <p class="mb-0 text-dark">{{ consultation.rating.feedback }}</p>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Back Button -->
            <div class="mt-4 text-center">
                {% if consultation.user == user %}
                    <a href="{% url 'consultation:my_consultations' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>{% trans "Back to My Consultations" %}
                    </a>
                {% else %}
                    <a href="{% url 'consultation:psychologist_consultations' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Consultations" %}
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/consultation.js' %}"></script>
{% endblock %}
