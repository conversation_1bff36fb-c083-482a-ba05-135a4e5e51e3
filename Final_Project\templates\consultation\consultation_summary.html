{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}
    {% trans "Consultation Summary" %} - {{ consultation.consultation_type.display_name }} - ECPI
{% endblock %}

{% block extra_css %}
<link href="{% static 'css/consultation.css' %}" rel="stylesheet">
<style>
    .summary-header {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: white;
        padding: 2rem 0;
    }
    
    .summary-form {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-top: -50px;
        position: relative;
        z-index: 10;
    }
    
    .consultation-info {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border-left: 4px solid #007bff;
    }
    
    .form-section {
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid #e9ecef;
    }
    
    .form-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
    
    .section-title {
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #007bff;
        display: inline-block;
    }
    
    .risk-indicator {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 600;
        display: inline-block;
    }
    
    .risk-low {
        background: #d4edda;
        color: #155724;
    }
    
    .risk-medium {
        background: #fff3cd;
        color: #856404;
    }
    
    .risk-high {
        background: #f8d7da;
        color: #721c24;
    }
    
    .btn-save-summary {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        border-radius: 25px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-save-summary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
    }
    
    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    .form-select:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    .file-upload-area {
        border: 2px dashed #dee2e6;
        border-radius: 10px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
    }
    
    .file-upload-area:hover {
        border-color: #007bff;
        background: #f8f9fa;
    }
    
    .summary-actions {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-top: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Summary Header -->
<section class="summary-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="mb-2">
                    <i class="fas fa-clipboard-list me-3"></i>
                    {% trans "Consultation Summary" %}
                </h1>
                <p class="mb-0 opacity-75">
                    {% trans "Document the session outcomes and recommendations" %}
                </p>
            </div>
            <div class="col-lg-4 text-end">
                <a href="{% url 'consultation:consultation_detail' consultation.pk %}" 
                   class="btn btn-outline-light">
                    <i class="fas fa-arrow-left me-2"></i>
                    {% trans "Back to Details" %}
                </a>
            </div>
        </div>
    </div>
</section>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Consultation Information -->
            <div class="consultation-info">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-2">
                            <i class="fas fa-user me-2"></i>{% trans "Client" %}
                        </h6>
                        <p class="mb-0">{{ consultation.user.get_full_name|default:consultation.user.username }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-2">
                            <i class="fas fa-calendar me-2"></i>{% trans "Session Date" %}
                        </h6>
                        <p class="mb-0">
                            {{ consultation.scheduled_date|date:"M d, Y" }} at 
                            {{ consultation.scheduled_start_time|time:"H:i" }}
                        </p>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-2">
                            <i class="fas fa-tag me-2"></i>{% trans "Consultation Type" %}
                        </h6>
                        <p class="mb-0">{{ consultation.consultation_type.display_name }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-2">
                            <i class="fas fa-clock me-2"></i>{% trans "Duration" %}
                        </h6>
                        <p class="mb-0">
                            {% if consultation.actual_start_time and consultation.actual_end_time %}
                                {% with duration=consultation.actual_end_time|timesince:consultation.actual_start_time %}
                                    {{ duration }}
                                {% endwith %}
                            {% else %}
                                {{ consultation.consultation_type.default_duration }} {% trans "minutes" %}
                            {% endif %}
                        </p>
                    </div>
                </div>
                
                {% if consultation.user_notes %}
                    <div class="mt-3">
                        <h6 class="fw-bold mb-2">
                            <i class="fas fa-sticky-note me-2"></i>{% trans "Client's Initial Notes" %}
                        </h6>
                        <p class="mb-0 text-muted">{{ consultation.user_notes }}</p>
                    </div>
                {% endif %}
            </div>
            
            <!-- Summary Form -->
            <div class="summary-form">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <!-- Session Assessment Section -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="fas fa-stethoscope me-2"></i>
                            {% trans "Session Assessment" %}
                        </h4>
                        
                        <div class="row">
                            <div class="col-12 mb-3">
                                {{ form.presenting_concerns|as_crispy_field }}
                            </div>
                            <div class="col-12 mb-3">
                                {{ form.assessment_notes|as_crispy_field }}
                            </div>
                            <div class="col-12 mb-3">
                                {{ form.diagnosis|as_crispy_field }}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recommendations Section -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="fas fa-lightbulb me-2"></i>
                            {% trans "Recommendations & Risk Assessment" %}
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                {{ form.recommendations|as_crispy_field }}
                            </div>
                            <div class="col-md-6 mb-3">
                                {{ form.risk_level|as_crispy_field }}
                            </div>
                            <div class="col-12 mb-3">
                                {{ form.recommendation_details|as_crispy_field }}
                            </div>
                            <div class="col-12 mb-3">
                                {{ form.risk_notes|as_crispy_field }}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Follow-up Section -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="fas fa-calendar-plus me-2"></i>
                            {% trans "Follow-up Planning" %}
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    {{ form.follow_up_needed }}
                                    <label class="form-check-label" for="{{ form.follow_up_needed.id_for_label }}">
                                        {{ form.follow_up_needed.label }}
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                {{ form.follow_up_timeframe|as_crispy_field }}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Additional Information Section -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="fas fa-info-circle me-2"></i>
                            {% trans "Additional Information" %}
                        </h4>
                        
                        <div class="row">
                            <div class="col-12 mb-3">
                                {{ form.resources_provided|as_crispy_field }}
                            </div>
                            <div class="col-12 mb-3">
                                <label for="{{ form.report_file.id_for_label }}" class="form-label">
                                    {{ form.report_file.label }}
                                </label>
                                <div class="file-upload-area">
                                    <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                                    <p class="mb-2">{% trans "Upload detailed report or additional documents" %}</p>
                                    {{ form.report_file }}
                                    <small class="text-muted">
                                        {% trans "Supported formats: PDF, DOC, DOCX (Max 10MB)" %}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Form Actions -->
                    <div class="summary-actions">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <small class="text-muted">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    {% trans "This summary is confidential and will be securely stored" %}
                                </small>
                            </div>
                            <div>
                                <a href="{% url 'consultation:consultation_detail' consultation.pk %}" 
                                   class="btn btn-outline-secondary me-2">
                                    {% trans "Cancel" %}
                                </a>
                                <button type="submit" class="btn btn-primary btn-save-summary">
                                    <i class="fas fa-save me-2"></i>
                                    {% trans "Save Summary" %}
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-save draft functionality
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input, textarea, select');
    
    // Load saved draft
    loadDraft();
    
    // Save draft on input change
    inputs.forEach(input => {
        input.addEventListener('input', saveDraft);
        input.addEventListener('change', saveDraft);
    });
    
    // Clear draft on successful submission
    form.addEventListener('submit', function() {
        localStorage.removeItem('consultation_summary_draft_{{ consultation.pk }}');
    });
    
    function saveDraft() {
        const formData = new FormData(form);
        const draftData = {};
        
        for (let [key, value] of formData.entries()) {
            if (key !== 'csrfmiddlewaretoken' && key !== 'report_file') {
                draftData[key] = value;
            }
        }
        
        localStorage.setItem('consultation_summary_draft_{{ consultation.pk }}', JSON.stringify(draftData));
    }
    
    function loadDraft() {
        const draftData = localStorage.getItem('consultation_summary_draft_{{ consultation.pk }}');
        
        if (draftData) {
            const data = JSON.parse(draftData);
            
            Object.keys(data).forEach(key => {
                const input = form.querySelector(`[name="${key}"]`);
                if (input) {
                    if (input.type === 'checkbox') {
                        input.checked = data[key] === 'on';
                    } else {
                        input.value = data[key];
                    }
                }
            });
        }
    }
    
    // Risk level indicator
    const riskLevelSelect = document.querySelector('select[name="risk_level"]');
    if (riskLevelSelect) {
        riskLevelSelect.addEventListener('change', function() {
            updateRiskIndicator(this.value);
        });
        
        // Initialize on page load
        updateRiskIndicator(riskLevelSelect.value);
    }
    
    function updateRiskIndicator(riskLevel) {
        // Add visual indicator based on risk level
        const riskColors = {
            'low': '#28a745',
            'medium': '#ffc107',
            'high': '#dc3545'
        };
        
        if (riskLevel && riskColors[riskLevel]) {
            riskLevelSelect.style.borderColor = riskColors[riskLevel];
        }
    }
});
</script>
{% endblock %}
