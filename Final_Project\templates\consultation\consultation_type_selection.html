{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Select Consultation Type" %} - ECPI{% endblock %}

{% block extra_css %}
<style>
.consultation-selection-card {
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-radius: 15px;
    margin-bottom: 2rem;
}

.consultation-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    padding: 2rem;
}

.type-selection-card {
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    text-decoration: none;
    color: inherit;
    display: block;
}

.type-selection-card:hover {
    border-color: #667eea;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    transform: translateY(-3px);
    color: inherit;
    text-decoration: none;
}

.type-selection-card.free {
    border-color: #28a745;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.type-selection-card.free:hover {
    border-color: #1e7e34;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.25);
}

.type-selection-card.paid {
    border-color: #17a2b8;
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
}

.type-selection-card.paid:hover {
    border-color: #117a8b;
    box-shadow: 0 8px 25px rgba(23, 162, 184, 0.25);
}

.type-selection-card.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: #f8f9fa;
    border-color: #dee2e6;
}

.type-selection-card.disabled:hover {
    transform: none;
    box-shadow: none;
    border-color: #dee2e6;
}

.type-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.type-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.type-description {
    font-size: 1rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.type-price {
    font-size: 1.2rem;
    font-weight: 600;
}

.restriction-notice {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.psychologist-info {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="consultation-selection-card">
                <div class="consultation-header text-center">
                    <h2 class="mb-1">
                        <i class="fas fa-clipboard-list me-2"></i>
                        {% trans "Select Consultation Type" %}
                    </h2>
                    <p class="mb-0 opacity-75">
                        {% trans "Choose between free or paid consultation options" %}
                    </p>
                </div>
                
                <div class="card-body p-4">
                    <!-- Psychologist Info -->
                    <div class="psychologist-info text-center">
                        <div class="d-flex align-items-center justify-content-center mb-3">
                            {% if psychologist.user.profile_picture %}
                                <img src="{{ psychologist.user.profile_picture.url }}" 
                                     alt="{{ psychologist.user.get_full_name }}"
                                     class="rounded-circle me-3" 
                                     style="width: 60px; height: 60px; object-fit: cover;">
                            {% else %}
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3"
                                     style="width: 60px; height: 60px;">
                                    <i class="fas fa-user-md text-white fa-lg"></i>
                                </div>
                            {% endif %}
                            <div class="text-start">
                                <h5 class="mb-0">{{ psychologist.user.get_full_name }}</h5>
                                <small class="text-muted">{{ psychologist.specializations|truncatechars:50 }}</small>
                            </div>
                        </div>
                    </div>

                    <!-- Free Consultation Restriction Notice -->
                    {% if not can_book_free %}
                        <div class="restriction-notice">
                            <h6 class="text-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                {% trans "Free Consultation Limit Reached" %}
                            </h6>
                            <p class="mb-0">
                                {% trans "You have already used your free consultation within the last 2 months. You can book another free consultation after the 2-month period expires, or choose a paid consultation now." %}
                            </p>
                        </div>
                    {% endif %}

                    <!-- Consultation Type Options -->
                    <div class="row">
                        <!-- Free Consultation -->
                        <div class="col-md-6">
                            {% if can_book_free %}
                                <a href="{% url 'consultation:book_consultation' psychologist.pk %}?type=free" 
                                   class="type-selection-card free">
                            {% else %}
                                <div class="type-selection-card free disabled">
                            {% endif %}
                                <div class="text-center">
                                    <div class="type-icon text-success">
                                        <i class="fas fa-gift"></i>
                                    </div>
                                    <div class="type-title text-success">
                                        {% trans "Free Consultation" %}
                                    </div>
                                    <div class="type-description">
                                        {% trans "Mental health screening and basic counseling services at no cost." %}
                                    </div>
                                    <div class="type-price text-success">
                                        {% trans "FREE" %}
                                    </div>
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            {% trans "Limited to once every 2 months" %}
                                        </small>
                                    </div>
                                    {% if not can_book_free %}
                                        <div class="mt-2">
                                            <span class="badge bg-warning">{% trans "Not Available" %}</span>
                                        </div>
                                    {% endif %}
                                </div>
                            {% if can_book_free %}
                                </a>
                            {% else %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Paid Consultation -->
                        <div class="col-md-6">
                            <a href="{% url 'consultation:book_consultation' psychologist.pk %}?type=paid" 
                               class="type-selection-card paid">
                                <div class="text-center">
                                    <div class="type-icon text-info">
                                        <i class="fas fa-credit-card"></i>
                                    </div>
                                    <div class="type-title text-info">
                                        {% trans "Paid Consultation" %}
                                    </div>
                                    <div class="type-description">
                                        {% trans "Full range of consultation services with extended session time and comprehensive care." %}
                                    </div>
                                    <div class="type-price text-info">
                                        {% trans "Starting from" %} ${{ psychologist.consultation_fee|default:"25" }}
                                    </div>
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            {% trans "No booking restrictions" %}
                                        </small>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>

                    <!-- Information Section -->
                    <div class="alert alert-info mt-4">
                        <h6 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>
                            {% trans "Important Information" %}
                        </h6>
                        <ul class="mb-0">
                            <li>{% trans "Free consultations are limited to one per patient every 2 months" %}</li>
                            <li>{% trans "Paid consultations have no booking restrictions and offer extended services" %}</li>
                            <li>{% trans "All consultations are conducted by licensed mental health professionals" %}</li>
                            <li>{% trans "You can cancel or reschedule your appointment up to 24 hours in advance" %}</li>
                        </ul>
                    </div>

                    <!-- Back Button -->
                    <div class="text-center mt-4">
                        <a href="{% url 'consultation:psychologist_detail' psychologist.pk %}" 
                           class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            {% trans "Back to Psychologist Profile" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add click handlers for disabled cards
    document.querySelectorAll('.type-selection-card.disabled').forEach(card => {
        card.addEventListener('click', function(e) {
            e.preventDefault();
            alert('{% trans "This consultation type is not currently available for you." %}');
        });
    });
});
</script>
{% endblock %}
