{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Edit Time Slot" %} - ECPI{% endblock %}

{% block extra_css %}
<link href="{% static 'css/dashboard.css' %}" rel="stylesheet">
<style>
.edit-slot-card {
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-radius: 15px;
}

.edit-slot-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    padding: 2rem;
}

.form-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.btn-save {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
}

.btn-save:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    color: white;
}

.btn-cancel {
    background: #6c757d;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
}

.btn-cancel:hover {
    background: #5a6268;
    transform: translateY(-2px);
    color: white;
}
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card edit-slot-card">
                <div class="edit-slot-header text-center">
                    <h2 class="mb-0">
                        <i class="fas fa-edit me-2"></i>
                        {% trans "Edit Time Slot" %}
                    </h2>
                    <p class="mb-0 mt-2 opacity-75">
                        {% trans "Modify the details of your time slot" %}
                    </p>
                </div>
                
                <div class="card-body p-4">
                    <!-- Current Slot Info -->
                    <div class="alert alert-info mb-4">
                        <h6 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>
                            {% trans "Current Time Slot" %}
                        </h6>
                        <p class="mb-0">
                            <strong>{% trans "Date:" %}</strong> {{ time_slot.date|date:"l, F d, Y" }}<br>
                            <strong>{% trans "Time:" %}</strong> {{ time_slot.start_time|time:"g:i A" }} - {{ time_slot.end_time|time:"g:i A" }}<br>
                            <strong>{% trans "Bookings:" %}</strong> {{ time_slot.current_bookings }}/{{ time_slot.max_bookings }}<br>
                            <strong>{% trans "Status:" %}</strong> 
                            {% if time_slot.is_available %}
                                <span class="badge bg-success">{% trans "Available" %}</span>
                            {% else %}
                                <span class="badge bg-secondary">{% trans "Disabled" %}</span>
                            {% endif %}
                        </p>
                    </div>

                    <!-- Edit Form -->
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-calendar-alt me-2 text-primary"></i>
                                {% trans "Date & Time" %}
                            </h5>
                            <div class="row">
                                <div class="col-md-4">
                                    {{ form.date|as_crispy_field }}
                                </div>
                                <div class="col-md-4">
                                    {{ form.start_time|as_crispy_field }}
                                </div>
                                <div class="col-md-4">
                                    {{ form.end_time|as_crispy_field }}
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h5 class="mb-3">
                                <i class="fas fa-cog me-2 text-primary"></i>
                                {% trans "Settings" %}
                            </h5>
                            <div class="row">
                                <div class="col-md-6">
                                    {{ form.max_bookings|as_crispy_field }}
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="form-check form-switch">
                                            {{ form.is_available }}
                                            <label class="form-check-label" for="{{ form.is_available.id_for_label }}">
                                                {{ form.is_available.label }}
                                            </label>
                                        </div>
                                        <small class="form-text text-muted">{{ form.is_available.help_text }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Warning for existing bookings -->
                        {% if time_slot.current_bookings > 0 %}
                            <div class="alert alert-warning">
                                <h6 class="alert-heading">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    {% trans "Warning: Existing Bookings" %}
                                </h6>
                                <p class="mb-0">
                                    {% trans "This time slot has" %} <strong>{{ time_slot.current_bookings }}</strong> 
                                    {% trans "existing booking(s). Changing the date or time may affect these bookings. Please contact the patients if you make significant changes." %}
                                </p>
                            </div>
                        {% endif %}

                        <!-- Action Buttons -->
                        <div class="d-flex justify-content-between mt-4">
                            <a href="{% url 'consultation:manage_schedule' %}" class="btn btn-cancel">
                                <i class="fas fa-arrow-left me-2"></i>
                                {% trans "Cancel" %}
                            </a>
                            <button type="submit" class="btn btn-save">
                                <i class="fas fa-save me-2"></i>
                                {% trans "Save Changes" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add form validation feedback
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input[required]');
    
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.value.trim() === '') {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    });
    
    // Time validation
    const startTimeInput = document.getElementById('{{ form.start_time.id_for_label }}');
    const endTimeInput = document.getElementById('{{ form.end_time.id_for_label }}');
    
    function validateTimes() {
        if (startTimeInput.value && endTimeInput.value) {
            if (startTimeInput.value >= endTimeInput.value) {
                endTimeInput.setCustomValidity('{% trans "End time must be after start time" %}');
                endTimeInput.classList.add('is-invalid');
            } else {
                endTimeInput.setCustomValidity('');
                endTimeInput.classList.remove('is-invalid');
                endTimeInput.classList.add('is-valid');
            }
        }
    }
    
    startTimeInput.addEventListener('change', validateTimes);
    endTimeInput.addEventListener('change', validateTimes);
});
</script>
{% endblock %}
