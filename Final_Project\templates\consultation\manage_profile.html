{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Manage Profile" %} - ECPI{% endblock %}

{% block extra_css %}
<style>
.profile-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.profile-form-container {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.form-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    background: #f8f9fa;
}

.form-section h5 {
    color: #495057;
    margin-bottom: 1rem;
    font-weight: 600;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-row .form-group {
    flex: 1;
}

.btn-save-profile {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 10px;
    color: white;
    transition: all 0.3s ease;
}

.btn-save-profile:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

.btn-cancel {
    background: #6c757d;
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 10px;
    color: white;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.btn-cancel:hover {
    background: #5a6268;
    color: white;
    text-decoration: none;
}

.profile-stats {
    background: #e3f2fd;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1976d2;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
}

.working-days-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.working-day-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: white;
    border-radius: 5px;
}

.help-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.required-field {
    color: #dc3545;
}
</style>
{% endblock %}

{% block content %}
<div class="profile-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-user-edit me-3"></i>
                    {% trans "Manage Your Profile" %}
                </h1>
                <p class="mb-0 opacity-75">
                    {% trans "Update your professional information and settings" %}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'consultation:psychologist_dashboard' %}" class="btn btn-light">
                    <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Dashboard" %}
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-lg-4">
            <!-- Profile Stats -->
            <div class="profile-stats">
                <h5 class="mb-3">
                    <i class="fas fa-chart-line me-2"></i>
                    {% trans "Profile Statistics" %}
                </h5>
                <div class="row">
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-value">{{ object.total_consultations|default:0 }}</div>
                            <div class="stat-label">{% trans "Consultations" %}</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-value">{{ object.average_rating|floatformat:1|default:"N/A" }}</div>
                            <div class="stat-label">{% trans "Avg Rating" %}</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-value">{{ object.total_ratings|default:0 }}</div>
                            <div class="stat-label">{% trans "Reviews" %}</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stat-item">
                            <div class="stat-value">
                                {% if object.is_available %}
                                    <span class="text-success">{% trans "Online" %}</span>
                                {% else %}
                                    <span class="text-danger">{% trans "Offline" %}</span>
                                {% endif %}
                            </div>
                            <div class="stat-label">{% trans "Status" %}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Info -->
            <div class="profile-form-container">
                <h5 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    {% trans "Profile Information" %}
                </h5>
                <p><strong>{% trans "Name:" %}</strong> {{ object.user.get_full_name }}</p>
                <p><strong>{% trans "Email:" %}</strong> {{ object.user.email }}</p>
                <p><strong>{% trans "Approval Status:" %}</strong> 
                    <span class="badge bg-{% if object.approval_status == 'approved' %}success{% elif object.approval_status == 'pending' %}warning{% else %}danger{% endif %}">
                        {{ object.get_approval_status_display }}
                    </span>
                </p>
                <p><strong>{% trans "Member Since:" %}</strong> {{ object.user.date_joined|date:"M Y" }}</p>
            </div>
        </div>
        
        <div class="col-lg-8">
            <!-- Profile Form -->
            <div class="profile-form-container">
                <form method="post">
                    {% csrf_token %}
                    
                    <h4 class="mb-4">
                        <i class="fas fa-edit me-2"></i>
                        {% trans "Update Profile Information" %}
                    </h4>
                    
                    <!-- Professional Information -->
                    <div class="form-section">
                        <h5>{% trans "Professional Information" %}</h5>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="{{ form.specialization.id_for_label }}">
                                    {% trans "Specialization" %} <span class="required-field">*</span>
                                </label>
                                <input type="text" name="specialization" class="form-control"
                                       value="{{ form.specialization.value|default:'' }}"
                                       placeholder="{% trans 'e.g., Clinical Psychology, Cognitive Behavioral Therapy' %}" required>
                                <div class="help-text">{% trans "Your main area of psychological expertise" %}</div>
                                {% if form.specialization.errors %}
                                    <div class="text-danger">{{ form.specialization.errors }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="specialties">{% trans "Additional Specialties" %}</label>
                                <textarea name="specialties" class="form-control" rows="3"
                                          placeholder="{% trans 'List any additional areas of expertise...' %}">{{ form.specialties.value|default:'' }}</textarea>
                                <div class="help-text">{% trans "Other areas you specialize in (optional)" %}</div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="education">{% trans "Education Background" %}</label>
                                <textarea name="education" class="form-control" rows="3"
                                          placeholder="{% trans 'Your educational qualifications...' %}">{{ form.education.value|default:'' }}</textarea>
                                <div class="help-text">{% trans "Your degrees and educational background" %}</div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="certifications">{% trans "Certifications" %}</label>
                                <textarea name="certifications" class="form-control" rows="3"
                                          placeholder="{% trans 'Professional certifications and licenses...' %}">{{ form.certifications.value|default:'' }}</textarea>
                                <div class="help-text">{% trans "Professional certifications and licenses" %}</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Consultation Settings -->
                    <div class="form-section">
                        <h5>{% trans "Consultation Settings" %}</h5>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="consultation_fee">{% trans "Consultation Fee" %}</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" name="consultation_fee" class="form-control"
                                           value="{{ form.consultation_fee.value|default:'50' }}"
                                           min="0" step="0.01" placeholder="50.00">
                                </div>
                                <div class="help-text">{% trans "Your consultation fee per session (USD)" %}</div>
                            </div>
                            <div class="form-group">
                                <label for="available_languages">{% trans "Available Languages" %}</label>
                                <input type="text" name="available_languages" class="form-control"
                                       value="{{ form.available_languages.value|default:'English' }}"
                                       placeholder="{% trans 'e.g., English, Spanish, French' %}">
                                <div class="help-text">{% trans "Languages you can conduct consultations in" %}</div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" name="offers_free_consultation" class="form-check-input"
                                           id="offers_free_consultation"
                                           {% if form.offers_free_consultation.value %}checked{% endif %}>
                                    <label class="form-check-label" for="offers_free_consultation">
                                        {% trans "Offer Free Initial Consultation" %}
                                    </label>
                                </div>
                                <div class="help-text">{% trans "Check if you offer a free initial consultation" %}</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Availability Settings -->
                    <div class="form-section">
                        <h5>{% trans "Availability Settings" %}</h5>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" name="is_available" class="form-check-input"
                                           id="is_available"
                                           {% if form.is_available.value %}checked{% endif %}>
                                    <label class="form-check-label" for="is_available">
                                        {% trans "Currently Available for Consultations" %}
                                    </label>
                                </div>
                                <div class="help-text">{% trans "Check if you are currently accepting new consultations" %}</div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="working_hours_start">{% trans "Working Hours Start" %}</label>
                                <input type="time" name="working_hours_start" class="form-control"
                                       value="{{ form.working_hours_start.value|default:'09:00' }}"
                                       id="id_working_hours_start">
                            </div>
                            <div class="form-group">
                                <label for="working_hours_end">{% trans "Working Hours End" %}</label>
                                <input type="time" name="working_hours_end" class="form-control"
                                       value="{{ form.working_hours_end.value|default:'17:00' }}"
                                       id="id_working_hours_end">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label>{% trans "Working Days" %}</label>
                                <div class="working-days-grid">
                                    {% for day_value, day_name in working_days_choices %}
                                        <div class="working-day-item">
                                            <input type="checkbox" name="working_days" value="{{ day_value }}"
                                                   id="working_day_{{ day_value }}" class="form-check-input"
                                                   {% if day_value in form.working_days.value %}checked{% endif %}>
                                            <label for="working_day_{{ day_value }}" class="form-check-label">{{ day_name }}</label>
                                        </div>
                                    {% empty %}
                                        <div class="working-day-item">
                                            <input type="checkbox" name="working_days" value="monday" id="working_day_monday" class="form-check-input">
                                            <label for="working_day_monday" class="form-check-label">{% trans "Monday" %}</label>
                                        </div>
                                        <div class="working-day-item">
                                            <input type="checkbox" name="working_days" value="tuesday" id="working_day_tuesday" class="form-check-input">
                                            <label for="working_day_tuesday" class="form-check-label">{% trans "Tuesday" %}</label>
                                        </div>
                                        <div class="working-day-item">
                                            <input type="checkbox" name="working_days" value="wednesday" id="working_day_wednesday" class="form-check-input">
                                            <label for="working_day_wednesday" class="form-check-label">{% trans "Wednesday" %}</label>
                                        </div>
                                        <div class="working-day-item">
                                            <input type="checkbox" name="working_days" value="thursday" id="working_day_thursday" class="form-check-input">
                                            <label for="working_day_thursday" class="form-check-label">{% trans "Thursday" %}</label>
                                        </div>
                                        <div class="working-day-item">
                                            <input type="checkbox" name="working_days" value="friday" id="working_day_friday" class="form-check-input">
                                            <label for="working_day_friday" class="form-check-label">{% trans "Friday" %}</label>
                                        </div>
                                        <div class="working-day-item">
                                            <input type="checkbox" name="working_days" value="saturday" id="working_day_saturday" class="form-check-input">
                                            <label for="working_day_saturday" class="form-check-label">{% trans "Saturday" %}</label>
                                        </div>
                                        <div class="working-day-item">
                                            <input type="checkbox" name="working_days" value="sunday" id="working_day_sunday" class="form-check-input">
                                            <label for="working_day_sunday" class="form-check-label">{% trans "Sunday" %}</label>
                                        </div>
                                    {% endfor %}
                                </div>
                                <div class="help-text">{% trans "Select the days you are available for consultations" %}</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Submit Buttons -->
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-save-profile me-3">
                            <i class="fas fa-save me-2"></i>
                            {% trans "Save Changes" %}
                        </button>
                        <a href="{% url 'consultation:psychologist_dashboard' %}" class="btn btn-cancel">
                            <i class="fas fa-times me-2"></i>
                            {% trans "Cancel" %}
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                isValid = false;
                field.classList.add('is-invalid');
            } else {
                field.classList.remove('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('{% trans "Please fill in all required fields." %}');
        }
    });
    
    // Working hours validation
    const startTime = document.querySelector('#id_working_hours_start');
    const endTime = document.querySelector('#id_working_hours_end');
    
    if (startTime && endTime) {
        function validateWorkingHours() {
            if (startTime.value && endTime.value) {
                if (startTime.value >= endTime.value) {
                    endTime.setCustomValidity('{% trans "End time must be after start time" %}');
                } else {
                    endTime.setCustomValidity('');
                }
            }
        }
        
        startTime.addEventListener('change', validateWorkingHours);
        endTime.addEventListener('change', validateWorkingHours);
    }
});
</script>
{% endblock %}
