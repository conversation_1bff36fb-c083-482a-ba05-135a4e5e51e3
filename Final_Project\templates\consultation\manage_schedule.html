{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Manage Time Slots" %} - ECPI{% endblock %}

{% block extra_css %}
<link href="{% static 'css/dashboard.css' %}" rel="stylesheet">
<style>
.schedule-card {
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-radius: 15px;
    margin-bottom: 2rem;
}

.schedule-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    padding: 2rem;
}

.create-slot-form {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    border: 2px dashed #dee2e6;
    transition: all 0.3s ease;
}

.create-slot-form:hover {
    border-color: #667eea;
    background: #f0f4ff;
}

.time-slot-card {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    background: white;
    transition: all 0.3s ease;
    position: relative;
}

.time-slot-card:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.time-slot-card.available {
    border-left: 4px solid #28a745;
}

.time-slot-card.disabled {
    border-left: 4px solid #6c757d;
    opacity: 0.7;
}

.time-slot-card.booked {
    border-left: 4px solid #ffc107;
}

.slot-date {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.slot-time {
    font-size: 1.3rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.slot-info {
    font-size: 0.9rem;
    color: #6c757d;
}

.slot-actions {
    position: absolute;
    top: 1rem;
    right: 1rem;
}

.btn-create {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
}

.btn-create:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    color: white;
}

.btn-edit {
    background: #17a2b8;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    color: white;
    font-size: 0.8rem;
    margin-right: 0.5rem;
}

.btn-delete {
    background: #dc3545;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    color: white;
    font-size: 0.8rem;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}

.stats-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-bottom: 1rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="schedule-card">
                <div class="schedule-header text-center">
                    <h2 class="mb-1">
                        <i class="fas fa-clock me-2"></i>{% trans "Manage Time Slots" %}
                    </h2>
                    <p class="mb-0 opacity-75">{% trans "Create and manage your specific available time slots" %}</p>
                    <div class="mt-3">
                        <a href="{% url 'consultation:psychologist_dashboard' %}" class="btn btn-light">
                            <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Dashboard" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Left Column - Create New Time Slot -->
        <div class="col-lg-4">
            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-6">
                    <div class="stats-card text-center">
                        <div class="stat-number">{{ time_slots|length }}</div>
                        <div class="stat-label">{% trans "Total Slots" %}</div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="stats-card text-center">
                        <div class="stat-number">{{ total_bookings|default:0 }}</div>
                        <div class="stat-label">{% trans "Bookings" %}</div>
                    </div>
                </div>
            </div>

            <!-- Create New Time Slot Form -->
            <div class="create-slot-form">
                <h5 class="mb-3 text-center">
                    <i class="fas fa-plus-circle me-2 text-primary"></i>
                    {% trans "Create New Time Slot" %}
                </h5>

                <form method="post" novalidate>
                    {% csrf_token %}

                    <div class="mb-3">
                        {{ form.date|as_crispy_field }}
                    </div>

                    <div class="row">
                        <div class="col-6">
                            {{ form.start_time|as_crispy_field }}
                        </div>
                        <div class="col-6">
                            {{ form.end_time|as_crispy_field }}
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.max_bookings|as_crispy_field }}
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            {{ form.is_available }}
                            <label class="form-check-label" for="{{ form.is_available.id_for_label }}">
                                {{ form.is_available.label }}
                            </label>
                        </div>
                        <small class="form-text text-muted">{{ form.is_available.help_text }}</small>
                    </div>

                    <button type="submit" class="btn btn-create w-100">
                        <i class="fas fa-plus me-2"></i>
                        {% trans "Create Time Slot" %}
                    </button>
                </form>
            </div>
        </div>

        <!-- Right Column - Existing Time Slots -->
        <div class="col-lg-8">
            <div class="schedule-card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>{% trans "Your Time Slots" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if time_slots %}
                        {% for slot in time_slots %}
                            <div class="time-slot-card {% if slot.is_available %}available{% else %}disabled{% endif %} {% if slot.current_bookings > 0 %}booked{% endif %}">
                                <div class="slot-actions">
                                    <a href="{% url 'consultation:edit_time_slot' slot.pk %}" class="btn btn-edit btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form method="post" action="{% url 'consultation:delete_time_slot' slot.pk %}" class="d-inline"
                                          onsubmit="return confirm('{% trans "Are you sure you want to delete this time slot?" %}')">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-delete btn-sm"
                                                {% if slot.current_bookings > 0 %}disabled title="{% trans 'Cannot delete slot with bookings' %}"{% endif %}>
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>

                                <div class="slot-date">
                                    <i class="fas fa-calendar me-2"></i>
                                    {{ slot.date|date:"l, F d, Y" }}
                                </div>

                                <div class="slot-time">
                                    <i class="fas fa-clock me-2"></i>
                                    {{ slot.start_time|time:"g:i A" }} - {{ slot.end_time|time:"g:i A" }}
                                </div>

                                <div class="slot-info">
                                    <div class="row">
                                        <div class="col-6">
                                            <i class="fas fa-users me-1"></i>
                                            {% trans "Bookings:" %} {{ slot.current_bookings }}/{{ slot.max_bookings }}
                                        </div>
                                        <div class="col-6">
                                            <i class="fas fa-toggle-{% if slot.is_available %}on text-success{% else %}off text-secondary{% endif %} me-1"></i>
                                            {% if slot.is_available %}{% trans "Available" %}{% else %}{% trans "Disabled" %}{% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-calendar-times fa-3x mb-3"></i>
                            <h5>{% trans "No Time Slots Created" %}</h5>
                            <p class="text-muted">
                                {% trans "You haven't created any time slots yet. Use the form on the left to create your first available time slot." %}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Past Time Slots (if any) -->
            {% if past_slots %}
                <div class="schedule-card mt-4">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-history me-2"></i>{% trans "Recent Past Slots" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        {% for slot in past_slots %}
                            <div class="time-slot-card disabled">
                                <div class="slot-date">
                                    <i class="fas fa-calendar me-2"></i>
                                    {{ slot.date|date:"l, F d, Y" }}
                                </div>

                                <div class="slot-time">
                                    <i class="fas fa-clock me-2"></i>
                                    {{ slot.start_time|time:"g:i A" }} - {{ slot.end_time|time:"g:i A" }}
                                </div>

                                <div class="slot-info">
                                    <i class="fas fa-users me-1"></i>
                                    {% trans "Had" %} {{ slot.current_bookings }}/{{ slot.max_bookings }} {% trans "bookings" %}
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('form');
    const startTimeInput = document.getElementById('{{ form.start_time.id_for_label }}');
    const endTimeInput = document.getElementById('{{ form.end_time.id_for_label }}');
    const dateInput = document.getElementById('{{ form.date.id_for_label }}');

    // Time validation
    function validateTimes() {
        if (startTimeInput.value && endTimeInput.value) {
            if (startTimeInput.value >= endTimeInput.value) {
                endTimeInput.setCustomValidity('{% trans "End time must be after start time" %}');
                endTimeInput.classList.add('is-invalid');
            } else {
                endTimeInput.setCustomValidity('');
                endTimeInput.classList.remove('is-invalid');
            }
        }
    }

    // Date validation
    function validateDate() {
        if (dateInput.value) {
            const selectedDate = new Date(dateInput.value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (selectedDate <= today) {
                dateInput.setCustomValidity('{% trans "Please select a future date" %}');
                dateInput.classList.add('is-invalid');
            } else {
                dateInput.setCustomValidity('');
                dateInput.classList.remove('is-invalid');
            }
        }
    }

    startTimeInput.addEventListener('change', validateTimes);
    endTimeInput.addEventListener('change', validateTimes);
    dateInput.addEventListener('change', validateDate);

    // Auto-focus on form errors
    const errorFields = document.querySelectorAll('.is-invalid');
    if (errorFields.length > 0) {
        errorFields[0].focus();
    }

    // Confirm delete actions
    document.querySelectorAll('form[action*="delete"]').forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!confirm('{% trans "Are you sure you want to delete this time slot?" %}')) {
                e.preventDefault();
            }
        });
    });
});
</script>
{% endblock %}
