{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "My Consultations" %} - ECPI{% endblock %}

{% block extra_css %}
<style>
.consultation-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 15px;
}

.consultation-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.status-badge {
    font-size: 0.85rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-confirmed {
    background: #d1ecf1;
    color: #0c5460;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

.consultation-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.filter-tabs {
    background: white;
    border-radius: 15px;
    padding: 1rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.filter-tab {
    border: none;
    background: transparent;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    margin: 0.25rem;
    transition: all 0.3s ease;
}

.filter-tab.active {
    background: #667eea;
    color: white;
}
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <!-- Header -->
    <div class="consultation-header text-center">
        <h1 class="display-6 fw-bold mb-3">{% trans "My Consultations" %}</h1>
        <p class="lead mb-0">
            {% trans "Track your mental health journey and consultation history" %}
        </p>
    </div>

    <!-- Filter Tabs -->
    <div class="filter-tabs text-center">
        <button class="filter-tab active" data-status="all">
            <i class="fas fa-list me-2"></i>{% trans "All" %}
        </button>
        <button class="filter-tab" data-status="pending">
            <i class="fas fa-clock me-2"></i>{% trans "Pending" %}
        </button>
        <button class="filter-tab" data-status="confirmed">
            <i class="fas fa-check-circle me-2"></i>{% trans "Confirmed" %}
        </button>
        <button class="filter-tab" data-status="completed">
            <i class="fas fa-check-double me-2"></i>{% trans "Completed" %}
        </button>
        <button class="filter-tab" data-status="cancelled">
            <i class="fas fa-times-circle me-2"></i>{% trans "Cancelled" %}
        </button>
    </div>

    <!-- Consultations List -->
    <div class="row">
        {% for consultation in consultations %}
        <div class="col-lg-6 mb-4 consultation-item" data-status="{{ consultation.status }}">
            <div class="card consultation-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <h5 class="card-title mb-0">
                            {% trans "Consultation with" %} {{ consultation.psychologist.user.get_full_name }}
                        </h5>
                        <span class="status-badge status-{{ consultation.status }}">
                            {{ consultation.get_status_display }}
                        </span>
                    </div>
                    
                    <div class="consultation-details mb-3">
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">{% trans "Date & Time" %}</small>
                                <div class="fw-bold">
                                    {{ consultation.scheduled_datetime|date:"M d, Y" }}<br>
                                    {{ consultation.scheduled_datetime|date:"g:i A" }}
                                </div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">{% trans "Type" %}</small>
                                <div class="fw-bold">{{ consultation.consultation_type.name }}</div>
                            </div>
                        </div>
                    </div>
                    
                    {% if consultation.notes %}
                    <div class="mb-3">
                        <small class="text-muted">{% trans "Notes" %}</small>
                        <p class="mb-0">{{ consultation.notes|truncatewords:20 }}</p>
                    </div>
                    {% endif %}
                    
                    <div class="consultation-meta text-muted small">
                        <i class="fas fa-calendar-plus me-1"></i>
                        {% trans "Booked" %} {{ consultation.created_at|timesince }} {% trans "ago" %}
                    </div>
                </div>
                
                <div class="card-footer bg-transparent">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="consultation-actions">
                            {% if consultation.status == 'pending' %}
                                <a href="{% url 'consultation:consultation_detail' consultation.pk %}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i>{% trans "View Details" %}
                                </a>
                                <a href="{% url 'consultation:cancel_consultation' consultation.pk %}" class="btn btn-outline-danger btn-sm">
                                    <i class="fas fa-times me-1"></i>{% trans "Cancel" %}
                                </a>
                            {% elif consultation.status == 'confirmed' %}
                                <a href="{% url 'consultation:consultation_detail' consultation.pk %}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-video me-1"></i>{% trans "Join Session" %}
                                </a>
                            {% elif consultation.status == 'completed' %}
                                <a href="{% url 'consultation:consultation_detail' consultation.pk %}" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-file-alt me-1"></i>{% trans "View Summary" %}
                                </a>
                                {% if not consultation.rating %}
                                <a href="{% url 'consultation:rate_consultation' consultation.pk %}" class="btn btn-outline-warning btn-sm">
                                    <i class="fas fa-star me-1"></i>{% trans "Rate" %}
                                </a>
                                {% endif %}
                            {% else %}
                                <a href="{% url 'consultation:consultation_detail' consultation.pk %}" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-eye me-1"></i>{% trans "View Details" %}
                                </a>
                            {% endif %}
                        </div>
                        
                        {% if consultation.fee > 0 %}
                        <div class="consultation-fee">
                            <span class="fw-bold text-success">{{ consultation.fee }} ETB</span>
                        </div>
                        {% else %}
                        <div class="consultation-fee">
                            <span class="badge bg-success">{% trans "Free" %}</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-calendar-times fa-4x text-muted mb-4"></i>
                <h4 class="text-muted mb-3">{% trans "No consultations yet" %}</h4>
                <p class="text-muted mb-4">
                    {% trans "You haven't booked any consultations yet. Start your mental health journey today!" %}
                </p>
                <a href="{% url 'consultation:psychologist_list' %}" class="btn btn-primary btn-lg">
                    <i class="fas fa-search me-2"></i>{% trans "Find a Psychologist" %}
                </a>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <nav aria-label="{% trans 'Consultations pagination' %}" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
            {% endif %}
            
            {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                    </li>
                {% endif %}
            {% endfor %}
            
            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const filterTabs = document.querySelectorAll('.filter-tab');
    const consultationItems = document.querySelectorAll('.consultation-item');

    filterTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Update active tab
            filterTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            
            // Filter consultations
            const status = this.dataset.status;
            consultationItems.forEach(item => {
                if (status === 'all' || item.dataset.status === status) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });
});
</script>
{% endblock %}
