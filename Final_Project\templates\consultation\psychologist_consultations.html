{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}My Consultations - Psychologist <PERSON><PERSON>{% endblock %}

{% block extra_css %}
<style>
.consultation-card {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.consultation-card:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.status-badge {
    font-size: 0.8rem;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-weight: 600;
}

.status-pending { background: #fff3cd; color: #856404; }
.status-confirmed { background: #d1ecf1; color: #0c5460; }
.status-in-progress { background: #d4edda; color: #155724; }
.status-completed { background: #e2e3e5; color: #383d41; }
.status-cancelled { background: #f8d7da; color: #721c24; }

.filter-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-bottom: 2rem;
}

.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}
</style>
{% endblock %}

{% block content %}
<div class="dashboard-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-calendar-check me-3"></i>
                    My Consultations
                </h1>
                <p class="mb-0 opacity-75">
                    Manage and track your consultation sessions
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'consultation:psychologist_dashboard' %}" class="btn btn-light">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Filters Section -->
    <div class="filter-section">
        <form method="get" class="row align-items-end">
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select name="status" id="status" class="form-select">
                    <option value="">All Status</option>
                    {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if current_filters.status == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="date_from" class="form-label">From Date</label>
                <input type="date" name="date_from" id="date_from" class="form-control" 
                       value="{{ current_filters.date_from }}">
            </div>
            <div class="col-md-3">
                <label for="date_to" class="form-label">To Date</label>
                <input type="date" name="date_to" id="date_to" class="form-control" 
                       value="{{ current_filters.date_to }}">
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-1"></i>Filter
                </button>
            </div>
        </form>
        
        {% if current_filters.status or current_filters.date_from or current_filters.date_to %}
            <div class="mt-3">
                <a href="{% url 'consultation:psychologist_consultations' %}" class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-times me-1"></i>Clear Filters
                </a>
            </div>
        {% endif %}
    </div>

    <!-- Consultations List -->
    <div class="row">
        {% for consultation in consultations %}
            <div class="col-12">
                <div class="consultation-card">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <h6 class="mb-1">{{ consultation.user.get_full_name|default:consultation.user.username }}</h6>
                            <small class="text-muted">{{ consultation.consultation_type.display_name }}</small>
                        </div>
                        <div class="col-md-2">
                            <strong>{{ consultation.scheduled_date|date:"M d, Y" }}</strong><br>
                            <small class="text-muted">{{ consultation.scheduled_start_time|time:"g:i A" }}</small>
                        </div>
                        <div class="col-md-2">
                            <span class="status-badge status-{{ consultation.status }}">
                                {{ consultation.get_status_display }}
                            </span>
                        </div>
                        <div class="col-md-2">
                            {% if consultation.payment_status == 'free' %}
                                <span class="badge bg-success">Free</span>
                            {% else %}
                                <span class="badge bg-primary">${{ consultation.consultation_type.price }}</span>
                            {% endif %}
                        </div>
                        <div class="col-md-3 text-end">
                            <div class="btn-group" role="group">
                                <a href="{% url 'consultation:consultation_detail' consultation.pk %}" 
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                
                                {% if consultation.status == 'confirmed' or consultation.status == 'in_progress' %}
                                    <a href="{% url 'consultation:chat_room' consultation.pk %}" 
                                       class="btn btn-sm btn-primary">
                                        <i class="fas fa-comments"></i> Chat
                                    </a>
                                {% endif %}
                                
                                {% if consultation.status == 'in_progress' %}
                                    <a href="{% url 'consultation:complete_consultation' consultation.pk %}" 
                                       class="btn btn-sm btn-success">
                                        <i class="fas fa-check"></i> Complete
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    {% if consultation.notes %}
                        <div class="row mt-2">
                            <div class="col-12">
                                <small class="text-muted">
                                    <strong>Notes:</strong> {{ consultation.notes|truncatewords:20 }}
                                </small>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        {% empty %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Consultations Found</h5>
                    <p class="text-muted">
                        {% if current_filters.status or current_filters.date_from or current_filters.date_to %}
                            No consultations match your current filters.
                        {% else %}
                            You don't have any consultations yet.
                        {% endif %}
                    </p>
                </div>
            </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
        <nav aria-label="Consultations pagination" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if current_filters.status %}&status={{ current_filters.status }}{% endif %}{% if current_filters.date_from %}&date_from={{ current_filters.date_from }}{% endif %}{% if current_filters.date_to %}&date_to={{ current_filters.date_to }}{% endif %}">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if current_filters.status %}&status={{ current_filters.status }}{% endif %}{% if current_filters.date_from %}&date_from={{ current_filters.date_from }}{% endif %}{% if current_filters.date_to %}&date_to={{ current_filters.date_to }}{% endif %}">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if current_filters.status %}&status={{ current_filters.status }}{% endif %}{% if current_filters.date_from %}&date_from={{ current_filters.date_from }}{% endif %}{% if current_filters.date_to %}&date_to={{ current_filters.date_to }}{% endif %}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if current_filters.status %}&status={{ current_filters.status }}{% endif %}{% if current_filters.date_from %}&date_from={{ current_filters.date_from }}{% endif %}{% if current_filters.date_to %}&date_to={{ current_filters.date_to }}{% endif %}">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if current_filters.status %}&status={{ current_filters.status }}{% endif %}{% if current_filters.date_from %}&date_from={{ current_filters.date_from }}{% endif %}{% if current_filters.date_to %}&date_to={{ current_filters.date_to }}{% endif %}">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form when date inputs change
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        input.addEventListener('change', function() {
            // Optional: Auto-submit form when date changes
            // this.form.submit();
        });
    });
});
</script>
{% endblock %}
