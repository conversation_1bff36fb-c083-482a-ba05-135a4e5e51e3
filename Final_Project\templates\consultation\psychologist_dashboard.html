{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Psychologist Dashboard" %} - ECPI{% endblock %}

{% block extra_css %}
<link href="{% static 'css/dashboard.css' %}" rel="stylesheet">
<style>
.dashboard-card {
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-radius: 15px;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px 15px 0 0;
    padding: 2rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-bottom: 1rem;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

.quick-action-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
    display: block;
    margin-bottom: 1rem;
}

.quick-action-card:hover {
    border-color: #667eea;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
    color: inherit;
    text-decoration: none;
}

.quick-action-icon {
    font-size: 2.5rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.quick-action-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.quick-action-desc {
    font-size: 0.9rem;
    color: #6c757d;
}

.recent-consultation {
    border-left: 4px solid #667eea;
    background: #f8f9fa;
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 0 8px 8px 0;
}

.consultation-status {
    font-size: 0.8rem;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-weight: 600;
}

.status-pending { background: #fff3cd; color: #856404; }
.status-confirmed { background: #d4edda; color: #155724; }
.status-completed { background: #d1ecf1; color: #0c5460; }
.status-cancelled { background: #f8d7da; color: #721c24; }

.welcome-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Welcome Section -->
    <div class="welcome-section">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2 class="mb-2">
                    <i class="fas fa-user-md me-2 text-primary"></i>
                    {% trans "Welcome back," %} {{ user.get_full_name|default:user.username }}!
                </h2>
                <p class="text-muted mb-0">
                    {% trans "Here's an overview of your consultation practice and upcoming appointments." %}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex align-items-center justify-content-end">
                    {% if user.profile_picture %}
                        <img src="{{ user.profile_picture.url }}" 
                             alt="{{ user.get_full_name }}"
                             class="rounded-circle" 
                             style="width: 80px; height: 80px; object-fit: cover;">
                    {% else %}
                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center"
                             style="width: 80px; height: 80px;">
                            <i class="fas fa-user-md text-white fa-2x"></i>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Row -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-number">{{ total_consultations|default:0 }}</div>
                <div class="stat-label">{% trans "Total Consultations" %}</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-number">{{ pending_consultations|default:0 }}</div>
                <div class="stat-label">{% trans "Pending" %}</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-number">{{ upcoming_slots|default:0 }}</div>
                <div class="stat-label">{% trans "Available Slots" %}</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="stat-number">{{ average_rating|default:0|floatformat:1 }}</div>
                <div class="stat-label">{% trans "Average Rating" %}</div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Left Column - Quick Actions -->
        <div class="col-lg-4">
            <div class="dashboard-card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>{% trans "Quick Actions" %}
                    </h5>
                </div>
                <div class="card-body p-3">
                    <a href="{% url 'consultation:manage_schedule' %}" class="quick-action-card">
                        <div class="quick-action-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="quick-action-title">{% trans "Manage Schedule" %}</div>
                        <div class="quick-action-desc">{% trans "Create and manage your time slots" %}</div>
                    </a>

                    <a href="{% url 'consultation:psychologist_consultations' %}" class="quick-action-card">
                        <div class="quick-action-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="quick-action-title">{% trans "View Consultations" %}</div>
                        <div class="quick-action-desc">{% trans "See all your appointments" %}</div>
                    </a>

                    <a href="{% url 'consultation:manage_psychologist_profile' %}" class="quick-action-card">
                        <div class="quick-action-icon">
                            <i class="fas fa-user-edit"></i>
                        </div>
                        <div class="quick-action-title">{% trans "Edit Profile" %}</div>
                        <div class="quick-action-desc">{% trans "Update your professional information" %}</div>
                    </a>
                </div>
            </div>
        </div>

        <!-- Right Column - Recent Activity -->
        <div class="col-lg-8">
            <div class="dashboard-card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>{% trans "Recent Consultations" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_consultations %}
                        {% for consultation in recent_consultations %}
                            <div class="recent-consultation">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">
                                            {{ consultation.user.get_full_name|default:consultation.user.username }}
                                        </h6>
                                        <p class="mb-1 text-muted">
                                            <i class="fas fa-tag me-1"></i>
                                            {{ consultation.consultation_type.display_name }}
                                        </p>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            {{ consultation.scheduled_date|date:"M d, Y" }} at 
                                            {{ consultation.scheduled_start_time|time:"g:i A" }}
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <span class="consultation-status status-{{ consultation.status }}">
                                            {{ consultation.get_status_display }}
                                        </span>
                                        {% if consultation.payment_status == 'free' %}
                                            <div class="mt-1">
                                                <span class="badge bg-success">{% trans "Free" %}</span>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                        
                        <div class="text-center mt-3">
                            <a href="{% url 'consultation:psychologist_consultations' %}" 
                               class="btn btn-outline-primary">
                                <i class="fas fa-list me-2"></i>{% trans "View All Consultations" %}
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">{% trans "No Recent Consultations" %}</h6>
                            <p class="text-muted">
                                {% trans "Your recent consultation activity will appear here." %}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add some interactive effects
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // Auto-refresh stats every 5 minutes
    setInterval(function() {
        // You can add AJAX calls here to refresh statistics
        console.log('Stats refresh interval');
    }, 300000);
});
</script>
{% endblock %}
