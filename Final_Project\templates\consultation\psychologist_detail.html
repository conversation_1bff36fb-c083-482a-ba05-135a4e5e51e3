{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}Dr. {{ psychologist.user.get_full_name }} - ECPI{% endblock %}

{% block extra_css %}
<link href="{% static 'css/consultation.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <!-- Psychologist Profile -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <!-- Header -->
                    <div class="row align-items-center mb-4">
                        <div class="col-md-3 text-center">
                            <img src="{% if psychologist.user.profile.avatar %}{{ psychologist.user.profile.avatar.url }}{% else %}{% static 'images/default-avatar.png' %}{% endif %}" 
                                 alt="Dr. {{ psychologist.user.get_full_name }}" 
                                 class="rounded-circle mb-3" width="150" height="150">
                            <div class="rating">
                                <div class="star-rating mb-2">
                                    {% for i in "12345" %}
                                        <i class="fas fa-star {% if psychologist.average_rating >= i|add:0 %}text-warning{% else %}text-muted{% endif %}" style="font-size: 1.2rem;"></i>
                                    {% endfor %}
                                </div>
                                <div class="rating-info">
                                    <span class="fw-bold fs-5 text-primary">{{ psychologist.average_rating|floatformat:1 }}</span>
                                    <span class="text-muted">/ 5.0</span>
                                </div>
                                <div class="rating-count">
                                    <small class="text-muted">
                                        <i class="fas fa-users me-1"></i>
                                        {{ psychologist.total_ratings }} {% trans "patient reviews" %}
                                    </small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-9">
                            <h1 class="h2 mb-2">Dr. {{ psychologist.user.get_full_name|default:psychologist.user.username }}</h1>
                            <p class="text-muted mb-3">
                                <i class="fas fa-graduation-cap me-2"></i>
                                {{ psychologist.years_of_experience }} {% trans "years of experience" %}
                            </p>
                            
                            <!-- Status -->
                            <div class="mb-3">
                                {% if psychologist.is_available %}
                                    <span class="badge bg-success fs-6">
                                        <i class="fas fa-circle me-1"></i>{% trans "Available for Consultations" %}
                                    </span>
                                {% else %}
                                    <span class="badge bg-secondary fs-6">
                                        <i class="fas fa-circle me-1"></i>{% trans "Currently Unavailable" %}
                                    </span>
                                {% endif %}
                            </div>
                            
                            <!-- Languages -->
                            <div class="mb-3">
                                <h6 class="fw-bold">{% trans "Languages" %}</h6>
                                <p class="text-muted">
                                    {% for lang in psychologist.language_list %}
                                        {% if lang == 'en' %}English{% elif lang == 'am' %}አማርኛ{% else %}{{ lang }}{% endif %}{% if not forloop.last %}, {% endif %}
                                    {% endfor %}
                                </p>
                            </div>
                            
                            <!-- Quick Actions -->
                            <div class="d-flex gap-2 flex-wrap">
                                {% if user.is_authenticated and psychologist.is_available and not user.is_staff and not user.psychologist_profile %}
                                    <a href="{% url 'consultation:select_consultation_type' psychologist.pk %}"
                                       class="btn btn-primary btn-lg">
                                        <i class="fas fa-calendar-plus me-2"></i>{% trans "Book Consultation" %}
                                    </a>
                                {% elif user.is_staff or user.psychologist_profile %}
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        {% trans "Only regular users can book consultations" %}
                                    </div>
                                {% elif not user.is_authenticated %}
                                    <a href="{% url 'accounts:login' %}" class="btn btn-primary btn-lg">
                                        {% trans "Login to Book" %}
                                    </a>
                                {% endif %}
                                

                            </div>
                        </div>
                    </div>
                    
                    <!-- Education & Credentials -->
                    <div class="mb-4">
                        <h4 class="mb-3">{% trans "Education & Credentials" %}</h4>
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="fw-bold">{% trans "Education" %}</h6>
                                <p class="mb-3">{{ psychologist.education }}</p>
                                
                                <h6 class="fw-bold">{% trans "License Number" %}</h6>
                                <p class="mb-3">{{ psychologist.license_number }}</p>
                                
                                {% if psychologist.certifications %}
                                    <h6 class="fw-bold">{% trans "Additional Certifications" %}</h6>
                                    <p class="mb-0">{{ psychologist.certifications }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Specialties -->
                    <div class="mb-4">
                        <h4 class="mb-3">{% trans "Specialties" %}</h4>
                        <div class="row g-2">
                            {% for specialty in psychologist.specialties.all %}
                                <div class="col-auto">
                                    <span class="badge bg-primary fs-6 p-2">{{ specialty.name }}</span>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <!-- Working Hours -->
                    <div class="mb-4">
                        <h4 class="mb-3">{% trans "Working Hours" %}</h4>
                        <div class="card bg-light">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p class="mb-2">
                                            <strong>{% trans "Hours:" %}</strong> 
                                            {{ psychologist.working_hours_start }} - {{ psychologist.working_hours_end }}
                                        </p>
                                        <p class="mb-0">
                                            <strong>{% trans "Days:" %}</strong> 
                                            {% trans "Monday to Friday" %}
                                        </p>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="text-muted small">
                                            <i class="fas fa-info-circle me-1"></i>
                                            {% trans "Consultations are available during working hours. Emergency support may be available outside these hours." %}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Reviews -->
                    {% if recent_reviews %}
                        <div class="mb-4">
                            <h4 class="mb-3">
                                <i class="fas fa-comments me-2"></i>
                                {% trans "Recent Patient Reviews" %}
                            </h4>
                            {% for review in recent_reviews %}
                                <div class="card mb-3 border-0 shadow-sm">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <div class="d-flex align-items-center">
                                                <div class="user-avatar me-3">
                                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                                                         style="width: 40px; height: 40px;">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                </div>
                                                <div>
                                                    <div class="rating mb-1">
                                                        {% for i in "12345" %}
                                                            <i class="fas fa-star {% if review.overall_rating >= i|add:0 %}text-warning{% else %}text-muted{% endif %}"></i>
                                                        {% endfor %}
                                                        <span class="ms-2 fw-bold">{{ review.overall_rating }}/5</span>
                                                    </div>
                                                    <small class="text-muted">
                                                        <i class="fas fa-user me-1"></i>
                                                        {% trans "by" %} {{ review.consultation.user.first_name|default:"Anonymous Patient" }}
                                                    </small>
                                                </div>
                                            </div>
                                            <div class="text-end">
                                                <small class="text-muted">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    {{ review.created_at|date:"M d, Y" }}
                                                </small>
                                                <br>
                                                <small class="text-muted">
                                                    <i class="fas fa-clock me-1"></i>
                                                    {{ review.created_at|timesince }} {% trans "ago" %}
                                                </small>
                                            </div>
                                        </div>
                                        {% if review.feedback %}
                                            <div class="review-content">
                                                <p class="mb-2 text-dark">{{ review.feedback }}</p>
                                            </div>
                                        {% endif %}

                                        <!-- Detailed Ratings -->
                                        <div class="row mt-3 pt-3 border-top">
                                            <div class="col-md-3">
                                                <small class="text-muted d-block">{% trans "Communication" %}</small>
                                                <div class="rating-small">
                                                    {% for i in "12345" %}
                                                        <i class="fas fa-star {% if review.communication_rating >= i|add:0 %}text-warning{% else %}text-muted{% endif %} small"></i>
                                                    {% endfor %}
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <small class="text-muted d-block">{% trans "Helpfulness" %}</small>
                                                <div class="rating-small">
                                                    {% for i in "12345" %}
                                                        <i class="fas fa-star {% if review.helpfulness_rating >= i|add:0 %}text-warning{% else %}text-muted{% endif %} small"></i>
                                                    {% endfor %}
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <small class="text-muted d-block">{% trans "Professionalism" %}</small>
                                                <div class="rating-small">
                                                    {% for i in "12345" %}
                                                        <i class="fas fa-star {% if review.psychologist_rating >= i|add:0 %}text-warning{% else %}text-muted{% endif %} small"></i>
                                                    {% endfor %}
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                {% if review.would_recommend %}
                                                    <small class="text-success">
                                                        <i class="fas fa-thumbs-up me-1"></i>
                                                        {% trans "Recommends" %}
                                                    </small>
                                                {% else %}
                                                    <small class="text-muted">
                                                        <i class="fas fa-thumbs-down me-1"></i>
                                                        {% trans "Doesn't recommend" %}
                                                    </small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}

                            <!-- View All Reviews Link -->
                            {% if psychologist.total_ratings > recent_reviews|length %}
                                <div class="text-center mt-3">
                                    <a href="#" class="btn btn-outline-primary" onclick="loadAllReviews()">
                                        <i class="fas fa-eye me-2"></i>
                                        {% trans "View All" %} {{ psychologist.total_ratings }} {% trans "Reviews" %}
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    {% else %}
                        <div class="mb-4 text-center">
                            <div class="text-muted py-4">
                                <i class="fas fa-comment-slash fa-3x mb-3"></i>
                                <h5>{% trans "No Reviews Yet" %}</h5>
                                <p>{% trans "This psychologist hasn't received any reviews yet." %}</p>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Pricing Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-tag me-2"></i>{% trans "Consultation Pricing" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if psychologist.offers_free_consultation %}
                        <div class="pricing-item mb-3 p-3 bg-success bg-opacity-10 rounded">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1 text-success">{% trans "Free Consultation" %}</h6>
                                    <small class="text-muted">{% trans "Initial screening (30 min)" %}</small>
                                </div>
                                <span class="badge bg-success fs-6">{% trans "FREE" %}</span>
                            </div>
                        </div>
                    {% endif %}
                    
                    {% if psychologist.consultation_fee > 0 %}
                        <div class="pricing-item p-3 bg-light rounded">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{% trans "Regular Consultation" %}</h6>
                                    <small class="text-muted">{% trans "Standard session (60 min)" %}</small>
                                </div>
                                <span class="fw-bold text-primary fs-5">{{ psychologist.consultation_fee }} ETB</span>
                            </div>
                        </div>
                    {% endif %}
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            {% trans "All consultations are conducted in a secure, confidential environment." %}
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- Available Time Slots -->
            {% if available_slots %}
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-clock me-2"></i>{% trans "Next Available Slots" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        {% for slot in available_slots|slice:":5" %}
                            <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                                <div>
                                    <strong>{{ slot.date|date:"M d" }}</strong>
                                    <small class="text-muted d-block">{{ slot.date|date:"l" }}</small>
                                </div>
                                <span class="badge bg-info">{{ slot.start_time|time:"H:i" }}</span>
                            </div>
                        {% endfor %}
                        
                        {% if user.is_authenticated and psychologist.is_available and not user.is_staff and not user.psychologist_profile %}
                            <div class="d-grid mt-3">
                                <a href="{% url 'consultation:select_consultation_type' psychologist.pk %}"
                                   class="btn btn-info">
                                    {% trans "View All Available Times" %}
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
            
            <!-- Statistics -->
            <div class="card shadow-sm">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>{% trans "Statistics" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="stat-item">
                                <h4 class="text-primary mb-1">{{ psychologist.total_consultations }}</h4>
                                <small class="text-muted">{% trans "Consultations" %}</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="stat-item">
                                <h4 class="text-success mb-1">{{ psychologist.average_rating|floatformat:1 }}</h4>
                                <small class="text-muted">{% trans "Avg Rating" %}</small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Rating Breakdown -->
                    {% if rating_breakdown and psychologist.total_ratings > 0 %}
                        <div class="mt-4">
                            <h6 class="mb-3">
                                <i class="fas fa-chart-bar me-2"></i>
                                {% trans "Rating Breakdown" %}
                            </h6>
                            {% for rating, count in rating_breakdown.items %}
                                <div class="d-flex align-items-center mb-2">
                                    <span class="me-2 fw-bold" style="min-width: 30px;">{{ rating }}★</span>
                                    <div class="progress flex-grow-1 me-3" style="height: 12px;">
                                        {% if psychologist.total_ratings > 0 %}
                                            {% widthratio count psychologist.total_ratings 100 as percentage %}
                                            <div class="progress-bar bg-warning" style="width: {{ percentage }}%;" role="progressbar"></div>
                                        {% else %}
                                            <div class="progress-bar bg-warning" style="width: 0%;" role="progressbar"></div>
                                        {% endif %}
                                    </div>
                                    <small class="text-muted fw-bold" style="min-width: 30px;">{{ count }}</small>
                                </div>
                            {% endfor %}
                        </div>
                    {% elif psychologist.total_ratings == 0 %}
                        <div class="mt-4 text-center">
                            <div class="text-muted">
                                <i class="fas fa-star-half-alt fa-2x mb-2"></i>
                                <p class="mb-0">{% trans "No ratings yet" %}</p>
                                <small>{% trans "Be the first to rate this psychologist!" %}</small>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Set progress bar widths from data attributes
document.addEventListener('DOMContentLoaded', function() {
    const progressBars = document.querySelectorAll('.progress-bar[data-width]');
    progressBars.forEach(bar => {
        const width = bar.getAttribute('data-width');
        bar.style.width = width + '%';
    });
});
</script>
<script src="{% static 'js/consultation.js' %}"></script>
{% endblock %}
