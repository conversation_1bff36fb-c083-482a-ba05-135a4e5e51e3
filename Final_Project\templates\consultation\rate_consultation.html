{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Rate Consultation" %} - {{ consultation.psychologist.user.get_full_name }}{% endblock %}

{% block extra_css %}
<style>
.rating-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.consultation-info {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.rating-form {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.rating-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    background: #f8f9fa;
}

.rating-section h5 {
    color: #495057;
    margin-bottom: 1rem;
    font-weight: 600;
}

.rating-stars {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.rating-stars input[type="radio"] {
    display: none;
}

.rating-stars label {
    font-size: 2rem;
    color: #ddd;
    cursor: pointer;
    transition: all 0.3s ease;
}

.rating-stars label:hover,
.rating-stars label:hover ~ label,
.rating-stars input[type="radio"]:checked ~ label,
.rating-stars input[type="radio"]:checked + label {
    color: #ffc107;
}

.rating-stars label:hover {
    transform: scale(1.1);
}

.rating-description {
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 0.5rem;
}

.psychologist-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    margin-bottom: 1rem;
}

.psychologist-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #667eea;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
}

.consultation-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.detail-item {
    text-align: center;
    padding: 1rem;
    background: #e3f2fd;
    border-radius: 8px;
}

.detail-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1976d2;
    margin-bottom: 0.5rem;
}

.detail-label {
    font-size: 0.9rem;
    color: #6c757d;
}

.feedback-section {
    background: #fff3e0;
    border: 1px solid #ffcc02;
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 2rem;
}

.recommendation-section {
    background: #e8f5e8;
    border: 1px solid #4caf50;
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 1rem;
}

.btn-submit-rating {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 10px;
    color: white;
    transition: all 0.3s ease;
}

.btn-submit-rating:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

.rating-scale-info {
    background: #e3f2fd;
    border-left: 4px solid #2196f3;
    padding: 1rem;
    margin-bottom: 2rem;
    border-radius: 0 8px 8px 0;
}
</style>
{% endblock %}

{% block content %}
<div class="rating-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-star me-3"></i>
                    {% trans "Rate Your Consultation" %}
                </h1>
                <p class="mb-0 opacity-75">
                    {% trans "Your feedback helps us improve our services and helps other patients" %}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'consultation:consultation_detail' consultation.pk %}" class="btn btn-light">
                    <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Consultation" %}
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-lg-4">
            <!-- Consultation Information -->
            <div class="consultation-info">
                <h5 class="mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    {% trans "Consultation Details" %}
                </h5>
                
                <div class="psychologist-card">
                    <div class="psychologist-avatar">
                        {{ consultation.psychologist.user.first_name|first }}{{ consultation.psychologist.user.last_name|first }}
                    </div>
                    <div>
                        <h6 class="mb-1">{{ consultation.psychologist.user.get_full_name }}</h6>
                        <small class="text-muted">{{ consultation.psychologist.specialization }}</small>
                    </div>
                </div>
                
                <div class="consultation-details">
                    <div class="detail-item">
                        <div class="detail-value">{{ consultation.scheduled_date|date:"M d, Y" }}</div>
                        <div class="detail-label">{% trans "Date" %}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-value">{{ consultation.scheduled_start_time|time:"g:i A" }}</div>
                        <div class="detail-label">{% trans "Time" %}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-value">{{ consultation.consultation_type.display_name }}</div>
                        <div class="detail-label">{% trans "Type" %}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-value">
                            {% if consultation.consultation_type.price > 0 %}
                                ${{ consultation.consultation_type.price }}
                            {% else %}
                                {% trans "Free" %}
                            {% endif %}
                        </div>
                        <div class="detail-label">{% trans "Price" %}</div>
                    </div>
                </div>
            </div>
            
            <!-- Rating Scale Information -->
            <div class="rating-scale-info">
                <h6 class="mb-2">
                    <i class="fas fa-info me-2"></i>
                    {% trans "Rating Scale" %}
                </h6>
                <ul class="mb-0 small">
                    <li><strong>5 stars:</strong> {% trans "Excellent" %}</li>
                    <li><strong>4 stars:</strong> {% trans "Very Good" %}</li>
                    <li><strong>3 stars:</strong> {% trans "Good" %}</li>
                    <li><strong>2 stars:</strong> {% trans "Fair" %}</li>
                    <li><strong>1 star:</strong> {% trans "Poor" %}</li>
                </ul>
            </div>
        </div>
        
        <div class="col-lg-8">
            <!-- Rating Form -->
            <div class="rating-form">
                <form method="post">
                    {% csrf_token %}
                    
                    <h4 class="mb-4">
                        <i class="fas fa-star me-2"></i>
                        {% trans "Rate Your Experience" %}
                    </h4>
                    
                    <!-- Overall Rating -->
                    <div class="rating-section">
                        <h5>{% trans "Overall Satisfaction" %}</h5>
                        <div class="rating-stars" data-rating="overall_rating">
                            {% for i in "12345" %}
                                <input type="radio" name="overall_rating" value="{{ i }}" id="overall_{{ i }}" required>
                                <label for="overall_{{ i }}">★</label>
                            {% endfor %}
                        </div>
                        <div class="rating-description">
                            {% trans "How satisfied are you with the overall consultation experience?" %}
                        </div>
                    </div>
                    
                    <!-- Psychologist Rating -->
                    <div class="rating-section">
                        <h5>{% trans "Psychologist Performance" %}</h5>
                        <div class="rating-stars" data-rating="psychologist_rating">
                            {% for i in "12345" %}
                                <input type="radio" name="psychologist_rating" value="{{ i }}" id="psychologist_{{ i }}" required>
                                <label for="psychologist_{{ i }}">★</label>
                            {% endfor %}
                        </div>
                        <div class="rating-description">
                            {% trans "How would you rate the psychologist's professionalism and expertise?" %}
                        </div>
                    </div>
                    
                    <!-- Communication Rating -->
                    <div class="rating-section">
                        <h5>{% trans "Communication Quality" %}</h5>
                        <div class="rating-stars" data-rating="communication_rating">
                            {% for i in "12345" %}
                                <input type="radio" name="communication_rating" value="{{ i }}" id="communication_{{ i }}" required>
                                <label for="communication_{{ i }}">★</label>
                            {% endfor %}
                        </div>
                        <div class="rating-description">
                            {% trans "How clear and effective was the communication during the session?" %}
                        </div>
                    </div>
                    
                    <!-- Helpfulness Rating -->
                    <div class="rating-section">
                        <h5>{% trans "How Helpful Was the Session" %}</h5>
                        <div class="rating-stars" data-rating="helpfulness_rating">
                            {% for i in "12345" %}
                                <input type="radio" name="helpfulness_rating" value="{{ i }}" id="helpfulness_{{ i }}" required>
                                <label for="helpfulness_{{ i }}">★</label>
                            {% endfor %}
                        </div>
                        <div class="rating-description">
                            {% trans "Did the session help you with your concerns and provide valuable insights?" %}
                        </div>
                    </div>
                    
                    <!-- Platform Rating -->
                    <div class="rating-section">
                        <h5>{% trans "Platform Experience" %}</h5>
                        <div class="rating-stars" data-rating="platform_rating">
                            {% for i in "12345" %}
                                <input type="radio" name="platform_rating" value="{{ i }}" id="platform_{{ i }}" required>
                                <label for="platform_{{ i }}">★</label>
                            {% endfor %}
                        </div>
                        <div class="rating-description">
                            {% trans "How was your experience using the ECPI platform for this consultation?" %}
                        </div>
                    </div>
                    
                    <!-- Feedback Section -->
                    <div class="feedback-section">
                        <h5 class="mb-3">
                            <i class="fas fa-comment me-2"></i>
                            {% trans "Additional Feedback" %}
                        </h5>
                        <textarea name="feedback" class="form-control" rows="4" 
                                  placeholder="{% trans 'Please share any additional comments or suggestions...' %}"></textarea>
                        <small class="form-text text-muted mt-2">
                            {% trans "Your feedback is valuable and helps us improve our services." %}
                        </small>
                    </div>
                    
                    <!-- Recommendation Section -->
                    <div class="recommendation-section">
                        <div class="form-check">
                            <input type="checkbox" name="would_recommend" value="true" class="form-check-input" id="recommend" checked>
                            <label class="form-check-label" for="recommend">
                                <strong>{% trans "I would recommend this psychologist to others" %}</strong>
                            </label>
                        </div>
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-submit-rating">
                            <i class="fas fa-paper-plane me-2"></i>
                            {% trans "Submit Rating" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle star rating interactions
    const ratingGroups = document.querySelectorAll('.rating-stars');
    
    ratingGroups.forEach(group => {
        const stars = group.querySelectorAll('label');
        const inputs = group.querySelectorAll('input[type="radio"]');
        
        stars.forEach((star, index) => {
            star.addEventListener('click', function() {
                // Update visual state
                stars.forEach((s, i) => {
                    if (i <= index) {
                        s.style.color = '#ffc107';
                    } else {
                        s.style.color = '#ddd';
                    }
                });
            });
            
            star.addEventListener('mouseenter', function() {
                // Hover effect
                stars.forEach((s, i) => {
                    if (i <= index) {
                        s.style.color = '#ffc107';
                    } else {
                        s.style.color = '#ddd';
                    }
                });
            });
        });
        
        group.addEventListener('mouseleave', function() {
            // Reset to selected state
            const checkedInput = group.querySelector('input[type="radio"]:checked');
            if (checkedInput) {
                const checkedIndex = Array.from(inputs).indexOf(checkedInput);
                stars.forEach((s, i) => {
                    if (i <= checkedIndex) {
                        s.style.color = '#ffc107';
                    } else {
                        s.style.color = '#ddd';
                    }
                });
            } else {
                stars.forEach(s => s.style.color = '#ddd');
            }
        });
    });
    
    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const requiredRatings = ['overall_rating', 'psychologist_rating', 'communication_rating', 'helpfulness_rating', 'platform_rating'];
        let isValid = true;
        
        requiredRatings.forEach(rating => {
            const checked = document.querySelector(`input[name="${rating}"]:checked`);
            if (!checked) {
                isValid = false;
                const section = document.querySelector(`[data-rating="${rating}"]`).closest('.rating-section');
                section.style.borderColor = '#dc3545';
                section.style.backgroundColor = '#f8d7da';
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('{% trans "Please provide ratings for all sections before submitting." %}');
            // Scroll to first invalid section
            const firstInvalid = document.querySelector('.rating-section[style*="border-color: rgb(220, 53, 69)"]');
            if (firstInvalid) {
                firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }
    });
});
</script>
{% endblock %}
