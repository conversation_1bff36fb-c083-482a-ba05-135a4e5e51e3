{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}Account Details - {{ account_user.get_full_name|default:account_user.username }}{% endblock %}

{% block extra_css %}
<style>
.admin-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.detail-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 3rem;
    margin: 0 auto 1rem;
}

.account-type-badge {
    font-size: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    text-transform: uppercase;
}

.type-patient { background: #e3f2fd; color: #1976d2; }
.type-psychologist { background: #f3e5f5; color: #7b1fa2; }
.type-admin { background: #fff3e0; color: #f57c00; }

.status-badge {
    font-size: 0.9rem;
    padding: 0.4rem 1rem;
    border-radius: 20px;
    font-weight: 600;
}

.status-active { background: #d4edda; color: #155724; }
.status-inactive { background: #f8d7da; color: #721c24; }
.status-staff { background: #d1ecf1; color: #0c5460; }

.info-item {
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 0;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.info-value {
    color: #6c757d;
}

.consultation-item {
    border-left: 4px solid #667eea;
    background: #f8f9fa;
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 0 8px 8px 0;
}

.notification-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.notification-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}
</style>
{% endblock %}

{% block content %}
<div class="admin-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-user-circle me-3"></i>
                    Account Details
                </h1>
                <p class="mb-0 opacity-75">
                    Detailed information for {{ account_user.get_full_name|default:account_user.username }}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'custom_admin:manage_accounts' %}" class="btn btn-light">
                    <i class="fas fa-arrow-left me-2"></i>Back to Accounts
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <!-- Profile Information -->
        <div class="col-lg-4">
            <div class="detail-card text-center">
                <div class="profile-avatar">
                    {{ account_user.first_name|first|default:account_user.username|first|upper }}
                </div>
                
                <h4 class="mb-2">{{ account_user.get_full_name|default:account_user.username }}</h4>
                <p class="text-muted mb-3">@{{ account_user.username }}</p>
                
                <div class="mb-3">
                    {% if admin_role %}
                        <span class="account-type-badge type-admin">Admin</span>
                        <div class="mt-2">
                            <small class="text-muted">{{ admin_role.get_role_display }}</small>
                        </div>
                    {% elif psychologist_profile %}
                        <span class="account-type-badge type-psychologist">Psychologist</span>
                        <div class="mt-2">
                            <small class="text-muted">{{ psychologist_profile.get_approval_status_display }}</small>
                        </div>
                    {% else %}
                        <span class="account-type-badge type-patient">Patient</span>
                    {% endif %}
                </div>
                
                <div class="mb-3">
                    {% if account_user.is_active %}
                        <span class="status-badge status-active">Active Account</span>
                    {% else %}
                        <span class="status-badge status-inactive">Inactive Account</span>
                    {% endif %}
                    {% if account_user.is_staff %}
                        <div class="mt-2">
                            <span class="status-badge status-staff">Staff Member</span>
                        </div>
                    {% endif %}
                    {% if account_user.is_superuser %}
                        <div class="mt-2">
                            <span class="status-badge" style="background: #fff3cd; color: #856404;">
                                <i class="fas fa-crown me-1"></i>Superuser
                            </span>
                        </div>
                    {% endif %}
                </div>
                
                <div class="action-buttons">
                    {% if not account_user.is_superuser %}
                        <form method="post" action="{% url 'custom_admin:toggle_account_status' account_user.pk %}" 
                              onsubmit="return confirm('Are you sure you want to change this account status?')">
                            {% csrf_token %}
                            {% if account_user.is_active %}
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-pause me-2"></i>Deactivate Account
                                </button>
                            {% else %}
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-play me-2"></i>Activate Account
                                </button>
                            {% endif %}
                        </form>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Detailed Information -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="detail-card">
                <h5 class="mb-4">
                    <i class="fas fa-info-circle me-2"></i>Basic Information
                </h5>
                
                <div class="info-item">
                    <div class="info-label">Full Name</div>
                    <div class="info-value">{{ account_user.get_full_name|default:"Not provided" }}</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">Username</div>
                    <div class="info-value">{{ account_user.username }}</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">Email Address</div>
                    <div class="info-value">{{ account_user.email|default:"Not provided" }}</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">Date Joined</div>
                    <div class="info-value">{{ account_user.date_joined|date:"F d, Y g:i A" }}</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">Last Login</div>
                    <div class="info-value">
                        {% if account_user.last_login %}
                            {{ account_user.last_login|date:"F d, Y g:i A" }}
                        {% else %}
                            Never logged in
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Psychologist Information -->
            {% if psychologist_profile %}
                <div class="detail-card">
                    <h5 class="mb-4">
                        <i class="fas fa-user-md me-2"></i>Psychologist Information
                    </h5>
                    
                    <div class="info-item">
                        <div class="info-label">Specializations</div>
                        <div class="info-value">{{ psychologist_profile.specializations|default:"Not specified" }}</div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">Years of Experience</div>
                        <div class="info-value">{{ psychologist_profile.years_of_experience|default:"Not specified" }} years</div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">License Number</div>
                        <div class="info-value">{{ psychologist_profile.license_number|default:"Not provided" }}</div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">Approval Status</div>
                        <div class="info-value">
                            <span class="badge bg-{{ psychologist_profile.approval_status|yesno:'success,warning,danger' }}">
                                {{ psychologist_profile.get_approval_status_display }}
                            </span>
                        </div>
                    </div>
                    
                    {% if psychologist_profile.bio %}
                        <div class="info-item">
                            <div class="info-label">Biography</div>
                            <div class="info-value">{{ psychologist_profile.bio|truncatewords:50 }}</div>
                        </div>
                    {% endif %}
                </div>
            {% endif %}
            
            <!-- Admin Role Information -->
            {% if admin_role %}
                <div class="detail-card">
                    <h5 class="mb-4">
                        <i class="fas fa-shield-alt me-2"></i>Admin Role Information
                    </h5>
                    
                    <div class="info-item">
                        <div class="info-label">Role</div>
                        <div class="info-value">{{ admin_role.get_role_display }}</div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">Role Status</div>
                        <div class="info-value">
                            {% if admin_role.is_active %}
                                <span class="badge bg-success">Active</span>
                            {% else %}
                                <span class="badge bg-danger">Inactive</span>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-label">Role Created</div>
                        <div class="info-value">{{ admin_role.created_at|date:"F d, Y g:i A" }}</div>
                    </div>
                    
                    {% if admin_role.created_by %}
                        <div class="info-item">
                            <div class="info-label">Created By</div>
                            <div class="info-value">{{ admin_role.created_by.get_full_name|default:admin_role.created_by.username }}</div>
                        </div>
                    {% endif %}
                </div>
            {% endif %}
            
            <!-- Recent Consultations -->
            {% if consultations %}
                <div class="detail-card">
                    <h5 class="mb-4">
                        <i class="fas fa-calendar-check me-2"></i>Recent Consultations (Last 10)
                    </h5>
                    
                    {% for consultation in consultations %}
                        <div class="consultation-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">
                                        {% if psychologist_profile %}
                                            Patient: {{ consultation.user.get_full_name|default:consultation.user.username }}
                                        {% else %}
                                            Psychologist: {{ consultation.psychologist.user.get_full_name }}
                                        {% endif %}
                                    </h6>
                                    <p class="mb-1 text-muted">
                                        {{ consultation.consultation_type.display_name }}
                                    </p>
                                    <small class="text-muted">
                                        {{ consultation.scheduled_date|date:"M d, Y" }} at 
                                        {{ consultation.scheduled_start_time|time:"g:i A" }}
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-{{ consultation.status|yesno:'primary,warning,success' }}">
                                        {{ consultation.get_status_display }}
                                    </span>
                                    {% if consultation.payment_status == 'free' %}
                                        <div class="mt-1">
                                            <span class="badge bg-success">Free</span>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
            
            <!-- Recent Notifications -->
            {% if notifications %}
                <div class="detail-card">
                    <h5 class="mb-4">
                        <i class="fas fa-bell me-2"></i>Recent Notifications (Last 5)
                    </h5>
                    
                    {% for notification in notifications %}
                        <div class="notification-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ notification.title }}</h6>
                                    <p class="mb-1 text-muted">{{ notification.message|truncatewords:20 }}</p>
                                    <small class="text-muted">{{ notification.created_at|timesince }} ago</small>
                                </div>
                                <div>
                                    {% if notification.is_read %}
                                        <span class="badge bg-secondary">Read</span>
                                    {% else %}
                                        <span class="badge bg-primary">Unread</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add confirmation for sensitive actions
    const sensitiveButtons = document.querySelectorAll('[data-confirm]');
    sensitiveButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (!confirm(this.dataset.confirm)) {
                e.preventDefault();
            }
        });
    });
});
</script>
{% endblock %}
