{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}Chat Session Details - {{ chat_session.consultation.psychologist.user.get_full_name }}{% endblock %}

{% block extra_css %}
<style>
.admin-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.detail-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.info-item {
    border-bottom: 1px solid #e9ecef;
    padding: 1rem 0;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.info-value {
    color: #6c757d;
}

.message-item {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.message-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.message-patient {
    border-left: 4px solid #28a745;
    background: #f8fff9;
}

.message-psychologist {
    border-left: 4px solid #007bff;
    background: #f8f9ff;
}

.message-system {
    border-left: 4px solid #ffc107;
    background: #fffdf8;
}

.log-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    background: #f8f9fa;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-box {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 600;
}

.session-status {
    font-size: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
}

.status-active { background: #d4edda; color: #155724; }
.status-completed { background: #e2e3e5; color: #383d41; }
.status-inactive { background: #f8d7da; color: #721c24; }
</style>
{% endblock %}

{% block content %}
<div class="admin-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-comment-dots me-3"></i>
                    Chat Session Details
                </h1>
                <p class="mb-0 opacity-75">
                    Detailed view of consultation chat session
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'custom_admin:chat_sessions' %}" class="btn btn-light">
                    <i class="fas fa-arrow-left me-2"></i>Back to Sessions
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-box">
            <div class="stat-number">{{ message_stats.total_messages }}</div>
            <div class="stat-label">Total Messages</div>
        </div>
        <div class="stat-box">
            <div class="stat-number">{{ message_stats.patient_messages }}</div>
            <div class="stat-label">Patient Messages</div>
        </div>
        <div class="stat-box">
            <div class="stat-number">{{ message_stats.psychologist_messages }}</div>
            <div class="stat-label">Psychologist Messages</div>
        </div>
        <div class="stat-box">
            <div class="stat-number">{{ logs.count }}</div>
            <div class="stat-label">Activity Logs</div>
        </div>
    </div>

    <div class="row">
        <!-- Session Information -->
        <div class="col-lg-4">
            <div class="detail-card">
                <h5 class="mb-4">
                    <i class="fas fa-info-circle me-2"></i>Session Information
                </h5>
                
                <div class="info-item">
                    <div class="info-label">Psychologist</div>
                    <div class="info-value">{{ chat_session.consultation.psychologist.user.get_full_name }}</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">Patient</div>
                    <div class="info-value">{{ chat_session.consultation.user.get_full_name|default:chat_session.consultation.user.username }}</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">Consultation Type</div>
                    <div class="info-value">{{ chat_session.consultation.consultation_type.display_name }}</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">Scheduled Date</div>
                    <div class="info-value">{{ chat_session.consultation.scheduled_date|date:"F d, Y" }}</div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">Scheduled Time</div>
                    <div class="info-value">
                        {{ chat_session.consultation.scheduled_start_time|time:"g:i A" }} - 
                        {{ chat_session.consultation.scheduled_end_time|time:"g:i A" }}
                    </div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">Session Status</div>
                    <div class="info-value">
                        {% if chat_session.is_active %}
                            <span class="session-status status-active">Active</span>
                        {% elif chat_session.ended_at %}
                            <span class="session-status status-completed">Completed</span>
                        {% else %}
                            <span class="session-status status-inactive">Inactive</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="info-item">
                    <div class="info-label">Session Created</div>
                    <div class="info-value">{{ chat_session.created_at|date:"F d, Y g:i A" }}</div>
                </div>
                
                {% if chat_session.started_at %}
                    <div class="info-item">
                        <div class="info-label">Session Started</div>
                        <div class="info-value">{{ chat_session.started_at|date:"F d, Y g:i A" }}</div>
                    </div>
                {% endif %}
                
                {% if chat_session.ended_at %}
                    <div class="info-item">
                        <div class="info-label">Session Ended</div>
                        <div class="info-value">{{ chat_session.ended_at|date:"F d, Y g:i A" }}</div>
                    </div>
                {% endif %}
            </div>

            <!-- Activity Logs -->
            <div class="detail-card">
                <h5 class="mb-4">
                    <i class="fas fa-history me-2"></i>Activity Logs
                </h5>
                
                {% if logs %}
                    {% for log in logs %}
                        <div class="log-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ log.user.get_full_name|default:log.user.username }}</h6>
                                    <p class="mb-1">{{ log.get_action_display }}</p>
                                    <small class="text-muted">{{ log.timestamp|date:"M d, Y g:i A" }}</small>
                                </div>
                                <div>
                                    <span class="badge bg-secondary">{{ log.action }}</span>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">No activity logs</p>
                    </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Chat Messages -->
        <div class="col-lg-8">
            <div class="detail-card">
                <h5 class="mb-4">
                    <i class="fas fa-comments me-2"></i>Chat Messages ({{ messages.count }})
                </h5>
                
                {% if messages %}
                    <div style="max-height: 600px; overflow-y: auto;">
                        {% for message in messages %}
                            <div class="message-item message-{{ message.sender_type }}">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="d-flex align-items-center mb-2">
                                            <h6 class="mb-0 me-2">
                                                {{ message.sender.get_full_name|default:message.sender.username }}
                                            </h6>
                                            <span class="badge bg-{% if message.sender_type == 'patient' %}success{% elif message.sender_type == 'psychologist' %}primary{% else %}warning{% endif %}">
                                                {{ message.sender_type|title }}
                                            </span>
                                        </div>
                                        <p class="mb-2">{{ message.message }}</p>
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            {{ message.timestamp|date:"M d, Y g:i A" }}
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        {% if message.is_read %}
                                            <i class="fas fa-check-double text-success" title="Read"></i>
                                        {% else %}
                                            <i class="fas fa-check text-muted" title="Sent"></i>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-comment-slash fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Messages</h5>
                        <p class="text-muted">No messages have been exchanged in this chat session yet.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-scroll to bottom of messages
    const messagesContainer = document.querySelector('.detail-card div[style*="max-height"]');
    if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    // Add copy functionality for messages
    const messageItems = document.querySelectorAll('.message-item');
    messageItems.forEach(item => {
        item.addEventListener('dblclick', function() {
            const messageText = this.querySelector('p').textContent;
            navigator.clipboard.writeText(messageText).then(() => {
                // Show temporary feedback
                const originalBg = this.style.backgroundColor;
                this.style.backgroundColor = '#d4edda';
                setTimeout(() => {
                    this.style.backgroundColor = originalBg;
                }, 1000);
            });
        });
    });
});
</script>
{% endblock %}
