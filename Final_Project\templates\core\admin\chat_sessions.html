{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}Chat Sessions Monitor - HR Manager{% endblock %}

{% block extra_css %}
<style>
.admin-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.stats-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-bottom: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-bottom: 2rem;
}

.session-card {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.session-card:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.session-status {
    font-size: 0.8rem;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-weight: 600;
}

.status-active { background: #d4edda; color: #155724; }
.status-completed { background: #e2e3e5; color: #383d41; }
.status-inactive { background: #f8d7da; color: #721c24; }

.consultation-type-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-weight: 600;
    background: #e3f2fd;
    color: #1976d2;
}

.message-count {
    background: #667eea;
    color: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
}

.recent-activity {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-bottom: 2rem;
}

.activity-item {
    border-left: 4px solid #667eea;
    background: #f8f9fa;
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 0 8px 8px 0;
}

.activity-item:last-child {
    margin-bottom: 0;
}
</style>
{% endblock %}

{% block content %}
<div class="admin-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-comments me-3"></i>
                    Chat Sessions Monitor
                </h1>
                <p class="mb-0 opacity-75">
                    HR Manager - Monitor and oversee consultation chat sessions
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'custom_admin:dashboard' %}" class="btn btn-light">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Statistics Row -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stat-number">{{ total_sessions }}</div>
                <div class="stat-label">Total Sessions</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stat-number">{{ active_sessions }}</div>
                <div class="stat-label">Active Sessions</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stat-number">{{ total_messages }}</div>
                <div class="stat-label">Total Messages</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stat-number">{{ page_obj.paginator.count }}</div>
                <div class="stat-label">Filtered Results</div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Filters Section -->
            <div class="filter-section">
                <form method="get" class="row align-items-end">
                    <div class="col-md-4">
                        <label for="status" class="form-label">Session Status</label>
                        <select name="status" id="status" class="form-select">
                            <option value="all" {% if status_filter == 'all' %}selected{% endif %}>All Sessions</option>
                            <option value="active" {% if status_filter == 'active' %}selected{% endif %}>Active Only</option>
                            <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>Completed Only</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="date_from" class="form-label">From Date</label>
                        <input type="date" name="date_from" id="date_from" class="form-control" 
                               value="{{ date_from }}">
                    </div>
                    <div class="col-md-3">
                        <label for="date_to" class="form-label">To Date</label>
                        <input type="date" name="date_to" id="date_to" class="form-control" 
                               value="{{ date_to }}">
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-1"></i>Filter
                        </button>
                    </div>
                </form>
                
                {% if status_filter != 'all' or date_from or date_to %}
                    <div class="mt-3">
                        <a href="{% url 'custom_admin:chat_sessions' %}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-times me-1"></i>Clear Filters
                        </a>
                    </div>
                {% endif %}
            </div>

            <!-- Chat Sessions List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        Chat Sessions ({{ page_obj.paginator.count }} total)
                    </h5>
                </div>
                <div class="card-body">
                    {% if page_obj %}
                        {% for session in page_obj %}
                            <div class="session-card">
                                <div class="row align-items-center">
                                    <div class="col-md-4">
                                        <h6 class="mb-1">
                                            <i class="fas fa-user-md me-1"></i>
                                            {{ session.consultation.psychologist.user.get_full_name }}
                                        </h6>
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i>
                                            {{ session.consultation.user.get_full_name|default:session.consultation.user.username }}
                                        </small>
                                        <div class="mt-1">
                                            <span class="consultation-type-badge">
                                                {{ session.consultation.consultation_type.display_name }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <strong>{{ session.consultation.scheduled_date|date:"M d, Y" }}</strong><br>
                                        <small class="text-muted">{{ session.consultation.scheduled_start_time|time:"g:i A" }}</small>
                                    </div>
                                    <div class="col-md-2">
                                        {% if session.is_active %}
                                            <span class="session-status status-active">Active</span>
                                        {% elif session.ended_at %}
                                            <span class="session-status status-completed">Completed</span>
                                        {% else %}
                                            <span class="session-status status-inactive">Inactive</span>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-2 text-center">
                                        <div class="message-count">{{ session.message_count }}</div>
                                        <small class="text-muted">Messages</small>
                                    </div>
                                    <div class="col-md-2 text-end">
                                        <a href="{% url 'custom_admin:chat_session_detail' session.pk %}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i> View Details
                                        </a>
                                    </div>
                                </div>
                                
                                {% if session.started_at %}
                                    <div class="row mt-2">
                                        <div class="col-12">
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>
                                                <strong>Started:</strong> {{ session.started_at|date:"M d, Y g:i A" }}
                                                {% if session.ended_at %}
                                                    | <strong>Ended:</strong> {{ session.ended_at|date:"M d, Y g:i A" }}
                                                {% endif %}
                                            </small>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                        {% endfor %}

                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                            <nav aria-label="Chat sessions pagination" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">
                                                <i class="fas fa-angle-double-left"></i>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">
                                                <i class="fas fa-angle-left"></i>
                                            </a>
                                        </li>
                                    {% endif %}

                                    {% for num in page_obj.paginator.page_range %}
                                        {% if page_obj.number == num %}
                                            <li class="page-item active">
                                                <span class="page-link">{{ num }}</span>
                                            </li>
                                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                            <li class="page-item">
                                                <a class="page-link" href="?page={{ num }}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">{{ num }}</a>
                                            </li>
                                        {% endif %}
                                    {% endfor %}

                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">
                                                <i class="fas fa-angle-right"></i>
                                            </a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">
                                                <i class="fas fa-angle-double-right"></i>
                                            </a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Chat Sessions Found</h5>
                            <p class="text-muted">
                                {% if status_filter != 'all' or date_from or date_to %}
                                    No chat sessions match your current filters.
                                {% else %}
                                    No chat sessions have been created yet.
                                {% endif %}
                            </p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Recent Activity -->
            <div class="recent-activity">
                <h5 class="mb-3">
                    <i class="fas fa-clock me-2"></i>Recent Messages
                </h5>
                
                {% if recent_messages %}
                    {% for message in recent_messages %}
                        <div class="activity-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ message.sender.get_full_name|default:message.sender.username }}</h6>
                                    <p class="mb-1 text-muted">{{ message.message|truncatewords:10 }}</p>
                                    <small class="text-muted">
                                        {{ message.chat_session.consultation.consultation_type.display_name }}
                                    </small>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted">{{ message.timestamp|timesince }} ago</small>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">No recent messages</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh page every 30 seconds for real-time monitoring
    setInterval(function() {
        // Only refresh if no filters are applied to avoid losing user's work
        const urlParams = new URLSearchParams(window.location.search);
        if (!urlParams.has('status') && !urlParams.has('date_from') && !urlParams.has('date_to')) {
            location.reload();
        }
    }, 30000);
    
    // Add hover effects to session cards
    const sessionCards = document.querySelectorAll('.session-card');
    sessionCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.borderColor = '#667eea';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.borderColor = '#e9ecef';
        });
    });
});
</script>
{% endblock %}
