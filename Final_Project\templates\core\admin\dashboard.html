{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}Admin Dashboard - ECPI{% endblock %}

{% block extra_css %}
<style>
.admin-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #667eea;
}

.stat-label {
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

.quick-action-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
    display: block;
    margin-bottom: 1rem;
}

.quick-action-card:hover {
    border-color: #667eea;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
    color: inherit;
    text-decoration: none;
}

.quick-action-icon {
    font-size: 2.5rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.role-badge {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
}

.role-badge.hr-manager {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.recent-activity {
    background: #f8f9fa;
    border-left: 4px solid #667eea;
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 0 8px 8px 0;
}
</style>
{% endblock %}

{% block content %}
<div class="admin-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-tachometer-alt me-3"></i>
                    Admin Dashboard
                </h1>
                <p class="mb-0 opacity-75">
                    Welcome back, {{ user.get_full_name|default:user.username }}
                    <span class="role-badge {% if admin_role.is_hr_manager %}hr-manager{% endif %}">
                        {{ admin_role.get_role_display }}
                    </span>
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'custom_admin:logout' %}" class="btn btn-light">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Statistics Row -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card text-center">
                <div class="stat-number">{{ total_users }}</div>
                <div class="stat-label">Total Users</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card text-center">
                <div class="stat-number">{{ total_psychologists }}</div>
                <div class="stat-label">Psychologists</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card text-center">
                <div class="stat-number">{{ pending_approvals }}</div>
                <div class="stat-label">Pending Approvals</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card text-center">
                <div class="stat-number">{{ total_consultations }}</div>
                <div class="stat-label">Consultations</div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Quick Actions -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body p-3">
                    {% if admin_role.is_super_admin %}
                        <a href="{% url 'custom_admin:manage_accounts' %}" class="quick-action-card">
                            <div class="quick-action-icon">
                                <i class="fas fa-users-cog"></i>
                            </div>
                            <h6>Account Management</h6>
                            <small class="text-muted">Comprehensive account management</small>
                        </a>

                        <a href="{% url 'custom_admin:manage_users' %}" class="quick-action-card">
                            <div class="quick-action-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <h6>Manage Users</h6>
                            <small class="text-muted">User accounts and permissions</small>
                        </a>

                        <a href="{% url 'custom_admin:manage_psychologists' %}" class="quick-action-card">
                            <div class="quick-action-icon">
                                <i class="fas fa-user-md"></i>
                            </div>
                            <h6>Manage Psychologists</h6>
                            <small class="text-muted">Approve and manage psychologists</small>
                        </a>
                    {% endif %}

                    {% if admin_role.is_hr_manager %}
                        <a href="{% url 'custom_admin:manage_consultations' %}" class="quick-action-card">
                            <div class="quick-action-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <h6>Manage Consultations</h6>
                            <small class="text-muted">Consultation oversight</small>
                        </a>

                        <a href="{% url 'custom_admin:manage_consultation_types' %}" class="quick-action-card">
                            <div class="quick-action-icon">
                                <i class="fas fa-tags"></i>
                            </div>
                            <h6>Consultation Types</h6>
                            <small class="text-muted">Manage types and pricing</small>
                        </a>

                        <a href="{% url 'custom_admin:chat_sessions' %}" class="quick-action-card">
                            <div class="quick-action-icon">
                                <i class="fas fa-comments"></i>
                            </div>
                            <h6>Chat Sessions</h6>
                            <small class="text-muted">Monitor chat activity</small>
                        </a>

                        <a href="{% url 'custom_admin:hr_analytics' %}" class="quick-action-card">
                            <div class="quick-action-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <h6>Analytics & Reports</h6>
                            <small class="text-muted">Performance insights</small>
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>Recent Activity
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_consultations %}
                        {% for consultation in recent_consultations %}
                            <div class="recent-activity">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">
                                            New consultation booked
                                        </h6>
                                        <p class="mb-1 text-muted">
                                            {{ consultation.user.get_full_name|default:consultation.user.username }} 
                                            with {{ consultation.psychologist.user.get_full_name }}
                                        </p>
                                        <small class="text-muted">
                                            {{ consultation.created_at|timesince }} ago
                                        </small>
                                    </div>
                                    <span class="badge bg-primary">{{ consultation.get_status_display }}</span>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No Recent Activity</h6>
                            <p class="text-muted">Recent system activity will appear here.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add some interactive effects
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});
</script>
{% endblock %}
