{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}HR Analytics - HR Manager{% endblock %}

{% block extra_css %}
<style>
.admin-header {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.analytics-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.analytics-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: #17a2b8;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

.psychologist-rank {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.psychologist-rank:hover {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.rank-number {
    background: #17a2b8;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 1rem;
}

.hr-manager-badge {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
}

.status-breakdown {
    display: flex;
    justify-content: space-between;
    margin: 1rem 0;
}

.status-item {
    text-align: center;
    flex: 1;
    padding: 1rem;
    border-radius: 10px;
    margin: 0 0.5rem;
}

.status-pending { background: #fff3cd; color: #856404; }
.status-confirmed { background: #d4edda; color: #155724; }
.status-completed { background: #d1ecf1; color: #0c5460; }
.status-cancelled { background: #f8d7da; color: #721c24; }
</style>
{% endblock %}

{% block content %}
<div class="admin-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-chart-bar me-3"></i>
                    HR Analytics Dashboard
                </h1>
                <p class="mb-0 opacity-75">
                    <span class="hr-manager-badge">HR Manager</span>
                    Comprehensive Consultation Analytics & Reports
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'custom_admin:dashboard' %}" class="btn btn-light me-2">
                    <i class="fas fa-arrow-left me-2"></i>Dashboard
                </a>
                <a href="{% url 'custom_admin:manage_consultations' %}" class="btn btn-light">
                    <i class="fas fa-calendar-check me-2"></i>Consultations
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Key Metrics Row -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="analytics-card text-center">
                <div class="stat-number">{{ total_consultations }}</div>
                <div class="stat-label">Total Consultations</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="analytics-card text-center">
                <div class="stat-number">{{ monthly_consultations }}</div>
                <div class="stat-label">This Month</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="analytics-card text-center">
                <div class="stat-number">{{ growth_rate }}%</div>
                <div class="stat-label">Growth Rate</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="analytics-card text-center">
                <div class="stat-number">${{ estimated_revenue }}</div>
                <div class="stat-label">Revenue Estimate</div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Consultation Status Breakdown -->
        <div class="col-lg-8">
            <div class="analytics-card">
                <h5 class="mb-4">
                    <i class="fas fa-chart-pie me-2"></i>
                    Consultation Status Breakdown
                </h5>
                
                <div class="status-breakdown">
                    {% for status in status_stats %}
                        <div class="status-item status-{{ status.status }}">
                            <div class="h4 mb-1">{{ status.count }}</div>
                            <div class="small">{{ status.status|title }}</div>
                        </div>
                    {% empty %}
                        <div class="text-center py-4">
                            <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No consultation data available</p>
                        </div>
                    {% endfor %}
                </div>

                <!-- Payment Status -->
                <h6 class="mt-4 mb-3">Payment Status Distribution</h6>
                <div class="row">
                    {% for payment in payment_stats %}
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="card-title">{{ payment.count }}</h5>
                                    <p class="card-text">
                                        {% if payment.payment_status == 'free' %}
                                            <span class="badge bg-success">Free Consultations</span>
                                        {% elif payment.payment_status == 'completed' %}
                                            <span class="badge bg-primary">Paid Consultations</span>
                                        {% elif payment.payment_status == 'pending' %}
                                            <span class="badge bg-warning">Pending Payment</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ payment.payment_status|title }}</span>
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Top Psychologists -->
        <div class="col-lg-4">
            <div class="analytics-card">
                <h5 class="mb-4">
                    <i class="fas fa-trophy me-2"></i>
                    Top Performing Psychologists
                </h5>
                
                {% if top_psychologists %}
                    {% for psychologist in top_psychologists %}
                        <div class="psychologist-rank">
                            <div class="rank-number">{{ forloop.counter }}</div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ psychologist.user.get_full_name }}</h6>
                                <small class="text-muted">
                                    {{ psychologist.consultation_count }} consultation{{ psychologist.consultation_count|pluralize }}
                                </small>
                            </div>
                            <div class="text-end">
                                <div class="badge bg-primary">
                                    {{ psychologist.consultation_count }}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-user-md fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No psychologist data available</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Additional Analytics -->
    <div class="row">
        <div class="col-12">
            <div class="analytics-card">
                <h5 class="mb-4">
                    <i class="fas fa-calendar-alt me-2"></i>
                    Monthly Consultation Trends
                </h5>
                
                <div class="chart-container">
                    <canvas id="consultationChart"></canvas>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="text-center">
                            <h6 class="text-muted">Average per Day</h6>
                            <div class="h4">{{ avg_per_day }}</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h6 class="text-muted">Peak Day</h6>
                            <div class="h4">Monday</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h6 class="text-muted">Completion Rate</h6>
                            <div class="h4">{{ completion_rate }}%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Sample chart data - in a real implementation, this would come from the backend
    const ctx = document.getElementById('consultationChart').getContext('2d');
    const consultationChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'Consultations',
                data: [12, 19, 15, 25, 22, 30, 28, 35, 32, 40, 38, 45],
                borderColor: '#17a2b8',
                backgroundColor: 'rgba(23, 162, 184, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                }
            }
        }
    });
});
</script>
{% endblock %}
