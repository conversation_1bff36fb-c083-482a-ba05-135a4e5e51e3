{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}Account Management - Admin Panel{% endblock %}

{% block extra_css %}
<style>
.admin-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.account-card {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.account-card:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.account-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.2rem;
}

.account-type-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-weight: 600;
    text-transform: uppercase;
}

.type-patient { background: #e3f2fd; color: #1976d2; }
.type-psychologist { background: #f3e5f5; color: #7b1fa2; }
.type-admin { background: #fff3e0; color: #f57c00; }

.status-badge {
    font-size: 0.8rem;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-weight: 600;
}

.status-active { background: #d4edda; color: #155724; }
.status-inactive { background: #f8d7da; color: #721c24; }
.status-staff { background: #d1ecf1; color: #0c5460; }

.stats-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-bottom: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-section {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-bottom: 2rem;
}

.account-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
}
</style>
{% endblock %}

{% block content %}
<div class="admin-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-users-cog me-3"></i>
                    Account Management
                </h1>
                <p class="mb-0 opacity-75">
                    Super Admin - Comprehensive User Account Management
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'custom_admin:dashboard' %}" class="btn btn-light">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Statistics Row -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stat-number">{{ total_accounts }}</div>
                <div class="stat-label">Total Accounts</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stat-number">{{ patient_accounts }}</div>
                <div class="stat-label">Patients</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stat-number">{{ psychologist_accounts }}</div>
                <div class="stat-label">Psychologists</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stat-number">{{ admin_accounts }}</div>
                <div class="stat-label">Admins</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stat-number">{{ active_accounts }}</div>
                <div class="stat-label">Active</div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stat-number">{{ recent_registrations }}</div>
                <div class="stat-label">New (7 days)</div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="filter-section">
        <form method="get" class="row align-items-end">
            <div class="col-md-3">
                <label for="type" class="form-label">Account Type</label>
                <select name="type" id="type" class="form-select">
                    <option value="all" {% if account_type == 'all' %}selected{% endif %}>All Accounts</option>
                    <option value="patients" {% if account_type == 'patients' %}selected{% endif %}>Patients Only</option>
                    <option value="psychologists" {% if account_type == 'psychologists' %}selected{% endif %}>Psychologists Only</option>
                    <option value="admins" {% if account_type == 'admins' %}selected{% endif %}>Admins Only</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select name="status" id="status" class="form-select">
                    <option value="all" {% if status_filter == 'all' %}selected{% endif %}>All Status</option>
                    <option value="active" {% if status_filter == 'active' %}selected{% endif %}>Active Only</option>
                    <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>Inactive Only</option>
                    <option value="staff" {% if status_filter == 'staff' %}selected{% endif %}>Staff Only</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="search" class="form-label">Search</label>
                <input type="text" name="search" id="search" class="form-control" 
                       placeholder="Search by name, username, or email..." value="{{ search_query }}">
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search me-1"></i>Filter
                </button>
            </div>
        </form>
        
        {% if search_query or account_type != 'all' or status_filter != 'all' %}
            <div class="mt-3">
                <a href="{% url 'custom_admin:manage_accounts' %}" class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-times me-1"></i>Clear Filters
                </a>
            </div>
        {% endif %}
    </div>

    <!-- Accounts List -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                User Accounts ({{ page_obj.paginator.count }} total)
            </h5>
        </div>
        <div class="card-body">
            {% if page_obj %}
                {% for user in page_obj %}
                    <div class="account-card">
                        <div class="row align-items-center">
                            <div class="col-md-1">
                                <div class="account-avatar">
                                    {{ user.first_name|first|default:user.username|first|upper }}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <h6 class="mb-1">
                                    {{ user.get_full_name|default:user.username }}
                                    {% if user.is_superuser %}
                                        <i class="fas fa-crown text-warning ms-1" title="Superuser"></i>
                                    {% endif %}
                                </h6>
                                <small class="text-muted">@{{ user.username }}</small>
                                <div class="mt-1">
                                    <small class="text-muted">{{ user.email|default:"No email" }}</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                {% if user.admin_role %}
                                    <span class="account-type-badge type-admin">Admin</span>
                                    <div class="mt-1">
                                        <small class="text-muted">{{ user.admin_role.get_role_display }}</small>
                                    </div>
                                {% elif user.psychologist_profile %}
                                    <span class="account-type-badge type-psychologist">Psychologist</span>
                                    <div class="mt-1">
                                        <small class="text-muted">{{ user.psychologist_profile.get_approval_status_display }}</small>
                                    </div>
                                {% else %}
                                    <span class="account-type-badge type-patient">Patient</span>
                                {% endif %}
                            </div>
                            <div class="col-md-2">
                                {% if user.is_active %}
                                    <span class="status-badge status-active">Active</span>
                                {% else %}
                                    <span class="status-badge status-inactive">Inactive</span>
                                {% endif %}
                                {% if user.is_staff %}
                                    <div class="mt-1">
                                        <span class="status-badge status-staff">Staff</span>
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">
                                    <strong>Joined:</strong><br>
                                    {{ user.date_joined|date:"M d, Y" }}
                                </small>
                                <div class="mt-1">
                                    <small class="text-muted">
                                        <strong>Last Login:</strong><br>
                                        {{ user.last_login|date:"M d, Y"|default:"Never" }}
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-2 text-end">
                                <div class="account-actions">
                                    <a href="{% url 'custom_admin:account_details' user.pk %}" 
                                       class="btn btn-sm btn-outline-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if not user.is_superuser %}
                                        <form method="post" action="{% url 'custom_admin:toggle_account_status' user.pk %}" 
                                              style="display: inline;" onsubmit="return confirm('Are you sure?')">
                                            {% csrf_token %}
                                            {% if user.is_active %}
                                                <button type="submit" class="btn btn-sm btn-outline-warning" title="Deactivate">
                                                    <i class="fas fa-pause"></i>
                                                </button>
                                            {% else %}
                                                <button type="submit" class="btn btn-sm btn-outline-success" title="Activate">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                            {% endif %}
                                        </form>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="Accounts pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if account_type != 'all' %}&type={{ account_type }}{% endif %}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}">
                                        <i class="fas fa-angle-double-left"></i>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if account_type != 'all' %}&type={{ account_type }}{% endif %}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}">
                                        <i class="fas fa-angle-left"></i>
                                    </a>
                                </li>
                            {% endif %}

                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if account_type != 'all' %}&type={{ account_type }}{% endif %}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if account_type != 'all' %}&type={{ account_type }}{% endif %}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}">
                                        <i class="fas fa-angle-right"></i>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if account_type != 'all' %}&type={{ account_type }}{% endif %}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}">
                                        <i class="fas fa-angle-double-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Accounts Found</h5>
                    <p class="text-muted">
                        {% if search_query or account_type != 'all' or status_filter != 'all' %}
                            No accounts match your current filters.
                        {% else %}
                            No user accounts found in the system.
                        {% endif %}
                    </p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus search input if no search query
    const searchInput = document.getElementById('search');
    if (searchInput && !searchInput.value) {
        searchInput.focus();
    }
    
    // Add hover effects to account cards
    const accountCards = document.querySelectorAll('.account-card');
    accountCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.borderColor = '#667eea';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.borderColor = '#e9ecef';
        });
    });
});
</script>
{% endblock %}
