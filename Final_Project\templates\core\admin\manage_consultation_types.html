{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}Manage Consultation Types - HR Manager{% endblock %}

{% block extra_css %}
<style>
.admin-header {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.consultation-type-card {
    border: 1px solid #e9ecef;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    background: white;
}

.consultation-type-card:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.type-badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-weight: 600;
}

.type-free { background: #d4edda; color: #155724; }
.type-paid { background: #d1ecf1; color: #0c5460; }
.type-inactive { background: #f8d7da; color: #721c24; }

.hr-manager-badge {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
}

.price-display {
    font-size: 1.5rem;
    font-weight: 700;
    color: #17a2b8;
}

.duration-display {
    background: #f8f9fa;
    padding: 0.5rem 1rem;
    border-radius: 10px;
    font-weight: 600;
    color: #495057;
}

.add-type-card {
    border: 2px dashed #17a2b8;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    background: rgba(23, 162, 184, 0.05);
}

.add-type-card:hover {
    background: rgba(23, 162, 184, 0.1);
    border-color: #138496;
}
</style>
{% endblock %}

{% block content %}
<div class="admin-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-tags me-3"></i>
                    Manage Consultation Types
                </h1>
                <p class="mb-0 opacity-75">
                    <span class="hr-manager-badge">HR Manager</span>
                    Create and manage consultation types and pricing
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'custom_admin:dashboard' %}" class="btn btn-light me-2">
                    <i class="fas fa-arrow-left me-2"></i>Dashboard
                </a>
                <button class="btn btn-light" data-bs-toggle="modal" data-bs-target="#addTypeModal">
                    <i class="fas fa-plus me-2"></i>Add Type
                </button>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Add New Type Card -->
    <div class="add-type-card" data-bs-toggle="modal" data-bs-target="#addTypeModal" style="cursor: pointer;">
        <div class="mb-3">
            <i class="fas fa-plus-circle fa-3x text-info"></i>
        </div>
        <h5 class="text-info mb-2">Add New Consultation Type</h5>
        <p class="text-muted mb-0">Create a new consultation type with custom pricing and duration</p>
    </div>

    <!-- Consultation Types List -->
    <div class="row">
        {% for consultation_type in consultation_types %}
            <div class="col-lg-6">
                <div class="consultation-type-card">
                    <div class="row align-items-start">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center mb-2">
                                <h5 class="mb-0 me-3">{{ consultation_type.display_name }}</h5>
                                {% if consultation_type.is_free %}
                                    <span class="type-badge type-free">FREE</span>
                                {% elif consultation_type.is_active %}
                                    <span class="type-badge type-paid">PAID</span>
                                {% else %}
                                    <span class="type-badge type-inactive">INACTIVE</span>
                                {% endif %}
                            </div>
                            
                            {% if consultation_type.description %}
                                <p class="text-muted mb-3">{{ consultation_type.description }}</p>
                            {% endif %}
                            
                            <div class="row">
                                <div class="col-6">
                                    <div class="mb-2">
                                        <strong>Duration:</strong><br>
                                        <span class="duration-display">
                                            {{ consultation_type.duration_minutes }} min
                                        </span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="mb-2">
                                        <strong>Price:</strong><br>
                                        {% if consultation_type.is_free %}
                                            <span class="price-display text-success">FREE</span>
                                        {% else %}
                                            <span class="price-display">${{ consultation_type.price|default:0 }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    Created: {{ consultation_type.created_at|date:"M d, Y" }}
                                </small>
                            </div>
                        </div>
                        
                        <div class="col-md-4 text-end">
                            <div class="dropdown">
                                <button class="btn btn-outline-primary dropdown-toggle" 
                                        type="button" 
                                        data-bs-toggle="dropdown">
                                    <i class="fas fa-cog"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" 
                                           onclick="editType({{ consultation_type.pk }})">
                                        <i class="fas fa-edit me-2"></i>Edit Type
                                    </a></li>
                                    {% if consultation_type.is_active %}
                                        <li><a class="dropdown-item text-warning" href="#" 
                                               onclick="toggleStatus({{ consultation_type.pk }}, false)">
                                            <i class="fas fa-pause me-2"></i>Deactivate
                                        </a></li>
                                    {% else %}
                                        <li><a class="dropdown-item text-success" href="#" 
                                               onclick="toggleStatus({{ consultation_type.pk }}, true)">
                                            <i class="fas fa-play me-2"></i>Activate
                                        </a></li>
                                    {% endif %}
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="#" 
                                           onclick="deleteType({{ consultation_type.pk }})">
                                        <i class="fas fa-trash me-2"></i>Delete
                                    </a></li>
                                </ul>
                            </div>
                            
                            <div class="mt-3 text-center">
                                <div class="small text-muted">Usage Stats</div>
                                <div class="h6 mb-0">
                                    {{ consultation_type.consultations.count }} booking{{ consultation_type.consultations.count|pluralize }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% empty %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Consultation Types</h5>
                    <p class="text-muted">Create your first consultation type to get started.</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTypeModal">
                        <i class="fas fa-plus me-2"></i>Add Consultation Type
                    </button>
                </div>
            </div>
        {% endfor %}
    </div>
</div>

<!-- Add/Edit Type Modal -->
<div class="modal fade" id="addTypeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>Add New Consultation Type
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="typeForm" method="post">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="display_name" class="form-label">Display Name</label>
                                <input type="text" class="form-control" name="display_name" id="display_name" 
                                       placeholder="e.g., Individual Therapy" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="duration_minutes" class="form-label">Duration (minutes)</label>
                                <input type="number" class="form-control" name="duration_minutes" id="duration_minutes" 
                                       placeholder="60" min="15" max="180" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" name="description" id="description" rows="3"
                                  placeholder="Brief description of this consultation type..."></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="is_free" id="is_free" 
                                           onchange="togglePriceField()">
                                    <label class="form-check-label" for="is_free">
                                        Free Consultation
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3" id="priceField">
                                <label for="price" class="form-label">Price ($)</label>
                                <input type="number" class="form-control" name="price" id="price" 
                                       placeholder="50.00" min="0" step="0.01">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" name="is_active" id="is_active" checked>
                            <label class="form-check-label" for="is_active">
                                Active (available for booking)
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Save Consultation Type
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function togglePriceField() {
    const isFree = document.getElementById('is_free').checked;
    const priceField = document.getElementById('priceField');
    const priceInput = document.getElementById('price');
    
    if (isFree) {
        priceField.style.display = 'none';
        priceInput.required = false;
        priceInput.value = '';
    } else {
        priceField.style.display = 'block';
        priceInput.required = true;
    }
}

function editType(typeId) {
    // In a real implementation, this would populate the modal with existing data
    document.getElementById('addTypeModal').querySelector('.modal-title').innerHTML = 
        '<i class="fas fa-edit me-2"></i>Edit Consultation Type';
    new bootstrap.Modal(document.getElementById('addTypeModal')).show();
}

function toggleStatus(typeId, activate) {
    const action = activate ? 'activate' : 'deactivate';
    if (confirm(`Are you sure you want to ${action} this consultation type?`)) {
        // Here you would make an AJAX call to update the status
        window.location.reload();
    }
}

function deleteType(typeId) {
    if (confirm('Are you sure you want to delete this consultation type? This action cannot be undone.')) {
        // Here you would make an AJAX call to delete the type
        window.location.reload();
    }
}

// Initialize price field visibility
document.addEventListener('DOMContentLoaded', function() {
    togglePriceField();
});
</script>
{% endblock %}
