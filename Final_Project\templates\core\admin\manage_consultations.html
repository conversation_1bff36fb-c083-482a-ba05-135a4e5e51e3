{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}Manage Consultations - HR Manager{% endblock %}

{% block extra_css %}
<style>
.admin-header {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.consultation-card {
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.consultation-card:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.status-badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-weight: 600;
}

.status-pending { background: #fff3cd; color: #856404; }
.status-confirmed { background: #d4edda; color: #155724; }
.status-completed { background: #d1ecf1; color: #0c5460; }
.status-cancelled { background: #f8d7da; color: #721c24; }
.status-in_progress { background: #e2e3e5; color: #383d41; }

.payment-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
    font-weight: 600;
}

.payment-free { background: #d4edda; color: #155724; }
.payment-pending { background: #fff3cd; color: #856404; }
.payment-completed { background: #d1ecf1; color: #0c5460; }
.payment-failed { background: #f8d7da; color: #721c24; }

.filter-tabs {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-bottom: 2rem;
}

.hr-manager-badge {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
}
</style>
{% endblock %}

{% block content %}
<div class="admin-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-calendar-check me-3"></i>
                    Manage Consultations
                </h1>
                <p class="mb-0 opacity-75">
                    <span class="hr-manager-badge">HR Manager</span>
                    Consultation Oversight & Management
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'custom_admin:dashboard' %}" class="btn btn-light me-2">
                    <i class="fas fa-arrow-left me-2"></i>Dashboard
                </a>
                <a href="{% url 'custom_admin:hr_analytics' %}" class="btn btn-light">
                    <i class="fas fa-chart-bar me-2"></i>Analytics
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Filter Tabs -->
    <div class="filter-tabs">
        <div class="row align-items-center">
            <div class="col-md-10">
                <div class="btn-group" role="group">
                    <a href="?status=all" class="btn {% if status_filter == 'all' %}btn-primary{% else %}btn-outline-primary{% endif %}">
                        All Consultations
                    </a>
                    <a href="?status=pending" class="btn {% if status_filter == 'pending' %}btn-warning{% else %}btn-outline-warning{% endif %}">
                        Pending
                    </a>
                    <a href="?status=confirmed" class="btn {% if status_filter == 'confirmed' %}btn-success{% else %}btn-outline-success{% endif %}">
                        Confirmed
                    </a>
                    <a href="?status=in_progress" class="btn {% if status_filter == 'in_progress' %}btn-info{% else %}btn-outline-info{% endif %}">
                        In Progress
                    </a>
                    <a href="?status=completed" class="btn {% if status_filter == 'completed' %}btn-secondary{% else %}btn-outline-secondary{% endif %}">
                        Completed
                    </a>
                    <a href="?status=cancelled" class="btn {% if status_filter == 'cancelled' %}btn-danger{% else %}btn-outline-danger{% endif %}">
                        Cancelled
                    </a>
                </div>
            </div>
            <div class="col-md-2 text-end">
                <small class="text-muted">
                    {{ page_obj.paginator.count }} consultation{{ page_obj.paginator.count|pluralize }}
                </small>
            </div>
        </div>
    </div>

    <!-- Consultations List -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                Consultation Records
            </h5>
        </div>
        <div class="card-body">
            {% if page_obj %}
                {% for consultation in page_obj %}
                    <div class="consultation-card">
                        <div class="row align-items-start">
                            <div class="col-md-3">
                                <h6 class="mb-1">
                                    <i class="fas fa-user me-1"></i>
                                    {{ consultation.user.get_full_name|default:consultation.user.username }}
                                </h6>
                                <small class="text-muted">Patient</small>
                                <div class="mt-2">
                                    <h6 class="mb-1">
                                        <i class="fas fa-user-md me-1"></i>
                                        {{ consultation.psychologist.user.get_full_name }}
                                    </h6>
                                    <small class="text-muted">Psychologist</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-2">
                                    <strong>Type:</strong><br>
                                    <small>{{ consultation.consultation_type.display_name }}</small>
                                </div>
                                <div class="mb-2">
                                    <strong>Scheduled:</strong><br>
                                    <small>
                                        {{ consultation.scheduled_date|date:"M d, Y" }}<br>
                                        {{ consultation.scheduled_start_time|time:"g:i A" }} - 
                                        {{ consultation.scheduled_end_time|time:"g:i A" }}
                                    </small>
                                </div>
                                <div class="mb-2">
                                    <strong>Booked:</strong><br>
                                    <small>{{ consultation.created_at|date:"M d, Y g:i A" }}</small>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center mb-3">
                                    <span class="status-badge status-{{ consultation.status }}">
                                        {{ consultation.get_status_display }}
                                    </span>
                                </div>
                                <div class="text-center">
                                    <span class="payment-badge payment-{{ consultation.payment_status }}">
                                        {% if consultation.payment_status == 'free' %}
                                            FREE
                                        {% else %}
                                            ${{ consultation.price|default:0 }}
                                        {% endif %}
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                {% if consultation.user_notes %}
                                    <div class="mb-2">
                                        <strong>Notes:</strong><br>
                                        <small>{{ consultation.user_notes|truncatechars:100 }}</small>
                                    </div>
                                {% endif %}
                                {% if consultation.psychologist_notes %}
                                    <div class="mb-2">
                                        <strong>Psychologist Notes:</strong><br>
                                        <small>{{ consultation.psychologist_notes|truncatechars:100 }}</small>
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-1 text-end">
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-primary dropdown-toggle" 
                                            type="button" 
                                            data-bs-toggle="dropdown">
                                        <i class="fas fa-cog"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#">
                                            <i class="fas fa-eye me-2"></i>View Details
                                        </a></li>
                                        {% if consultation.status == 'pending' %}
                                            <li><a class="dropdown-item text-success" href="#" 
                                                   onclick="updateStatus({{ consultation.pk }}, 'confirmed')">
                                                <i class="fas fa-check me-2"></i>Confirm
                                            </a></li>
                                        {% endif %}
                                        {% if consultation.status in 'pending,confirmed' %}
                                            <li><a class="dropdown-item text-warning" href="#">
                                                <i class="fas fa-edit me-2"></i>Reschedule
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger" href="#" 
                                                   onclick="updateStatus({{ consultation.pk }}, 'cancelled')">
                                                <i class="fas fa-times me-2"></i>Cancel
                                            </a></li>
                                        {% endif %}
                                        {% if consultation.status == 'completed' %}
                                            <li><a class="dropdown-item" href="#">
                                                <i class="fas fa-star me-2"></i>View Rating
                                            </a></li>
                                        {% endif %}
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Progress Indicator -->
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="progress" style="height: 6px;">
                                    {% if consultation.status == 'pending' %}
                                        <div class="progress-bar bg-warning" style="width: 25%"></div>
                                    {% elif consultation.status == 'confirmed' %}
                                        <div class="progress-bar bg-info" style="width: 50%"></div>
                                    {% elif consultation.status == 'in_progress' %}
                                        <div class="progress-bar bg-primary" style="width: 75%"></div>
                                    {% elif consultation.status == 'completed' %}
                                        <div class="progress-bar bg-success" style="width: 100%"></div>
                                    {% elif consultation.status == 'cancelled' %}
                                        <div class="progress-bar bg-danger" style="width: 100%"></div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="Consultations pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}">
                                        <i class="fas fa-angle-double-left"></i>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}">
                                        <i class="fas fa-angle-left"></i>
                                    </a>
                                </li>
                            {% endif %}

                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}">
                                        <i class="fas fa-angle-right"></i>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}">
                                        <i class="fas fa-angle-double-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Consultations Found</h5>
                    <p class="text-muted">
                        {% if status_filter != 'all' %}
                            No consultations with {{ status_filter }} status.
                        {% else %}
                            No consultations in the system.
                        {% endif %}
                    </p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updateStatus(consultationId, newStatus) {
    if (confirm(`Are you sure you want to ${newStatus} this consultation?`)) {
        // Here you would make an AJAX call to update the status
        // For now, we'll just reload the page
        window.location.reload();
    }
}
</script>
{% endblock %}
