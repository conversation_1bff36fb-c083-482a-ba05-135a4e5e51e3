{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}Manage Psychologists - Admin Panel{% endblock %}

{% block extra_css %}
<style>
.admin-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.psychologist-card {
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.psychologist-card:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.status-badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-weight: 600;
}

.status-pending { background: #fff3cd; color: #856404; }
.status-approved { background: #d4edda; color: #155724; }
.status-rejected { background: #f8d7da; color: #721c24; }

.filter-tabs {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-bottom: 2rem;
}

.psychologist-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #667eea;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.2rem;
}
</style>
{% endblock %}

{% block content %}
<div class="admin-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-user-md me-3"></i>
                    Manage Psychologists
                </h1>
                <p class="mb-0 opacity-75">
                    Super Admin - Psychologist Approval Management
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'custom_admin:dashboard' %}" class="btn btn-light">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Filter Tabs -->
    <div class="filter-tabs">
        <div class="row align-items-center">
            <div class="col-md-8">
                <div class="btn-group" role="group">
                    <a href="?status=all" class="btn {% if status_filter == 'all' %}btn-primary{% else %}btn-outline-primary{% endif %}">
                        All Psychologists
                    </a>
                    <a href="?status=pending" class="btn {% if status_filter == 'pending' %}btn-warning{% else %}btn-outline-warning{% endif %}">
                        Pending Approval
                    </a>
                    <a href="?status=approved" class="btn {% if status_filter == 'approved' %}btn-success{% else %}btn-outline-success{% endif %}">
                        Approved
                    </a>
                    <a href="?status=rejected" class="btn {% if status_filter == 'rejected' %}btn-danger{% else %}btn-outline-danger{% endif %}">
                        Rejected
                    </a>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <small class="text-muted">
                    Showing {{ page_obj.paginator.count }} psychologist{{ page_obj.paginator.count|pluralize }}
                </small>
            </div>
        </div>
    </div>

    <!-- Psychologists List -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                Psychologist Applications
            </h5>
        </div>
        <div class="card-body">
            {% if page_obj %}
                {% for psychologist in page_obj %}
                    <div class="psychologist-card">
                        <div class="row align-items-start">
                            <div class="col-md-1">
                                <div class="psychologist-avatar">
                                    {{ psychologist.user.first_name|first|default:psychologist.user.username|first|upper }}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <h6 class="mb-1">{{ psychologist.user.get_full_name }}</h6>
                                <small class="text-muted">@{{ psychologist.user.username }}</small>
                                <div class="mt-1">
                                    <small class="text-muted">
                                        <i class="fas fa-envelope me-1"></i>{{ psychologist.user.email }}
                                    </small>
                                </div>
                                <div class="mt-1">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>Applied: {{ psychologist.created_at|date:"M d, Y" }}
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-2">
                                    <strong>Specializations:</strong><br>
                                    <small>{{ psychologist.specializations|truncatechars:100 }}</small>
                                </div>
                                <div class="mb-2">
                                    <strong>Experience:</strong><br>
                                    <small>{{ psychologist.years_of_experience }} years</small>
                                </div>
                                {% if psychologist.license_number %}
                                    <div class="mb-2">
                                        <strong>License:</strong><br>
                                        <small>{{ psychologist.license_number }}</small>
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-2">
                                <div class="text-center mb-3">
                                    <span class="status-badge status-{{ psychologist.approval_status }}">
                                        {{ psychologist.get_approval_status_display }}
                                    </span>
                                </div>
                                {% if psychologist.approval_status == 'approved' and psychologist.approved_by %}
                                    <small class="text-muted text-center d-block">
                                        Approved by:<br>{{ psychologist.approved_by.get_full_name }}
                                    </small>
                                {% endif %}
                            </div>
                            <div class="col-md-1 text-end">
                                {% if psychologist.approval_status == 'pending' %}
                                    <div class="btn-group-vertical">
                                        <a href="{% url 'custom_admin:approve_psychologist' psychologist.pk %}"
                                           class="btn btn-sm btn-success mb-1">
                                            <i class="fas fa-check"></i>
                                        </a>
                                        <button class="btn btn-sm btn-danger" 
                                                onclick="rejectPsychologist({{ psychologist.pk }})">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                {% else %}
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-primary dropdown-toggle" 
                                                type="button" 
                                                data-bs-toggle="dropdown">
                                            <i class="fas fa-cog"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#">
                                                <i class="fas fa-eye me-2"></i>View Profile
                                            </a></li>
                                            <li><a class="dropdown-item" href="#">
                                                <i class="fas fa-chart-bar me-2"></i>View Stats
                                            </a></li>
                                            {% if psychologist.approval_status == 'approved' %}
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-warning" href="#">
                                                    <i class="fas fa-pause me-2"></i>Suspend
                                                </a></li>
                                            {% endif %}
                                        </ul>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Additional Details -->
                        {% if psychologist.bio %}
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="alert alert-light py-2 mb-0">
                                        <strong>Bio:</strong> {{ psychologist.bio|truncatechars:200 }}
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                        
                        {% if psychologist.approval_status == 'rejected' and psychologist.rejection_reason %}
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="alert alert-danger py-2 mb-0">
                                        <strong>Rejection Reason:</strong> {{ psychologist.rejection_reason }}
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                {% endfor %}

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="Psychologists pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}">
                                        <i class="fas fa-angle-double-left"></i>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}">
                                        <i class="fas fa-angle-left"></i>
                                    </a>
                                </li>
                            {% endif %}

                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}">
                                        <i class="fas fa-angle-right"></i>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if status_filter != 'all' %}&status={{ status_filter }}{% endif %}">
                                        <i class="fas fa-angle-double-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-user-md fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Psychologists Found</h5>
                    <p class="text-muted">
                        {% if status_filter != 'all' %}
                            No psychologists with {{ status_filter }} status.
                        {% else %}
                            No psychologist applications in the system.
                        {% endif %}
                    </p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Rejection Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reject Psychologist Application</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="rejectForm" method="post">
                {% csrf_token %}
                <div class="modal-body">
                    <input type="hidden" name="action" value="reject">
                    <div class="mb-3">
                        <label for="reason" class="form-label">Rejection Reason</label>
                        <textarea class="form-control" name="reason" id="reason" rows="3" required
                                  placeholder="Please provide a reason for rejection..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Reject Application</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function rejectPsychologist(psychologistId) {
    const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
    const form = document.getElementById('rejectForm');
    form.action = `/admin/psychologists/${psychologistId}/approve/`;
    modal.show();
}
</script>
{% endblock %}
