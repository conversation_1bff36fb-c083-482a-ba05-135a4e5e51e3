{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}Manage Users - Admin Panel{% endblock %}

{% block extra_css %}
<style>
.admin-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.user-card {
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.user-card:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #667eea;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.user-status {
    font-size: 0.8rem;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-weight: 600;
}

.status-active { background: #d4edda; color: #155724; }
.status-inactive { background: #f8d7da; color: #721c24; }
.status-staff { background: #d1ecf1; color: #0c5460; }

.search-box {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-bottom: 2rem;
}
</style>
{% endblock %}

{% block content %}
<div class="admin-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-users me-3"></i>
                    Manage Users
                </h1>
                <p class="mb-0 opacity-75">
                    Super Admin - User Account Management
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'custom_admin:dashboard' %}" class="btn btn-light">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Search Box -->
    <div class="search-box">
        <form method="get" class="row align-items-center">
            <div class="col-md-8">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" 
                           name="search" 
                           class="form-control" 
                           placeholder="Search users by username, name, or email..."
                           value="{{ search_query }}">
                </div>
            </div>
            <div class="col-md-4">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>Search
                </button>
                {% if search_query %}
                    <a href="{% url 'custom_admin:manage_users' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>Clear
                    </a>
                {% endif %}
            </div>
        </form>
    </div>

    <!-- Users List -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                Users ({{ page_obj.paginator.count }} total)
            </h5>
        </div>
        <div class="card-body">
            {% if page_obj %}
                {% for user in page_obj %}
                    <div class="user-card">
                        <div class="row align-items-center">
                            <div class="col-md-1">
                                <div class="user-avatar">
                                    {{ user.first_name|first|default:user.username|first|upper }}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <h6 class="mb-1">{{ user.get_full_name|default:user.username }}</h6>
                                <small class="text-muted">@{{ user.username }}</small>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-1">{{ user.email|default:"No email" }}</div>
                                <small class="text-muted">
                                    Joined: {{ user.date_joined|date:"M d, Y" }}
                                </small>
                            </div>
                            <div class="col-md-2">
                                {% if user.is_superuser %}
                                    <span class="user-status status-staff">Superuser</span>
                                {% elif user.is_staff %}
                                    <span class="user-status status-staff">Staff</span>
                                {% elif user.is_active %}
                                    <span class="user-status status-active">Active</span>
                                {% else %}
                                    <span class="user-status status-inactive">Inactive</span>
                                {% endif %}
                            </div>
                            <div class="col-md-2">
                                <small class="text-muted">
                                    Last login:<br>
                                    {{ user.last_login|date:"M d, Y"|default:"Never" }}
                                </small>
                            </div>
                            <div class="col-md-1 text-end">
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-primary dropdown-toggle" 
                                            type="button" 
                                            data-bs-toggle="dropdown">
                                        <i class="fas fa-cog"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#">
                                            <i class="fas fa-eye me-2"></i>View Details
                                        </a></li>
                                        <li><a class="dropdown-item" href="#">
                                            <i class="fas fa-edit me-2"></i>Edit User
                                        </a></li>
                                        {% if not user.is_superuser %}
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger" href="#">
                                                <i class="fas fa-ban me-2"></i>Deactivate
                                            </a></li>
                                        {% endif %}
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Additional Info -->
                        {% if user.psychologist_profile %}
                            <div class="row mt-2">
                                <div class="col-12">
                                    <div class="alert alert-info py-2 mb-0">
                                        <i class="fas fa-user-md me-2"></i>
                                        <strong>Psychologist:</strong> 
                                        {{ user.psychologist_profile.specializations|truncatechars:50 }}
                                        <span class="badge bg-{{ user.psychologist_profile.approval_status|yesno:'success,warning,danger' }} ms-2">
                                            {{ user.psychologist_profile.get_approval_status_display }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                {% endfor %}

                <!-- Pagination -->
                {% if page_obj.has_other_pages %}
                    <nav aria-label="Users pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}">
                                        <i class="fas fa-angle-double-left"></i>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">
                                        <i class="fas fa-angle-left"></i>
                                    </a>
                                </li>
                            {% endif %}

                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">
                                        <i class="fas fa-angle-right"></i>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}">
                                        <i class="fas fa-angle-double-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Users Found</h5>
                    <p class="text-muted">
                        {% if search_query %}
                            No users match your search criteria.
                        {% else %}
                            No users are registered in the system.
                        {% endif %}
                    </p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus search input
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput && !searchInput.value) {
        searchInput.focus();
    }
});
</script>
{% endblock %}
