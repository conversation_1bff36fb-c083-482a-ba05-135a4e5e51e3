{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Notifications" %}{% endblock %}

{% block extra_css %}
<style>
.notifications-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.notification-item {
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    background: white;
    position: relative;
}

.notification-item:hover {
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.notification-item.unread {
    border-left: 4px solid #007bff;
    background: #f8f9ff;
}

.notification-item.unread::before {
    content: '';
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 8px;
    height: 8px;
    background: #007bff;
    border-radius: 50%;
}

.notification-type-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-weight: 600;
}

.type-info { background: #e3f2fd; color: #1976d2; }
.type-success { background: #e8f5e8; color: #2e7d32; }
.type-warning { background: #fff3e0; color: #f57c00; }
.type-error { background: #ffebee; color: #d32f2f; }
.type-consultation { background: #f3e5f5; color: #7b1fa2; }
.type-message { background: #e0f2f1; color: #00695c; }
.type-meeting { background: #fce4ec; color: #c2185b; }
.type-system { background: #f5f5f5; color: #424242; }

.notification-actions {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.filter-tabs {
    background: white;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.filter-tabs .nav-link {
    border: none;
    border-radius: 8px;
    color: #6c757d;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    margin-right: 0.5rem;
}

.filter-tabs .nav-link.active {
    background: #667eea;
    color: white;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.stats-row {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.stat-item {
    text-align: center;
    padding: 1rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 600;
}
</style>
{% endblock %}

{% block content %}
<div class="notifications-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-bell me-3"></i>
                    {% trans "Notifications & Messages" %}
                </h1>
                <p class="mb-0 opacity-75">
                    {% trans "Stay updated with your consultations, messages, and system updates" %}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'core:dashboard' %}" class="btn btn-light">
                    <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Dashboard" %}
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Statistics Row -->
    <div class="stats-row">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ notifications.count }}</div>
                    <div class="stat-label">{% trans "Total Notifications" %}</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ unread_count }}</div>
                    <div class="stat-label">{% trans "Unread" %}</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ message_count }}</div>
                    <div class="stat-label">{% trans "Messages" %}</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ consultation_count }}</div>
                    <div class="stat-label">{% trans "Consultations" %}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Tabs -->
    <div class="filter-tabs">
        <ul class="nav nav-pills" id="notificationTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="all-tab" data-bs-toggle="pill" data-bs-target="#all" type="button" role="tab">
                    {% trans "All" %} ({{ notifications.count }})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="unread-tab" data-bs-toggle="pill" data-bs-target="#unread" type="button" role="tab">
                    {% trans "Unread" %} ({{ unread_count }})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="messages-tab" data-bs-toggle="pill" data-bs-target="#messages" type="button" role="tab">
                    {% trans "Messages" %} ({{ message_count }})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="consultations-tab" data-bs-toggle="pill" data-bs-target="#consultations" type="button" role="tab">
                    {% trans "Consultations" %} ({{ consultation_count }})
                </button>
            </li>
        </ul>
    </div>

    <!-- Notification Content -->
    <div class="tab-content" id="notificationTabsContent">
        <!-- All Notifications -->
        <div class="tab-pane fade show active" id="all" role="tabpanel">
            {% if notifications %}
                {% for notification in notifications %}
                    <div class="notification-item {% if not notification.is_read %}unread{% endif %}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-2">
                                    <h6 class="mb-0 me-3">{{ notification.title }}</h6>
                                    <span class="notification-type-badge type-{{ notification.notification_type }}">
                                        {{ notification.get_notification_type_display }}
                                    </span>
                                </div>
                                <p class="mb-2 text-muted">{{ notification.message }}</p>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    {{ notification.created_at|timesince }} {% trans "ago" %}
                                </small>
                            </div>
                            <div class="text-end">
                                {% if not notification.is_read %}
                                    <span class="badge bg-primary">{% trans "New" %}</span>
                                {% endif %}
                            </div>
                        </div>
                        
                        {% if notification.url %}
                            <div class="notification-actions">
                                <a href="{% url 'core:mark_notification_read' notification.id %}" 
                                   class="btn btn-sm btn-primary">
                                    <i class="fas fa-external-link-alt me-1"></i>{% trans "View" %}
                                </a>
                            </div>
                        {% endif %}
                    </div>
                {% endfor %}
            {% else %}
                <div class="empty-state">
                    <i class="fas fa-bell-slash"></i>
                    <h5>{% trans "No Notifications" %}</h5>
                    <p>{% trans "You don't have any notifications yet." %}</p>
                </div>
            {% endif %}
        </div>

        <!-- Other tabs would be filtered versions of the same content -->
        <!-- For brevity, I'll implement the filtering via JavaScript -->
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab filtering functionality
    const tabs = document.querySelectorAll('#notificationTabs button[data-bs-toggle="pill"]');
    const notifications = document.querySelectorAll('.notification-item');
    
    tabs.forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(e) {
            const target = e.target.id;
            
            notifications.forEach(notification => {
                const isUnread = notification.classList.contains('unread');
                const type = notification.querySelector('.notification-type-badge').classList;
                const isMessage = type.contains('type-message');
                const isConsultation = type.contains('type-consultation');
                
                let show = true;
                
                switch(target) {
                    case 'unread-tab':
                        show = isUnread;
                        break;
                    case 'messages-tab':
                        show = isMessage;
                        break;
                    case 'consultations-tab':
                        show = isConsultation;
                        break;
                    default:
                        show = true;
                }
                
                notification.style.display = show ? 'block' : 'none';
            });
        });
    });
    
    // Auto-refresh notifications every 30 seconds
    setInterval(function() {
        // Check for new notifications
        fetch('/api/notifications/check/')
            .then(response => response.json())
            .then(data => {
                if (data.new_count > 0) {
                    // Show notification about new notifications
                    if (window.notificationManager) {
                        window.notificationManager.info(
                            '{% trans "New Notifications" %}',
                            `{% trans "You have" %} ${data.new_count} {% trans "new notifications" %}`
                        );
                    }
                }
            })
            .catch(error => console.error('Error checking notifications:', error));
    }, 30000);
});
</script>
{% endblock %}
