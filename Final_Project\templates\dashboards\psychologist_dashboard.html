{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load math_filters %}

{% block title %}{% trans "Psychologist Dashboard" %} - ECPI{% endblock %}

{% block extra_css %}
<link href="{% static 'css/dashboard.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="welcome-card psychologist-welcome">
                <div class="d-flex align-items-center">
                    <img src="{% if user.profile.avatar %}{{ user.profile.avatar.url }}{% else %}{% static 'images/default-avatar.png' %}{% endif %}" 
                         alt="Avatar" class="rounded-circle me-3" width="60" height="60">
                    <div class="flex-grow-1">
                        <h2 class="mb-1">{% trans "Welcome" %}, Dr. {{ user.get_full_name|default:user.username }}!</h2>
                        <p class="text-muted mb-0">{% trans "Your professional dashboard for managing consultations and clients" %}</p>
                    </div>
                    <div class="text-end">
                        <div class="rating-display">
                            <div class="rating-stars">
                                {% for i in "12345" %}
                                    <i class="fas fa-star {% if average_rating >= i|add:0 %}text-warning{% else %}text-muted{% endif %}"></i>
                                {% endfor %}
                            </div>
                            <small class="text-muted">{{ average_rating }}/5 ({{ total_ratings }} {% trans "reviews" %})</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card bg-primary text-white">
                <div class="stat-icon">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ today_consultations.count }}</h3>
                    <p>{% trans "Today's Sessions" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card bg-success text-white">
                <div class="stat-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ completed_this_month }}</h3>
                    <p>{% trans "This Month" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card bg-info text-white">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ upcoming_consultations.count }}</h3>
                    <p>{% trans "Upcoming" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card bg-warning text-white">
                <div class="stat-icon">
                    <i class="fas fa-file-medical"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ pending_summaries }}</h3>
                    <p>{% trans "Pending Summaries" %}</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="row">
        <!-- Left Column -->
        <div class="col-lg-8">
            <!-- Today's Schedule -->
            <div class="dashboard-card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-day me-2"></i>{% trans "Today's Schedule" %}
                    </h5>
                    <span class="badge bg-primary">{{ today_consultations.count }} {% trans "sessions" %}</span>
                </div>
                <div class="card-body">
                    {% if today_consultations %}
                        {% for consultation in today_consultations %}
                            <div class="consultation-item today-item">
                                <div class="row align-items-center">
                                    <div class="col-md-2">
                                        <div class="time-badge">
                                            {{ consultation.scheduled_start_time|time:"H:i" }}
                                        </div>
                                    </div>
                                    <div class="col-md-5">
                                        <h6 class="mb-1">{{ consultation.consultation_type.display_name }}</h6>
                                        <p class="text-muted mb-0">
                                            <i class="fas fa-user me-1"></i>
                                            {{ consultation.user.get_full_name|default:consultation.user.username }}
                                        </p>
                                    </div>
                                    <div class="col-md-2">
                                        <span class="badge bg-{{ consultation.status }} status-badge">
                                            {{ consultation.get_status_display }}
                                        </span>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'consultation:consultation_detail' consultation.pk %}"
                                               class="btn btn-outline-primary">
                                                <i class="fas fa-eye me-1"></i>{% trans "View" %}
                                            </a>
                                            {% if consultation.status == 'confirmed' %}
                                                <a href="{% url 'consultation:chat_room' consultation.pk %}"
                                                   class="btn btn-success">
                                                    <i class="fas fa-comments me-1"></i>{% trans "Start Chat" %}
                                                </a>
                                            {% elif consultation.status == 'in_progress' %}
                                                <a href="{% url 'consultation:chat_room' consultation.pk %}"
                                                   class="btn btn-warning">
                                                    <i class="fas fa-comments me-1"></i>{% trans "Continue" %}
                                                </a>
                                            {% elif consultation.status == 'completed' and not consultation.summary %}
                                                <a href="{% url 'consultation:consultation_summary' consultation.pk %}"
                                                   class="btn btn-info">
                                                    <i class="fas fa-clipboard me-1"></i>{% trans "Add Summary" %}
                                                </a>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-calendar-check fa-3x text-muted mb-3"></i>
                            <h6>{% trans "No sessions scheduled for today" %}</h6>
                            <p class="text-muted">{% trans "Enjoy your free time or check your upcoming schedule." %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Upcoming Consultations -->
            <div class="dashboard-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>{% trans "Upcoming Consultations" %}
                    </h5>
                    <a href="{% url 'consultation:psychologist_consultations' %}" class="btn btn-sm btn-outline-primary">
                        {% trans "View All" %}
                    </a>
                </div>
                <div class="card-body">
                    {% if upcoming_consultations %}
                        {% for consultation in upcoming_consultations|slice:":5" %}
                            <div class="consultation-item">
                                <div class="row align-items-center">
                                    <div class="col-md-2">
                                        <div class="date-badge">
                                            <div class="day">{{ consultation.scheduled_date|date:"d" }}</div>
                                            <div class="month">{{ consultation.scheduled_date|date:"M" }}</div>
                                        </div>
                                    </div>
                                    <div class="col-md-5">
                                        <h6 class="mb-1">{{ consultation.consultation_type.display_name }}</h6>
                                        <p class="text-muted mb-1">
                                            <i class="fas fa-user me-1"></i>
                                            {{ consultation.user.get_full_name|default:consultation.user.username }}
                                        </p>
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            {{ consultation.scheduled_start_time|time:"H:i" }}
                                        </small>
                                    </div>
                                    <div class="col-md-2">
                                        <span class="badge bg-{{ consultation.status }} status-badge">
                                            {{ consultation.get_status_display }}
                                        </span>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="{% url 'consultation:consultation_detail' consultation.pk %}" 
                                           class="btn btn-sm btn-primary">
                                            {% trans "View" %}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-calendar-plus fa-3x text-muted mb-3"></i>
                            <h6>{% trans "No upcoming consultations" %}</h6>
                            <p class="text-muted">{% trans "Your schedule is clear for the coming days." %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Right Column -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="dashboard-card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>{% trans "Quick Actions" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'consultation:psychologist_consultations' %}" class="btn btn-primary">
                            <i class="fas fa-calendar me-2"></i>{% trans "My Consultations" %}
                        </a>
                        <a href="{% url 'consultation:manage_schedule' %}" class="btn btn-outline-primary">
                            <i class="fas fa-clock me-2"></i>{% trans "Manage Schedule" %}
                        </a>
                        <a href="{% url 'resources:upload_resource' %}" class="btn btn-outline-success">
                            <i class="fas fa-plus me-2"></i>{% trans "Add Resource" %}
                        </a>
                        <a href="{% url 'accounts:profile' %}" class="btn btn-outline-info">
                            <i class="fas fa-user me-2"></i>{% trans "View Profile" %}
                        </a>
                        <a href="{% url 'accounts:edit_profile' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-user-edit me-2"></i>{% trans "Edit Profile" %}
                        </a>
                    </div>

                    <!-- Active Sessions Alert -->
                    {% if active_sessions %}
                        <div class="alert alert-warning mt-3 mb-0">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <div class="flex-grow-1">
                                    <strong>{% trans "Active Sessions" %}</strong><br>
                                    <small>{% trans "You have" %} {{ active_sessions.count }} {% trans "session(s) in progress" %}</small>
                                </div>
                                <a href="{% url 'consultation:psychologist_consultations' %}?status=in_progress"
                                   class="btn btn-sm btn-warning">
                                    {% trans "View" %}
                                </a>
                            </div>
                        </div>
                    {% endif %}

                    <!-- Pending Summaries Alert -->
                    {% if pending_summaries > 0 %}
                        <div class="alert alert-info mt-3 mb-0">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-clipboard-list me-2"></i>
                                <div class="flex-grow-1">
                                    <strong>{% trans "Pending Summaries" %}</strong><br>
                                    <small>{{ pending_summaries }} {% trans "consultation(s) need summary" %}</small>
                                </div>
                                <a href="{% url 'consultation:psychologist_consultations' %}?status=completed&no_summary=1"
                                   class="btn btn-sm btn-info">
                                    {% trans "Complete" %}
                                </a>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Weekly Overview -->
            <div class="dashboard-card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>{% trans "This Week" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="weekly-chart">
                        {% for day in weekly_consultations %}
                            <div class="day-item">
                                <div class="day-name">{{ day.day_name|slice:":3" }}</div>
                                <div class="day-bar">
                                    <div class="bar-fill" style="height: {% if day.count > 0 %}{{ day.count|mul:20 }}px{% else %}2px{% endif %};"></div>
                                </div>
                                <div class="day-count">{{ day.count }}</div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            
            <!-- Recent Feedback -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2"></i>{% trans "Recent Feedback" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_ratings %}
                        {% for rating in recent_ratings %}
                            <div class="feedback-item">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div class="rating-stars">
                                        {% for i in "12345" %}
                                            <i class="fas fa-star {% if rating.overall_rating >= i|add:0 %}text-warning{% else %}text-muted{% endif %} small"></i>
                                        {% endfor %}
                                    </div>
                                    <small class="text-muted">{{ rating.created_at|date:"M d" }}</small>
                                </div>
                                {% if rating.feedback %}
                                    <p class="small mb-1">{{ rating.feedback|truncatewords:15 }}</p>
                                {% endif %}
                                <small class="text-muted">
                                    {% trans "by" %} {{ rating.consultation.user.first_name|default:"Anonymous" }}
                                </small>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-star fa-2x text-muted mb-2"></i>
                            <p class="text-muted">{% trans "No recent feedback" %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/dashboard.js' %}"></script>
{% endblock %}
