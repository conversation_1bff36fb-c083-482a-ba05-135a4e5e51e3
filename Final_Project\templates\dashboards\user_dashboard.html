{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "My Dashboard" %} - ECPI{% endblock %}

{% block extra_css %}
<link href="{% static 'css/dashboard.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="welcome-card">
                <div class="d-flex align-items-center">
                    <img src="{% if user.profile.avatar %}{{ user.profile.avatar.url }}{% else %}{% static 'images/default-avatar.png' %}{% endif %}" 
                         alt="Avatar" class="rounded-circle me-3" width="60" height="60">
                    <div>
                        <h2 class="mb-1">{% trans "Welcome back" %}, {{ user.get_full_name|default:user.username }}!</h2>
                        <p class="text-muted mb-0">{% trans "Here's your mental health journey overview" %}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card bg-primary text-white">
                <div class="stat-icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ total_consultations }}</h3>
                    <p>{% trans "Total Consultations" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card bg-success text-white">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ completed_consultations }}</h3>
                    <p>{% trans "Completed Sessions" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card bg-info text-white">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ upcoming_consultations_count }}</h3>
                    <p>{% trans "Upcoming Sessions" %}</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card bg-warning text-white">
                <div class="stat-icon">
                    <i class="fas fa-bell"></i>
                </div>
                <div class="stat-content">
                    <h3>{{ unread_notifications.count }}</h3>
                    <p>{% trans "Notifications" %}</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="row">
        <!-- Left Column -->
        <div class="col-lg-8">
            <!-- Upcoming Consultations -->
            <div class="dashboard-card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i>{% trans "Upcoming Consultations" %}
                    </h5>
                    <a href="{% url 'consultation:my_consultations' %}" class="btn btn-sm btn-outline-primary">
                        {% trans "View All" %}
                    </a>
                </div>
                <div class="card-body">
                    {% if upcoming_consultations %}
                        {% for consultation in upcoming_consultations %}
                            <div class="consultation-item">
                                <div class="row align-items-center">
                                    <div class="col-md-2">
                                        <div class="date-badge">
                                            <div class="day">{{ consultation.scheduled_date|date:"d" }}</div>
                                            <div class="month">{{ consultation.scheduled_date|date:"M" }}</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="mb-1">{{ consultation.consultation_type.display_name }}</h6>
                                        <p class="text-muted mb-1">
                                            <i class="fas fa-user-md me-1"></i>
                                            Dr. {{ consultation.psychologist.user.get_full_name }}
                                        </p>
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            {{ consultation.scheduled_start_time|time:"H:i" }}
                                        </small>
                                    </div>
                                    <div class="col-md-2">
                                        <span class="badge bg-{{ consultation.status }} status-badge">
                                            {{ consultation.get_status_display }}
                                        </span>
                                    </div>
                                    <div class="col-md-2">
                                        <a href="{% url 'consultation:consultation_detail' consultation.pk %}" 
                                           class="btn btn-sm btn-primary">
                                            {% trans "View" %}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-calendar-plus fa-3x text-muted mb-3"></i>
                            <h6>{% trans "No upcoming consultations" %}</h6>
                            <p class="text-muted">{% trans "Book a consultation to get started with your mental health journey." %}</p>
                            <a href="{% url 'consultation:psychologist_list' %}" class="btn btn-primary">
                                {% trans "Find a Psychologist" %}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Recent Activity -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>{% trans "Recent Activity" %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_consultations %}
                        <div class="activity-timeline">
                            {% for consultation in recent_consultations %}
                                <div class="activity-item">
                                    <div class="activity-icon bg-{{ consultation.status }}">
                                        <i class="fas fa-calendar"></i>
                                    </div>
                                    <div class="activity-content">
                                        <h6 class="mb-1">{{ consultation.consultation_type.display_name }}</h6>
                                        <p class="text-muted mb-1">
                                            {% trans "with" %} Dr. {{ consultation.psychologist.user.get_full_name }}
                                        </p>
                                        <small class="text-muted">
                                            {{ consultation.created_at|date:"M d, Y H:i" }}
                                        </small>
                                    </div>
                                    <div class="activity-status">
                                        <span class="badge bg-{{ consultation.status }}">
                                            {{ consultation.get_status_display }}
                                        </span>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-history fa-2x text-muted mb-2"></i>
                            <p class="text-muted">{% trans "No recent activity" %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Right Column -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="dashboard-card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>{% trans "Quick Actions" %}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'consultation:psychologist_list' %}" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>{% trans "Find Psychologist" %}
                        </a>
                        <a href="{% url 'consultation:my_consultations' %}" class="btn btn-outline-primary">
                            <i class="fas fa-calendar me-2"></i>{% trans "My Consultations" %}
                        </a>
                        <a href="{% url 'resources:resource_list' %}" class="btn btn-outline-success">
                            <i class="fas fa-book me-2"></i>{% trans "Browse Resources" %}
                        </a>
                        <a href="{% url 'discussions:discussion_list' %}" class="btn btn-outline-info">
                            <i class="fas fa-comments me-2"></i>{% trans "Join Discussions" %}
                        </a>
                        <a href="{% url 'accounts:profile' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-user me-2"></i>{% trans "View Profile" %}
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Notifications -->
            <div class="dashboard-card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-bell me-2"></i>{% trans "Notifications" %}
                    </h5>
                    <a href="{% url 'core:notifications' %}" class="btn btn-sm btn-outline-primary">
                        {% trans "View All" %}
                    </a>
                </div>
                <div class="card-body">
                    {% if unread_notifications %}
                        {% for notification in unread_notifications %}
                            <div class="notification-item">
                                <div class="notification-icon">
                                    <i class="fas fa-{{ notification.notification_type }}"></i>
                                </div>
                                <div class="notification-content">
                                    <h6 class="mb-1">{{ notification.title }}</h6>
                                    <p class="text-muted small mb-1">{{ notification.message|truncatewords:10 }}</p>
                                    <small class="text-muted">{{ notification.created_at|timesince }} {% trans "ago" %}</small>
                                </div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-bell-slash fa-2x text-muted mb-2"></i>
                            <p class="text-muted">{% trans "No new notifications" %}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Favorite Psychologists -->
            {% if favorite_psychologists %}
                <div class="dashboard-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-heart me-2"></i>{% trans "Favorite Psychologists" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        {% for psychologist in favorite_psychologists %}
                            <div class="psychologist-item">
                                <div class="d-flex align-items-center">
                                    <img src="{% if psychologist.user.profile.avatar %}{{ psychologist.user.profile.avatar.url }}{% else %}{% static 'images/default-avatar.png' %}{% endif %}" 
                                         alt="Dr. {{ psychologist.user.get_full_name }}" 
                                         class="rounded-circle me-3" width="40" height="40">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">Dr. {{ psychologist.user.get_full_name }}</h6>
                                        <small class="text-muted">{{ psychologist.years_of_experience }} {% trans "years exp." %}</small>
                                    </div>
                                    <a href="{% url 'consultation:psychologist_detail' psychologist.pk %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        {% trans "View" %}
                                    </a>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/dashboard.js' %}"></script>
{% endblock %}
