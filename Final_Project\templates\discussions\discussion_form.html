{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Start New Discussion" %} - ECPI{% endblock %}

{% block extra_css %}
<style>
.discussion-form-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.form-card {
    border: none;
    box-shadow: 0 2px 20px rgba(0,0,0,0.1);
    border-radius: 15px;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
}

.btn-secondary {
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
}

.guidelines-card {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.guideline-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}

.guideline-item:last-child {
    margin-bottom: 0;
}

.guideline-icon {
    width: 24px;
    height: 24px;
    background: #667eea;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    font-size: 0.75rem;
}

.character-counter {
    font-size: 0.85rem;
    color: #6c757d;
    text-align: right;
}

.character-counter.warning {
    color: #ffc107;
}

.character-counter.danger {
    color: #dc3545;
}
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <!-- Header -->
    <div class="discussion-form-header text-center">
        <h1 class="display-6 fw-bold mb-3">{% trans "Start New Discussion" %}</h1>
        <p class="lead mb-0">
            {% trans "Share your thoughts, ask questions, and connect with our community" %}
        </p>
    </div>

    <div class="row">
        <!-- Form -->
        <div class="col-lg-8">
            <div class="card form-card">
                <div class="card-body p-4">
                    <form method="post" id="discussion-form">
                        {% csrf_token %}
                        
                        <!-- Title -->
                        <div class="form-group">
                            <label for="id_title" class="form-label fw-bold">
                                <i class="fas fa-heading me-2"></i>{% trans "Discussion Title" %} *
                            </label>
                            <input type="text" 
                                   class="form-control" 
                                   id="id_title" 
                                   name="title" 
                                   maxlength="200"
                                   placeholder="{% trans 'Enter a clear, descriptive title for your discussion...' %}"
                                   required>
                            <div class="character-counter mt-1">
                                <span id="title-count">0</span>/200 {% trans "characters" %}
                            </div>
                        </div>

                        <!-- Category -->
                        <div class="form-group">
                            <label for="id_category" class="form-label fw-bold">
                                <i class="fas fa-folder me-2"></i>{% trans "Category" %} *
                            </label>
                            <select class="form-select" id="id_category" name="category" required>
                                <option value="">{% trans "Select a category..." %}</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}">
                                    {{ category.name }} - {{ category.description|truncatewords:8 }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Content -->
                        <div class="form-group">
                            <label for="id_content" class="form-label fw-bold">
                                <i class="fas fa-edit me-2"></i>{% trans "Discussion Content" %} *
                            </label>
                            <textarea class="form-control" 
                                      id="id_content" 
                                      name="content" 
                                      rows="8"
                                      maxlength="5000"
                                      placeholder="{% trans 'Share your thoughts, experiences, or questions. Be respectful and constructive...' %}"
                                      required></textarea>
                            <div class="character-counter mt-1">
                                <span id="content-count">0</span>/5000 {% trans "characters" %}
                            </div>
                        </div>

                        <!-- Tags -->
                        <div class="form-group">
                            <label for="id_tags" class="form-label fw-bold">
                                <i class="fas fa-tags me-2"></i>{% trans "Tags" %} {% trans "(Optional)" %}
                            </label>
                            <input type="text" 
                                   class="form-control" 
                                   id="id_tags" 
                                   name="tags"
                                   placeholder="{% trans 'anxiety, depression, therapy, support (comma-separated)' %}">
                            <small class="form-text text-muted">
                                {% trans "Add relevant tags to help others find your discussion" %}
                            </small>
                        </div>

                        <!-- Privacy Options -->
                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="id_is_anonymous" name="is_anonymous">
                                <label class="form-check-label" for="id_is_anonymous">
                                    <i class="fas fa-user-secret me-2"></i>{% trans "Post anonymously" %}
                                </label>
                                <small class="form-text text-muted d-block">
                                    {% trans "Your username will be hidden from other users" %}
                                </small>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="form-group mb-0">
                            <div class="d-flex justify-content-between">
                                <a href="{% url 'discussions:discussion_list' %}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>{% trans "Cancel" %}
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-2"></i>{% trans "Start Discussion" %}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Guidelines -->
        <div class="col-lg-4">
            <div class="guidelines-card">
                <h5 class="fw-bold mb-3">
                    <i class="fas fa-lightbulb me-2 text-warning"></i>{% trans "Community Guidelines" %}
                </h5>
                
                <div class="guideline-item">
                    <div class="guideline-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <div>
                        <strong>{% trans "Be Respectful" %}</strong><br>
                        <small class="text-muted">{% trans "Treat all community members with kindness and respect" %}</small>
                    </div>
                </div>

                <div class="guideline-item">
                    <div class="guideline-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div>
                        <strong>{% trans "Stay Safe" %}</strong><br>
                        <small class="text-muted">{% trans "Don't share personal information or contact details" %}</small>
                    </div>
                </div>

                <div class="guideline-item">
                    <div class="guideline-icon">
                        <i class="fas fa-hands-helping"></i>
                    </div>
                    <div>
                        <strong>{% trans "Be Supportive" %}</strong><br>
                        <small class="text-muted">{% trans "Offer encouragement and constructive advice" %}</small>
                    </div>
                </div>

                <div class="guideline-item">
                    <div class="guideline-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div>
                        <strong>{% trans "Search First" %}</strong><br>
                        <small class="text-muted">{% trans "Check if your topic has been discussed before" %}</small>
                    </div>
                </div>

                <div class="guideline-item">
                    <div class="guideline-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div>
                        <strong>{% trans "Crisis Support" %}</strong><br>
                        <small class="text-muted">{% trans "For emergencies, contact local crisis hotlines immediately" %}</small>
                    </div>
                </div>
            </div>

            <!-- Quick Tips -->
            <div class="guidelines-card">
                <h6 class="fw-bold mb-3">
                    <i class="fas fa-tips me-2 text-info"></i>{% trans "Quick Tips" %}
                </h6>
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>{% trans "Use clear, descriptive titles" %}</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>{% trans "Choose the most relevant category" %}</small>
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>{% trans "Add helpful tags for discoverability" %}</small>
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-check text-success me-2"></i>
                        <small>{% trans "Proofread before posting" %}</small>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const titleInput = document.getElementById('id_title');
    const contentTextarea = document.getElementById('id_content');
    const titleCounter = document.getElementById('title-count');
    const contentCounter = document.getElementById('content-count');

    // Character counters
    function updateCounter(input, counter, maxLength) {
        const currentLength = input.value.length;
        counter.textContent = currentLength;
        
        // Update counter color based on usage
        counter.parentElement.classList.remove('warning', 'danger');
        if (currentLength > maxLength * 0.8) {
            counter.parentElement.classList.add('warning');
        }
        if (currentLength > maxLength * 0.95) {
            counter.parentElement.classList.add('danger');
        }
    }

    titleInput.addEventListener('input', function() {
        updateCounter(this, titleCounter, 200);
    });

    contentTextarea.addEventListener('input', function() {
        updateCounter(this, contentCounter, 5000);
    });

    // Form validation
    document.getElementById('discussion-form').addEventListener('submit', function(e) {
        const title = titleInput.value.trim();
        const content = contentTextarea.value.trim();
        const category = document.getElementById('id_category').value;

        if (!title || !content || !category) {
            e.preventDefault();
            alert('{% trans "Please fill in all required fields." %}');
            return false;
        }

        if (title.length < 10) {
            e.preventDefault();
            alert('{% trans "Title must be at least 10 characters long." %}');
            titleInput.focus();
            return false;
        }

        if (content.length < 20) {
            e.preventDefault();
            alert('{% trans "Content must be at least 20 characters long." %}');
            contentTextarea.focus();
            return false;
        }
    });
});
</script>
{% endblock %}
