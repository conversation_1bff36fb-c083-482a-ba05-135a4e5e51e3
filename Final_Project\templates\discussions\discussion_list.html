{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Discussions" %} - ECPI{% endblock %}

{% block extra_css %}
<style>
.discussion-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 3rem 2rem;
    margin-bottom: 2rem;
}

.category-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 15px;
}

.discussion-item {
    border: none;
    border-bottom: 1px solid #e9ecef;
    padding: 1.5rem 0;
    transition: all 0.3s ease;
}

.discussion-item:hover {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin: 0 -1rem;
}

.discussion-meta {
    font-size: 0.85rem;
    color: #6c757d;
}

.vote-score {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 0.5rem;
    text-align: center;
    min-width: 60px;
}

.stats-badge {
    background: #e9ecef;
    color: #495057;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    margin: 0.125rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2">{% trans "Community Discussions" %}</h1>
                {% if user.is_authenticated %}
                    <a href="{% url 'discussions:create_discussion' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>{% trans "Start Discussion" %}
                    </a>
                {% endif %}
            </div>
            
            <!-- Categories Sidebar -->
            <div class="col-lg-3 mb-4">
                <div class="card category-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>{% trans "Categories" %}
                        </h5>
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="{% url 'discussions:discussion_list' %}"
                           class="list-group-item list-group-item-action {% if not current_category %}active{% endif %}">
                            <i class="fas fa-comments me-2"></i>{% trans "All Discussions" %}
                            <span class="badge bg-primary rounded-pill float-end">{{ total_discussions }}</span>
                        </a>
                        {% for category in categories %}
                        <a href="{% url 'discussions:category_discussions' category.pk %}"
                           class="list-group-item list-group-item-action {% if current_category.pk == category.pk %}active{% endif %}">
                            <i class="{{ category.icon|default:'fas fa-folder' }} me-2" style="color: {{ category.color }};"></i>
                            {{ category.name }}
                            <span class="badge bg-secondary rounded-pill float-end">{{ category.discussions.count }}</span>
                        </a>
                        {% endfor %}
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="card category-card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>{% trans "Community Stats" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="h4 text-primary mb-0">{{ total_discussions }}</div>
                                <small class="text-muted">{% trans "Discussions" %}</small>
                            </div>
                            <div class="col-6">
                                <div class="h4 text-success mb-0">{{ total_replies }}</div>
                                <small class="text-muted">{% trans "Replies" %}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Discussions List -->
            <div class="col-lg-9">
                <!-- Regular Discussions -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-comments me-2"></i>{% trans "Recent Discussions" %}
                        </h6>
                    </div>
                    <div class="card-body">
                        {% for discussion in discussions %}
                        <div class="discussion-item">
                            <div class="row align-items-center">
                                <div class="col-auto">
                                    <div class="vote-score {% if discussion.vote_score > 0 %}positive{% elif discussion.vote_score < 0 %}negative{% endif %}">
                                        <div class="fw-bold">{{ discussion.vote_score }}</div>
                                        <small>{% trans "votes" %}</small>
                                    </div>
                                </div>
                                <div class="col">
                                    <h6 class="mb-2">
                                        <a href="{{ discussion.get_absolute_url }}" class="text-decoration-none">
                                            {{ discussion.title }}
                                        </a>
                                    </h6>
                                    <div class="discussion-meta">
                                        {% trans "by" %}
                                        {% if discussion.is_anonymous %}
                                            {% trans "Anonymous" %}
                                        {% else %}
                                            <strong>{{ discussion.author.get_full_name|default:discussion.author.username }}</strong>
                                        {% endif %}
                                        • {{ discussion.created_at|timesince }} {% trans "ago" %}
                                        • <span class="badge bg-primary">{{ discussion.category.name }}</span>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="text-end">
                                        <span class="stats-badge">
                                            <i class="fas fa-reply me-1"></i>{{ discussion.reply_count }}
                                        </span>
                                        <span class="stats-badge">
                                            <i class="fas fa-eye me-1"></i>{{ discussion.view_count }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <div class="text-center py-5">
                            <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">{% trans "No discussions yet" %}</h5>
                            <p class="text-muted">{% trans "Be the first to start a discussion!" %}</p>
                            {% if user.is_authenticated %}
                            <a href="{% url 'discussions:create_discussion' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>{% trans "Start Discussion" %}
                            </a>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
