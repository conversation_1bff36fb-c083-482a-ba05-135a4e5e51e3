{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Contact Us" %} - ECPI{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section bg-primary text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 fw-bold mb-4">
                    {% trans "Contact Us" %}
                </h1>
                <p class="lead">
                    {% trans "We're here to help. Reach out to us with any questions or concerns." %}
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="py-5">
    <div class="container">
        <div class="row g-5">
            <!-- Contact Form -->
            <div class="col-lg-8">
                <div class="card shadow-sm border-0">
                    <div class="card-body p-5">
                        <h2 class="h3 mb-4">{% trans "Send us a Message" %}</h2>
                        <form method="post" class="needs-validation" novalidate>
                            {% csrf_token %}
                            {{ form|crispy }}
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="col-lg-4">
                <div class="card shadow-sm border-0 h-100">
                    <div class="card-body p-4">
                        <h3 class="h4 mb-4">{% trans "Get in Touch" %}</h3>
                        
                        <!-- Contact Details -->
                        <div class="contact-info">
                            <div class="d-flex align-items-start mb-4">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-map-marker-alt fa-lg text-primary"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h5 class="mb-1">{% trans "Address" %}</h5>
                                    <p class="text-muted mb-0">
                                        {% if site_settings.address %}
                                            {{ site_settings.address }}
                                        {% else %}
                                            {% trans "Addis Ababa, Ethiopia" %}
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                            
                            <div class="d-flex align-items-start mb-4">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-phone fa-lg text-primary"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h5 class="mb-1">{% trans "Phone" %}</h5>
                                    <p class="text-muted mb-0">
                                        {% if site_settings.contact_phone %}
                                            {{ site_settings.contact_phone }}
                                        {% else %}
                                            +251-11-XXX-XXXX
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                            
                            <div class="d-flex align-items-start mb-4">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-envelope fa-lg text-primary"></i>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h5 class="mb-1">{% trans "Email" %}</h5>
                                    <p class="text-muted mb-0">
                                        {% if site_settings.contact_email %}
                                            {{ site_settings.contact_email }}
                                        {% else %}
                                            <EMAIL>
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Emergency Information -->
                        <div class="alert alert-warning mt-4" role="alert">
                            <h6 class="alert-heading">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                {% trans "Mental Health Emergency?" %}
                            </h6>
                            <p class="mb-2">
                                {% trans "If you're experiencing a mental health crisis, please contact:" %}
                            </p>
                            <ul class="mb-0">
                                <li>{% trans "Emergency Services: 911" %}</li>
                                <li>{% trans "Crisis Hotline: 988" %}</li>
                                {% if site_settings.emergency_hotline %}
                                    <li>{% trans "Local Emergency:" %} {{ site_settings.emergency_hotline }}</li>
                                {% endif %}
                            </ul>
                        </div>
                        
                        <!-- Social Media -->
                        {% if site_settings.facebook_url or site_settings.twitter_url or site_settings.instagram_url or site_settings.linkedin_url %}
                            <div class="mt-4">
                                <h5 class="mb-3">{% trans "Follow Us" %}</h5>
                                <div class="social-links">
                                    {% if site_settings.facebook_url %}
                                        <a href="{{ site_settings.facebook_url }}" class="btn btn-outline-primary btn-sm me-2" target="_blank">
                                            <i class="fab fa-facebook-f"></i>
                                        </a>
                                    {% endif %}
                                    {% if site_settings.twitter_url %}
                                        <a href="{{ site_settings.twitter_url }}" class="btn btn-outline-info btn-sm me-2" target="_blank">
                                            <i class="fab fa-twitter"></i>
                                        </a>
                                    {% endif %}
                                    {% if site_settings.instagram_url %}
                                        <a href="{{ site_settings.instagram_url }}" class="btn btn-outline-danger btn-sm me-2" target="_blank">
                                            <i class="fab fa-instagram"></i>
                                        </a>
                                    {% endif %}
                                    {% if site_settings.linkedin_url %}
                                        <a href="{{ site_settings.linkedin_url }}" class="btn btn-outline-primary btn-sm" target="_blank">
                                            <i class="fab fa-linkedin-in"></i>
                                        </a>
                                    {% endif %}
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="bg-light py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="text-center mb-5">
                    <h2 class="display-6 fw-bold mb-3">{% trans "Frequently Asked Questions" %}</h2>
                    <p class="lead text-muted">
                        {% trans "Find quick answers to common questions about our platform." %}
                    </p>
                </div>
                
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq1">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                {% trans "How do I book a consultation with a psychologist?" %}
                            </button>
                        </h2>
                        <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                {% trans "You can browse our list of certified psychologists, view their profiles, and book a consultation directly through their profile page. First-time users can often access free initial consultations." %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq2">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                {% trans "Is my information kept confidential?" %}
                            </button>
                        </h2>
                        <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                {% trans "Yes, absolutely. All communications on our platform are encrypted and confidential. We follow strict privacy guidelines to protect your personal information and mental health data." %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq3">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                                {% trans "What languages are supported?" %}
                            </button>
                        </h2>
                        <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                {% trans "Our platform supports both English and Amharic. Many of our psychologists are bilingual and can provide services in both languages." %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq4">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4">
                                {% trans "What is the minimum age to use this platform?" %}
                            </button>
                        </h2>
                        <div id="collapse4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                {% trans "Users must be at least 14 years old to register and use our platform. For users under 18, we recommend involving a parent or guardian in the mental health journey." %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <a href="{% url 'core:faq' %}" class="btn btn-outline-primary">
                        {% trans "View All FAQs" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
