{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Home" %} - ECPI{% endblock %}

{% block extra_css %}
<link href="{% static 'css/home.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold mb-4">
                    {% trans "Your Mental Health Matters" %}
                </h1>
                <p class="lead mb-4">
                    {% trans "Connect with professional psychologists, join supportive discussions, and access mental health resources in English and Amharic." %}
                </p>
                <div class="hero-buttons">
                    {% if not user.is_authenticated %}
                        <a href="{% url 'accounts:signup' %}" class="btn btn-light btn-lg me-3">
                            {% trans "Get Started" %}
                        </a>
                        <a href="{% url 'consultation:psychologist_list' %}" class="btn btn-outline-light btn-lg">
                            {% trans "Find a Psychologist" %}
                        </a>
                    {% elif user.is_staff %}
                        <a href="{% url 'admin:index' %}" class="btn btn-light btn-lg me-3">
                            {% trans "Admin Panel" %}
                        </a>
                        <a href="{% url 'discussions:discussion_list' %}" class="btn btn-outline-light btn-lg">
                            {% trans "Manage Discussions" %}
                        </a>
                    {% elif user.psychologist_profile %}
                        <a href="{% url 'discussions:discussion_list' %}" class="btn btn-light btn-lg me-3">
                            {% trans "Join Discussions" %}
                        </a>
                        <a href="{% url 'resources:resource_list' %}" class="btn btn-outline-light btn-lg">
                            {% trans "View Resources" %}
                        </a>
                    {% else %}
                        <a href="{% url 'consultation:psychologist_list' %}" class="btn btn-light btn-lg me-3">
                            {% trans "Book Consultation" %}
                        </a>
                        <a href="{% url 'discussions:discussion_list' %}" class="btn btn-outline-light btn-lg">
                            {% trans "Join Discussions" %}
                        </a>
                    {% endif %}
                </div>
            </div>
            <div class="col-lg-6">
                <img src="{% static 'images/hero-image.jpg' %}" alt="Mental Health Support" class="img-fluid rounded">
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section py-5">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col-lg-8 mx-auto">
                <h2 class="display-5 fw-bold mb-3">{% trans "How We Help" %}</h2>
                <p class="lead text-muted">
                    {% trans "Our platform provides comprehensive mental health support tailored for Ethiopian users." %}
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            <!-- Professional Consultations -->
            <div class="col-lg-4 col-md-6">
                <div class="feature-card h-100 p-4 text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-user-md fa-3x text-primary"></i>
                    </div>
                    <h4 class="mb-3">{% trans "Professional Consultations" %}</h4>
                    <p class="text-muted">
                        {% trans "Book one-on-one sessions with certified psychologists. Free consultations available for first-time users." %}
                    </p>
                    {% if not user.is_staff and not user.psychologist_profile %}
                        <a href="{% url 'consultation:psychologist_list' %}" class="btn btn-outline-primary">
                            {% trans "Find Psychologist" %}
                        </a>
                    {% elif user.psychologist_profile %}
                        <a href="{% url 'discussions:discussion_list' %}" class="btn btn-outline-primary">
                            {% trans "Join Discussions" %}
                        </a>
                    {% else %}
                        <a href="{% url 'admin:index' %}" class="btn btn-outline-primary">
                            {% trans "Manage Platform" %}
                        </a>
                    {% endif %}
                </div>
            </div>
            
            <!-- Community Discussions -->
            <div class="col-lg-4 col-md-6">
                <div class="feature-card h-100 p-4 text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-comments fa-3x text-success"></i>
                    </div>
                    <h4 class="mb-3">{% trans "Community Discussions" %}</h4>
                    <p class="text-muted">
                        {% trans "Share experiences and get support from peers in a safe, moderated environment." %}
                    </p>
                    <a href="{% url 'discussions:discussion_list' %}" class="btn btn-outline-success">
                        {% trans "Join Community" %}
                    </a>
                </div>
            </div>
            
            <!-- Mental Health Resources -->
            <div class="col-lg-4 col-md-6">
                <div class="feature-card h-100 p-4 text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-book fa-3x text-info"></i>
                    </div>
                    <h4 class="mb-3">{% trans "Educational Resources" %}</h4>
                    <p class="text-muted">
                        {% trans "Access videos, audio content, and documents to learn about mental health and coping strategies." %}
                    </p>
                    <a href="{% url 'resources:resource_list' %}" class="btn btn-outline-info">
                        {% trans "Browse Resources" %}
                    </a>
                </div>
            </div>
            
            <!-- Secure Messaging -->
            <div class="col-lg-4 col-md-6">
                <div class="feature-card h-100 p-4 text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-lock fa-3x text-warning"></i>
                    </div>
                    <h4 class="mb-3">{% trans "Secure Messaging" %}</h4>
                    <p class="text-muted">
                        {% trans "Private, encrypted communication with psychologists and other users." %}
                    </p>
                    {% if user.is_authenticated %}
                        <a href="{% url 'chat:inbox' %}" class="btn btn-outline-warning">
                            {% trans "Open Messages" %}
                        </a>
                    {% else %}
                        <a href="{% url 'accounts:login' %}" class="btn btn-outline-warning">
                            {% trans "Login to Message" %}
                        </a>
                    {% endif %}
                </div>
            </div>
            
            <!-- Meetings & Seminars -->
            <div class="col-lg-4 col-md-6">
                <div class="feature-card h-100 p-4 text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-calendar fa-3x text-danger"></i>
                    </div>
                    <h4 class="mb-3">{% trans "Meetings & Seminars" %}</h4>
                    <p class="text-muted">
                        {% trans "Attend online and offline mental health events, workshops, and group sessions." %}
                    </p>
                    <a href="{% url 'meetings:meeting_list' %}" class="btn btn-outline-danger">
                        {% trans "View Events" %}
                    </a>
                </div>
            </div>
            
            <!-- 24/7 Support -->
            <div class="col-lg-4 col-md-6">
                <div class="feature-card h-100 p-4 text-center">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-headset fa-3x text-secondary"></i>
                    </div>
                    <h4 class="mb-3">{% trans "24/7 AI Assistant" %}</h4>
                    <p class="text-muted">
                        {% trans "Get immediate support from our AI chatbot trained on mental health resources and FAQs." %}
                    </p>
                    <button class="btn btn-outline-secondary" onclick="toggleChatbot()">
                        {% trans "Chat Now" %}
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="stats-section bg-light py-5">
    <div class="container">
        <div class="row text-center">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-item">
                    <h3 class="display-4 fw-bold text-primary">{{ stats.total_users|default:"500+" }}</h3>
                    <p class="text-muted">{% trans "Active Users" %}</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-item">
                    <h3 class="display-4 fw-bold text-success">{{ stats.total_psychologists|default:"50+" }}</h3>
                    <p class="text-muted">{% trans "Certified Psychologists" %}</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-item">
                    <h3 class="display-4 fw-bold text-info">{{ stats.total_consultations|default:"1000+" }}</h3>
                    <p class="text-muted">{% trans "Consultations Completed" %}</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="stat-item">
                    <h3 class="display-4 fw-bold text-warning">{{ stats.total_resources|default:"200+" }}</h3>
                    <p class="text-muted">{% trans "Educational Resources" %}</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section bg-primary text-white py-5">
    <div class="container text-center">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <h2 class="display-5 fw-bold mb-4">
                    {% trans "Ready to Start Your Mental Health Journey?" %}
                </h2>
                <p class="lead mb-4">
                    {% trans "Join thousands of Ethiopians who have found support and healing through our platform." %}
                </p>
                {% if not user.is_authenticated %}
                    <a href="{% url 'accounts:signup' %}" class="btn btn-light btn-lg me-3">
                        {% trans "Sign Up Free" %}
                    </a>
                    <a href="{% url 'core:about' %}" class="btn btn-outline-light btn-lg">
                        {% trans "Learn More" %}
                    </a>
                {% elif user.is_staff %}
                    <a href="{% url 'admin:index' %}" class="btn btn-light btn-lg me-3">
                        {% trans "Manage Platform" %}
                    </a>
                    <a href="{% url 'core:about' %}" class="btn btn-outline-light btn-lg">
                        {% trans "Learn More" %}
                    </a>
                {% elif user.psychologist_profile %}
                    <a href="{% url 'discussions:create_discussion' %}" class="btn btn-light btn-lg me-3">
                        {% trans "Start a Discussion" %}
                    </a>
                    <a href="{% url 'resources:resource_list' %}" class="btn btn-outline-light btn-lg">
                        {% trans "Share Resources" %}
                    </a>
                {% else %}
                    <a href="{% url 'consultation:psychologist_list' %}" class="btn btn-light btn-lg me-3">
                        {% trans "Book Your First Session" %}
                    </a>
                    <a href="{% url 'discussions:create_discussion' %}" class="btn btn-outline-light btn-lg">
                        {% trans "Start a Discussion" %}
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
function toggleChatbot() {
    document.getElementById('chatbot-toggle').click();
}
</script>
{% endblock %}
