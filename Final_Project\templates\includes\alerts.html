{% load i18n %}

{% if messages %}
    <div class="container mt-3">
        {% for message in messages %}
            <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show" role="alert">
                {% if message.tags == 'error' %}
                    <i class="fas fa-exclamation-triangle me-2"></i>
                {% elif message.tags == 'success' %}
                    <i class="fas fa-check-circle me-2"></i>
                {% elif message.tags == 'warning' %}
                    <i class="fas fa-exclamation-circle me-2"></i>
                {% else %}
                    <i class="fas fa-info-circle me-2"></i>
                {% endif %}
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    </div>
{% endif %}
