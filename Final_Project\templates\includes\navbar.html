{% load static %}
{% load i18n %}

<nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
    <div class="container">
        <!-- Brand -->
        <a class="navbar-brand d-flex align-items-center" href="{% url 'core:home' %}">
            <img src="{% static 'images/logo-white.png' %}" alt="ECPI Logo" height="40" class="me-2"
                 onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
            <!-- ECPI text removed as requested -->
        </a>
        
        <!-- Mobile toggle -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <!-- Navigation items -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'core:home' %}">
                        <i class="fas fa-home me-1"></i>{% trans "Home" %}
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link" href="{% url 'discussions:discussion_list' %}">
                        <i class="fas fa-comments me-1"></i>{% trans "Discussions" %}
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'consultation:psychologist_list' %}">
                        <i class="fas fa-user-md me-1"></i>{% trans "Consultations" %}
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'resources:resource_list' %}">
                        <i class="fas fa-book me-1"></i>{% trans "Resources" %}
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'meetings:meeting_list' %}">
                        <i class="fas fa-calendar me-1"></i>{% trans "Meetings" %}
                    </a>
                </li>
            </ul>
            
            <!-- Right side navigation -->
            <ul class="navbar-nav">
                <!-- Language switcher -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-globe me-1"></i>
                        {% get_current_language as LANGUAGE_CODE %}
                        {% if LANGUAGE_CODE == 'am' %}አማርኛ{% else %}English{% endif %}
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="setLanguage('en'); return false;">English</a></li>
                        <li><a class="dropdown-item" href="#" onclick="setLanguage('am'); return false;">አማርኛ</a></li>
                    </ul>
                </li>
                
                {% if user.is_authenticated %}
                    <!-- Notifications -->
                    <li class="nav-item dropdown">
                        <a class="nav-link position-relative" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bell"></i>
                            {% if unread_notifications_count > 0 %}
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                {{ unread_notifications_count }}
                            </span>
                            {% endif %}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end notification-dropdown">
                            <li><h6 class="dropdown-header">{% trans "Notifications" %}</h6></li>
                            {% if user.notifications.all|slice:":3" %}
                                {% for notification in user.notifications.all|slice:":3" %}
                                    <li>
                                        <a class="dropdown-item {% if not notification.is_read %}fw-bold{% endif %}"
                                           href="{% url 'core:mark_notification_read' notification.id %}">
                                            <div class="d-flex justify-content-between">
                                                <span>{{ notification.title|truncatechars:30 }}</span>
                                                {% if not notification.is_read %}
                                                    <span class="badge bg-primary badge-sm">{% trans "New" %}</span>
                                                {% endif %}
                                            </div>
                                            <small class="text-muted">{{ notification.created_at|timesince }} {% trans "ago" %}</small>
                                        </a>
                                    </li>
                                {% endfor %}
                            {% else %}
                                <li><span class="dropdown-item text-muted">{% trans "No notifications" %}</span></li>
                            {% endif %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-center" href="{% url 'core:notifications' %}">{% trans "View all" %}</a></li>
                        </ul>
                    </li>

                    <!-- User menu -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                            <img src="{% if user.profile.avatar %}{{ user.profile.avatar.url }}{% else %}{% static 'images/default-avatar.png' %}{% endif %}" 
                                 alt="Avatar" class="rounded-circle me-2" width="30" height="30">
                            {{ user.get_full_name|default:user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{% url 'core:dashboard' %}">
                                <i class="fas fa-tachometer-alt me-2"></i>{% trans "Dashboard" %}
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'accounts:profile' %}">
                                <i class="fas fa-user me-2"></i>{% trans "Profile" %}
                            </a></li>
                            {% if user.is_staff %}
                                <li><a class="dropdown-item" href="{% url 'admin:index' %}">
                                    <i class="fas fa-cog me-2"></i>{% trans "Admin" %}
                                </a></li>
                            {% endif %}
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="post" action="{% url 'accounts:logout' %}" style="display:inline;">
                                    {% csrf_token %}
                                    <button type="submit" class="dropdown-item" style="width:100%;text-align:left;">
                                        <i class="fas fa-sign-out-alt me-2"></i>{% trans "Logout" %}
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'accounts:login' %}">
                            <i class="fas fa-sign-in-alt me-1"></i>{% trans "Login" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-outline-light ms-2" href="{% url 'accounts:signup' %}">
                            {% trans "Sign Up" %}
                        </a>
                    </li>
                {% endif %}
            </ul>
        </div>
    </div>
</nav>

<script>
function setLanguage(lang) {
    // Prevent any default behavior
    event.preventDefault();

    // Create form for language switching
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{% url "set_language" %}';
    form.style.display = 'none';

    // Add CSRF token
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = 'csrfmiddlewaretoken';
    csrfToken.value = '{{ csrf_token }}';

    // Add language input
    const languageInput = document.createElement('input');
    languageInput.type = 'hidden';
    languageInput.name = 'language';
    languageInput.value = lang;

    // Add next parameter to stay on current page
    const nextInput = document.createElement('input');
    nextInput.type = 'hidden';
    nextInput.name = 'next';
    nextInput.value = window.location.pathname + window.location.search;

    // Append inputs to form
    form.appendChild(csrfToken);
    form.appendChild(languageInput);
    form.appendChild(nextInput);

    // Submit form
    document.body.appendChild(form);
    form.submit();

    return false;
}
</script>
