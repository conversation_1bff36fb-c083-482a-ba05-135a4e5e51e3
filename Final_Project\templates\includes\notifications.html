{% load static %}
{% load i18n %}

<!-- Notification System -->
<div id="notification-container" class="position-fixed top-0 end-0 p-3" style="z-index: 1055;">
    <!-- Notifications will be dynamically added here -->
</div>

<!-- Notification Styles -->
<style>
.notification {
    min-width: 300px;
    max-width: 400px;
    margin-bottom: 0.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    animation: slideInRight 0.3s ease-out;
}

.notification.notification-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.notification.notification-info {
    background: linear-gradient(135deg, #17a2b8 0%, #007bff 100%);
    color: white;
}

.notification.notification-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: #212529;
}

.notification.notification-error {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
    color: white;
}

.notification.notification-consultation {
    background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
    color: white;
}

.notification-header {
    display: flex;
    align-items-center;
    justify-content-between;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.notification-body {
    font-size: 0.875rem;
    line-height: 1.4;
}

.notification-actions {
    margin-top: 0.75rem;
    display: flex;
    gap: 0.5rem;
}

.notification-btn {
    padding: 0.25rem 0.75rem;
    border: 1px solid rgba(255,255,255,0.3);
    background: rgba(255,255,255,0.1);
    color: inherit;
    border-radius: 4px;
    text-decoration: none;
    font-size: 0.75rem;
    transition: all 0.2s ease;
}

.notification-btn:hover {
    background: rgba(255,255,255,0.2);
    color: inherit;
    text-decoration: none;
}

.notification-close {
    background: none;
    border: none;
    color: inherit;
    opacity: 0.7;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-close:hover {
    opacity: 1;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.notification.removing {
    animation: slideOutRight 0.3s ease-in forwards;
}

/* Consultation reminder specific styles */
.consultation-reminder {
    border-left: 4px solid #ffc107;
}

.consultation-reminder .notification-icon {
    color: #ffc107;
    font-size: 1.2rem;
    margin-right: 0.5rem;
}

/* Message notification styles */
.message-notification {
    border-left: 4px solid #17a2b8;
}

.message-notification .notification-icon {
    color: #17a2b8;
    font-size: 1.2rem;
    margin-right: 0.5rem;
}
</style>

<!-- Notification JavaScript -->
<script>
class NotificationManager {
    constructor() {
        this.container = document.getElementById('notification-container');
        this.notifications = new Map();
        this.init();
    }
    
    init() {
        // Check for consultation reminders
        this.checkConsultationReminders();
        
        // Set up periodic checks
        setInterval(() => {
            this.checkConsultationReminders();
        }, 60000); // Check every minute
        
        // Listen for WebSocket messages if available
        if (window.consultationSocket) {
            window.consultationSocket.addEventListener('message', (event) => {
                const data = JSON.parse(event.data);
                if (data.type === 'notification') {
                    this.show(data.notification);
                }
            });
        }
    }
    
    show(options) {
        const {
            id = Date.now(),
            type = 'info',
            title,
            message,
            actions = [],
            autoClose = true,
            duration = 5000
        } = options;
        
        // Remove existing notification with same ID
        if (this.notifications.has(id)) {
            this.remove(id);
        }
        
        const notification = this.createNotification({
            id, type, title, message, actions
        });
        
        this.container.appendChild(notification);
        this.notifications.set(id, notification);
        
        // Auto-close if enabled
        if (autoClose) {
            setTimeout(() => {
                this.remove(id);
            }, duration);
        }
        
        return id;
    }
    
    createNotification({ id, type, title, message, actions }) {
        const notification = document.createElement('div');
        notification.className = `alert notification notification-${type}`;
        notification.setAttribute('data-id', id);
        
        const iconMap = {
            success: 'fas fa-check-circle',
            info: 'fas fa-info-circle',
            warning: 'fas fa-exclamation-triangle',
            error: 'fas fa-times-circle',
            consultation: 'fas fa-video'
        };
        
        notification.innerHTML = `
            <div class="notification-header">
                <div>
                    <i class="${iconMap[type] || iconMap.info} me-2"></i>
                    ${title}
                </div>
                <button class="notification-close" onclick="notificationManager.remove('${id}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="notification-body">
                ${message}
            </div>
            ${actions.length > 0 ? `
                <div class="notification-actions">
                    ${actions.map(action => `
                        <a href="${action.url}" class="notification-btn">
                            ${action.label}
                        </a>
                    `).join('')}
                </div>
            ` : ''}
        `;
        
        return notification;
    }
    
    remove(id) {
        const notification = this.notifications.get(id);
        if (notification) {
            notification.classList.add('removing');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
                this.notifications.delete(id);
            }, 300);
        }
    }
    
    checkConsultationReminders() {
        // This would typically fetch from an API endpoint
        // For now, we'll use a simple check based on page data
        if (window.upcomingConsultations) {
            const now = new Date();
            
            window.upcomingConsultations.forEach(consultation => {
                const consultationTime = new Date(consultation.scheduled_datetime);
                const timeDiff = consultationTime - now;
                const minutesUntil = Math.floor(timeDiff / (1000 * 60));
                
                // Show reminder 15 minutes before
                if (minutesUntil === 15) {
                    this.show({
                        id: `reminder-${consultation.id}`,
                        type: 'consultation',
                        title: '{% trans "Consultation Reminder" %}',
                        message: `{% trans "Your consultation with" %} ${consultation.client_name} {% trans "starts in 15 minutes" %}`,
                        actions: [
                            {
                                label: '{% trans "Join Now" %}',
                                url: consultation.chat_url
                            }
                        ],
                        autoClose: false
                    });
                }
                
                // Show immediate reminder
                if (minutesUntil === 0) {
                    this.show({
                        id: `start-${consultation.id}`,
                        type: 'warning',
                        title: '{% trans "Consultation Starting" %}',
                        message: `{% trans "Your consultation with" %} ${consultation.client_name} {% trans "is starting now" %}`,
                        actions: [
                            {
                                label: '{% trans "Join Now" %}',
                                url: consultation.chat_url
                            }
                        ],
                        autoClose: false
                    });
                }
            });
        }
    }
    
    // Predefined notification types
    success(title, message, actions = []) {
        return this.show({ type: 'success', title, message, actions });
    }
    
    info(title, message, actions = []) {
        return this.show({ type: 'info', title, message, actions });
    }
    
    warning(title, message, actions = []) {
        return this.show({ type: 'warning', title, message, actions });
    }
    
    error(title, message, actions = []) {
        return this.show({ type: 'error', title, message, actions });
    }
    
    consultation(title, message, actions = []) {
        return this.show({ type: 'consultation', title, message, actions });
    }
}

// Initialize notification manager
document.addEventListener('DOMContentLoaded', function() {
    window.notificationManager = new NotificationManager();
});

// Helper functions for common notifications
function showConsultationStarted(consultationId, clientName) {
    notificationManager.consultation(
        '{% trans "Consultation Started" %}',
        `{% trans "Your consultation with" %} ${clientName} {% trans "has started" %}`,
        [
            {
                label: '{% trans "Join Chat" %}',
                url: `/consultation/${consultationId}/chat/`
            }
        ]
    );
}

function showNewMessage(senderName, message, consultationId = null) {
    const actions = consultationId ? [
        {
            label: '{% trans "View Chat" %}',
            url: `/consultation/consultation/${consultationId}/chat-room/`
        }
    ] : [];

    notificationManager.show({
        type: 'message',
        title: '{% trans "New Message" %}',
        message: `${senderName}: ${message.length > 50 ? message.substring(0, 50) + '...' : message}`,
        actions: actions,
        autoClose: false
    });
}

function showConsultationCompleted(consultationId) {
    notificationManager.success(
        '{% trans "Consultation Completed" %}',
        '{% trans "The consultation has been completed successfully" %}',
        [
            {
                label: '{% trans "Add Summary" %}',
                url: `/consultation/${consultationId}/summary/`
            }
        ]
    );
}
</script>
