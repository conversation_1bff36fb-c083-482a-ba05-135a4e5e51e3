{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Payment Cancelled" %} - ECPI{% endblock %}

{% block extra_css %}
<link href="{% static 'css/payments.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <!-- Cancel Card -->
            <div class="card shadow-lg border-0">
                <div class="card-body text-center p-5">
                    <!-- Cancel Icon -->
                    <div class="cancel-icon mb-4">
                        <i class="fas fa-times-circle fa-5x text-warning"></i>
                    </div>
                    
                    <!-- Cancel Message -->
                    <h1 class="h2 text-warning mb-3">{% trans "Payment Cancelled" %}</h1>
                    <p class="lead text-muted mb-4">
                        {% trans "Your payment was cancelled. No charges have been made to your account." %}
                    </p>
                    
                    <!-- Information -->
                    <div class="info-section mb-4">
                        <div class="alert alert-info">
                            <h6 class="alert-heading">
                                <i class="fas fa-info-circle me-2"></i>{% trans "What happened?" %}
                            </h6>
                            <p class="mb-0">
                                {% trans "You cancelled the payment process or closed the payment window. Your consultation booking is still pending and you can complete the payment anytime." %}
                            </p>
                        </div>
                    </div>
                    
                    <!-- Next Steps -->
                    <div class="next-steps mb-4">
                        <h5 class="mb-3">{% trans "What can you do now?" %}</h5>
                        <div class="row g-3">
                            <div class="col-12">
                                <div class="option-item p-3 bg-light rounded">
                                    <div class="d-flex align-items-center">
                                        <div class="option-icon bg-primary text-white rounded-circle me-3">
                                            <i class="fas fa-credit-card"></i>
                                        </div>
                                        <div class="text-start">
                                            <h6 class="mb-1">{% trans "Try Payment Again" %}</h6>
                                            <small class="text-muted">{% trans "Complete your consultation booking" %}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-12">
                                <div class="option-item p-3 bg-light rounded">
                                    <div class="d-flex align-items-center">
                                        <div class="option-icon bg-success text-white rounded-circle me-3">
                                            <i class="fas fa-calendar"></i>
                                        </div>
                                        <div class="text-start">
                                            <h6 class="mb-1">{% trans "Choose Different Time" %}</h6>
                                            <small class="text-muted">{% trans "Select a different consultation slot" %}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-12">
                                <div class="option-item p-3 bg-light rounded">
                                    <div class="d-flex align-items-center">
                                        <div class="option-icon bg-info text-white rounded-circle me-3">
                                            <i class="fas fa-user-md"></i>
                                        </div>
                                        <div class="text-start">
                                            <h6 class="mb-1">{% trans "Choose Different Psychologist" %}</h6>
                                            <small class="text-muted">{% trans "Browse other available professionals" %}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="d-grid gap-2">
                        <a href="{% url 'consultation:my_consultations' %}" class="btn btn-primary btn-lg">
                            <i class="fas fa-calendar me-2"></i>{% trans "View My Consultations" %}
                        </a>
                        <a href="{% url 'consultation:psychologist_list' %}" class="btn btn-outline-success">
                            <i class="fas fa-search me-2"></i>{% trans "Find Another Psychologist" %}
                        </a>
                        <a href="{% url 'core:home' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-home me-2"></i>{% trans "Back to Home" %}
                        </a>
                    </div>
                    
                    <!-- Support Info -->
                    <div class="mt-4 pt-4 border-top">
                        <small class="text-muted">
                            {% trans "Need help with payment?" %} 
                            <a href="{% url 'core:contact' %}">{% trans "Contact Support" %}</a>
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- Payment Methods Info -->
            <div class="card mt-4 border-0 bg-light">
                <div class="card-body p-4">
                    <h6 class="mb-3">{% trans "Accepted Payment Methods" %}</h6>
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="payment-method-info">
                                <i class="fas fa-credit-card fa-2x text-primary mb-2"></i>
                                <small class="d-block text-muted">{% trans "Credit Cards" %}</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="payment-method-info">
                                <i class="fas fa-mobile-alt fa-2x text-success mb-2"></i>
                                <small class="d-block text-muted">{% trans "Mobile Money" %}</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="payment-method-info">
                                <i class="fas fa-university fa-2x text-info mb-2"></i>
                                <small class="d-block text-muted">{% trans "Bank Transfer" %}</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-3 text-center">
                        <small class="text-muted">
                            <i class="fas fa-shield-alt me-1"></i>
                            {% trans "All payments are processed securely through Chapa" %}
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- FAQ Section -->
            <div class="card mt-4 border-0">
                <div class="card-body p-4">
                    <h6 class="mb-3">{% trans "Frequently Asked Questions" %}</h6>
                    
                    <div class="accordion" id="cancelFAQ">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq1">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                    {% trans "Why was my payment cancelled?" %}
                                </button>
                            </h2>
                            <div id="collapse1" class="accordion-collapse collapse" data-bs-parent="#cancelFAQ">
                                <div class="accordion-body">
                                    {% trans "Payment can be cancelled if you close the payment window, click the back button, or choose to cancel during the payment process. No charges are made when payment is cancelled." %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq2">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                    {% trans "Is my consultation booking still valid?" %}
                                </button>
                            </h2>
                            <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#cancelFAQ">
                                <div class="accordion-body">
                                    {% trans "Your consultation booking is held temporarily, but payment must be completed to confirm the appointment. The time slot may be released if payment is not completed within a reasonable time." %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq3">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                                    {% trans "Can I try a different payment method?" %}
                                </button>
                            </h2>
                            <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#cancelFAQ">
                                <div class="accordion-body">
                                    {% trans "Yes, you can try again with the same or different payment method. Chapa supports various payment options including credit cards, mobile money, and bank transfers." %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Add hover effects to option items
document.addEventListener('DOMContentLoaded', function() {
    const optionItems = document.querySelectorAll('.option-item');
    
    optionItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });
});
</script>

<style>
/* Option Icons */
.option-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

/* Option Items */
.option-item {
    transition: all 0.3s ease;
    cursor: pointer;
}

/* Payment Method Info */
.payment-method-info {
    padding: 1rem;
    transition: all 0.3s ease;
}

.payment-method-info:hover {
    transform: translateY(-2px);
}

/* Cancel Icon Animation */
.cancel-icon {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}
</style>
{% endblock %}
