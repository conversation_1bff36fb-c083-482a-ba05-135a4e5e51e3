{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Payment Checkout" %} - ECPI{% endblock %}

{% block extra_css %}
<link href="{% static 'css/payments.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="display-6 fw-bold">{% trans "Complete Your Payment" %}</h1>
                <p class="lead text-muted">{% trans "Secure payment processing with Chapa" %}</p>
            </div>
            
            <!-- Payment Summary Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-receipt me-2"></i>{% trans "Payment Summary" %}
                    </h4>
                </div>
                <div class="card-body p-4">
                    <!-- Consultation Details -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5 class="mb-3">{% trans "Consultation Details" %}</h5>
                            <div class="consultation-info">
                                <div class="info-item mb-2">
                                    <strong>{% trans "Type:" %}</strong>
                                    <span class="ms-2">{{ consultation.consultation_type.display_name }}</span>
                                </div>
                                <div class="info-item mb-2">
                                    <strong>{% trans "Date:" %}</strong>
                                    <span class="ms-2">{{ consultation.scheduled_date|date:"l, F d, Y" }}</span>
                                </div>
                                <div class="info-item mb-2">
                                    <strong>{% trans "Time:" %}</strong>
                                    <span class="ms-2">{{ consultation.scheduled_start_time|time:"H:i" }}</span>
                                </div>
                                <div class="info-item mb-2">
                                    <strong>{% trans "Duration:" %}</strong>
                                    <span class="ms-2">{{ consultation.consultation_type.default_duration }} {% trans "minutes" %}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h5 class="mb-3">{% trans "Psychologist" %}</h5>
                            <div class="d-flex align-items-center">
                                <img src="{% if consultation.psychologist.user.profile.avatar %}{{ consultation.psychologist.user.profile.avatar.url }}{% else %}{% static 'images/default-avatar.png' %}{% endif %}" 
                                     alt="Dr. {{ consultation.psychologist.user.get_full_name }}" 
                                     class="rounded-circle me-3" width="60" height="60">
                                <div>
                                    <h6 class="mb-1">Dr. {{ consultation.psychologist.user.get_full_name }}</h6>
                                    <small class="text-muted">{{ consultation.psychologist.years_of_experience }} {% trans "years experience" %}</small>
                                    <div class="rating">
                                        {% for i in "12345" %}
                                            <i class="fas fa-star {% if consultation.psychologist.average_rating >= i|add:0 %}text-warning{% else %}text-muted{% endif %} small"></i>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Price Breakdown -->
                    <div class="price-breakdown">
                        <h5 class="mb-3">{% trans "Price Breakdown" %}</h5>
                        <div class="table-responsive">
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <td>{{ consultation.consultation_type.display_name }}</td>
                                        <td class="text-end">{{ consultation.price }} {{ consultation.currency }}</td>
                                    </tr>
                                    <tr>
                                        <td>{% trans "Processing Fee" %}</td>
                                        <td class="text-end">{% trans "Included" %}</td>
                                    </tr>
                                    <tr class="border-top">
                                        <td class="fw-bold fs-5">{% trans "Total Amount" %}</td>
                                        <td class="text-end fw-bold fs-5 text-primary">{{ consultation.price }} {{ consultation.currency }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Payment Method Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-credit-card me-2"></i>{% trans "Payment Method" %}
                    </h4>
                </div>
                <div class="card-body p-4">
                    <div class="payment-methods">
                        <div class="payment-method-item selected">
                            <div class="d-flex align-items-center">
                                <div class="payment-icon me-3">
                                    <img src="{% static 'images/chapa-logo.png' %}" alt="Chapa" height="40" 
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                    <div class="bg-success text-white rounded p-2" style="display: none;">
                                        <i class="fas fa-credit-card"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{% trans "Chapa Payment" %}</h6>
                                    <small class="text-muted">{% trans "Secure payment with bank cards, mobile money, and more" %}</small>
                                </div>
                                <div class="payment-badges">
                                    <span class="badge bg-light text-dark me-1">Visa</span>
                                    <span class="badge bg-light text-dark me-1">Mastercard</span>
                                    <span class="badge bg-light text-dark">Mobile Money</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Security Notice -->
                    <div class="security-notice mt-4">
                        <div class="alert alert-info">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-shield-alt fa-2x text-info me-3"></i>
                                <div>
                                    <h6 class="mb-1">{% trans "Secure Payment" %}</h6>
                                    <small>{% trans "Your payment information is encrypted and secure. We do not store your payment details." %}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Terms and Conditions -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="terms-checkbox" required>
                        <label class="form-check-label" for="terms-checkbox">
                            {% trans "I agree to the" %} 
                            <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">{% trans "Terms and Conditions" %}</a> 
                            {% trans "and" %} 
                            <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">{% trans "Privacy Policy" %}</a>
                        </label>
                    </div>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            {% trans "By proceeding with payment, you confirm that you understand our cancellation policy and consultation terms." %}
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- Payment Actions -->
            <div class="d-grid gap-2">
                <form method="post" id="payment-form">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-success btn-lg" id="pay-button" disabled>
                        <i class="fas fa-lock me-2"></i>
                        {% trans "Pay" %} {{ consultation.price }} {{ consultation.currency }} {% trans "Securely" %}
                    </button>
                </form>
                
                <a href="{% url 'consultation:consultation_detail' consultation.pk %}" 
                   class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>{% trans "Back to Consultation" %}
                </a>
            </div>
            
            <!-- Support Info -->
            <div class="text-center mt-4">
                <small class="text-muted">
                    {% trans "Need help?" %} 
                    <a href="{% url 'core:contact' %}">{% trans "Contact Support" %}</a>
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Terms Modal -->
<div class="modal fade" id="termsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Terms and Conditions" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>{% trans "Consultation Terms" %}</h6>
                <ul>
                    <li>{% trans "Consultations are conducted online through our secure platform" %}</li>
                    <li>{% trans "Sessions must be attended at the scheduled time" %}</li>
                    <li>{% trans "Cancellations must be made at least 24 hours in advance" %}</li>
                    <li>{% trans "No-shows will be charged the full consultation fee" %}</li>
                </ul>
                
                <h6>{% trans "Payment Terms" %}</h6>
                <ul>
                    <li>{% trans "Payment is required before the consultation begins" %}</li>
                    <li>{% trans "Refunds are available for cancellations made 24+ hours in advance" %}</li>
                    <li>{% trans "Processing fees may apply to refunds" %}</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Privacy Modal -->
<div class="modal fade" id="privacyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Privacy Policy" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>{% trans "Data Protection" %}</h6>
                <p>{% trans "We protect your personal and payment information using industry-standard encryption and security measures." %}</p>
                
                <h6>{% trans "Information Usage" %}</h6>
                <p>{% trans "Your information is used solely for providing consultation services and improving our platform." %}</p>
                
                <h6>{% trans "Data Sharing" %}</h6>
                <p>{% trans "We do not share your personal information with third parties except as required for payment processing." %}</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Close" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const termsCheckbox = document.getElementById('terms-checkbox');
    const payButton = document.getElementById('pay-button');
    const paymentForm = document.getElementById('payment-form');
    
    // Enable/disable pay button based on terms acceptance
    termsCheckbox.addEventListener('change', function() {
        payButton.disabled = !this.checked;
    });
    
    // Add loading state to payment button
    paymentForm.addEventListener('submit', function() {
        payButton.disabled = true;
        payButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>{% trans "Processing..." %}';
    });
});
</script>
{% endblock %}
