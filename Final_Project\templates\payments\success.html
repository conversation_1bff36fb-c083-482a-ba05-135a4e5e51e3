{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Payment Successful" %} - ECPI{% endblock %}

{% block extra_css %}
<link href="{% static 'css/payments.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <!-- Success Card -->
            <div class="card shadow-lg border-0">
                <div class="card-body text-center p-5">
                    <!-- Success Icon -->
                    <div class="success-icon mb-4">
                        <div class="success-checkmark">
                            <div class="check-icon">
                                <span class="icon-line line-tip"></span>
                                <span class="icon-line line-long"></span>
                                <div class="icon-circle"></div>
                                <div class="icon-fix"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Success Message -->
                    <h1 class="h2 text-success mb-3">{% trans "Payment Successful!" %}</h1>
                    <p class="lead text-muted mb-4">
                        {% trans "Your consultation has been confirmed and you will receive a confirmation email shortly." %}
                    </p>
                    
                    <!-- Next Steps -->
                    <div class="next-steps mb-4">
                        <h5 class="mb-3">{% trans "What's Next?" %}</h5>
                        <div class="row g-3">
                            <div class="col-12">
                                <div class="step-item p-3 bg-light rounded">
                                    <div class="d-flex align-items-center">
                                        <div class="step-number bg-primary text-white rounded-circle me-3">1</div>
                                        <div class="text-start">
                                            <h6 class="mb-1">{% trans "Check Your Email" %}</h6>
                                            <small class="text-muted">{% trans "You'll receive a confirmation email with consultation details" %}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-12">
                                <div class="step-item p-3 bg-light rounded">
                                    <div class="d-flex align-items-center">
                                        <div class="step-number bg-success text-white rounded-circle me-3">2</div>
                                        <div class="text-start">
                                            <h6 class="mb-1">{% trans "Prepare for Your Session" %}</h6>
                                            <small class="text-muted">{% trans "Think about what you'd like to discuss during your consultation" %}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-12">
                                <div class="step-item p-3 bg-light rounded">
                                    <div class="d-flex align-items-center">
                                        <div class="step-number bg-info text-white rounded-circle me-3">3</div>
                                        <div class="text-start">
                                            <h6 class="mb-1">{% trans "Join at Scheduled Time" %}</h6>
                                            <small class="text-muted">{% trans "Access your consultation through your dashboard" %}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="d-grid gap-2">
                        <a href="{% url 'consultation:my_consultations' %}" class="btn btn-primary btn-lg">
                            <i class="fas fa-calendar me-2"></i>{% trans "View My Consultations" %}
                        </a>
                        <a href="{% url 'core:home' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-home me-2"></i>{% trans "Back to Home" %}
                        </a>
                    </div>
                    
                    <!-- Support Info -->
                    <div class="mt-4 pt-4 border-top">
                        <small class="text-muted">
                            {% trans "Questions about your consultation?" %} 
                            <a href="{% url 'core:contact' %}">{% trans "Contact Support" %}</a>
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- Additional Information -->
            <div class="card mt-4 border-0 bg-light">
                <div class="card-body p-4">
                    <h6 class="mb-3">{% trans "Important Reminders" %}</h6>
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-clock text-primary me-2"></i>
                            {% trans "Please join your consultation 5 minutes before the scheduled time" %}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-wifi text-primary me-2"></i>
                            {% trans "Ensure you have a stable internet connection" %}
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-headphones text-primary me-2"></i>
                            {% trans "Use headphones for better audio quality and privacy" %}
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-shield-alt text-primary me-2"></i>
                            {% trans "All consultations are confidential and secure" %}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Animate the success checkmark
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        document.querySelector('.success-checkmark').classList.add('animate');
    }, 500);
});
</script>

<style>
/* Success Checkmark Animation */
.success-checkmark {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: block;
    stroke-width: 2;
    stroke: #4CAF50;
    stroke-miterlimit: 10;
    margin: 0 auto;
    box-shadow: inset 0px 0px 0px #4CAF50;
    animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
    position: relative;
}

.success-checkmark.animate .check-icon {
    opacity: 1;
}

.success-checkmark .check-icon {
    width: 56px;
    height: 56px;
    position: absolute;
    left: 12px;
    top: 12px;
    opacity: 0;
}

.success-checkmark .check-icon .icon-line {
    height: 2px;
    background-color: #4CAF50;
    display: block;
    border-radius: 2px;
    position: absolute;
    z-index: 10;
}

.success-checkmark .check-icon .icon-line.line-tip {
    top: 19px;
    left: 14px;
    width: 25px;
    transform: rotate(45deg);
    animation: icon-line-tip .75s;
}

.success-checkmark .check-icon .icon-line.line-long {
    top: 38px;
    right: 8px;
    width: 47px;
    transform: rotate(-45deg);
    animation: icon-line-long .75s;
}

.success-checkmark .check-icon .icon-circle {
    top: -4px;
    left: -4px;
    z-index: 10;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    position: absolute;
    box-sizing: content-box;
    border: 4px solid rgba(76, 175, 80, .5);
}

.success-checkmark .check-icon .icon-fix {
    top: 8px;
    width: 5px;
    left: 26px;
    z-index: 1;
    height: 85px;
    position: absolute;
    transform: rotate(-45deg);
    background-color: white;
}

@keyframes icon-line-tip {
    0% {
        width: 0;
        left: 1px;
        top: 19px;
    }
    54% {
        width: 0;
        left: 1px;
        top: 19px;
    }
    70% {
        width: 50px;
        left: -8px;
        top: 37px;
    }
    84% {
        width: 17px;
        left: 21px;
        top: 48px;
    }
    100% {
        width: 25px;
        left: 14px;
        top: 45px;
    }
}

@keyframes icon-line-long {
    0% {
        width: 0;
        right: 46px;
        top: 54px;
    }
    65% {
        width: 0;
        right: 46px;
        top: 54px;
    }
    84% {
        width: 55px;
        right: 0px;
        top: 35px;
    }
    100% {
        width: 47px;
        right: 8px;
        top: 38px;
    }
}

@keyframes fill {
    100% {
        box-shadow: inset 0px 0px 0px 60px #4CAF50;
    }
}

@keyframes scale {
    0%, 100% {
        transform: none;
    }
    50% {
        transform: scale3d(1.1, 1.1, 1);
    }
}

/* Step Numbers */
.step-number {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
}

/* Step Items */
.step-item {
    transition: all 0.3s ease;
}

.step-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}
</style>
{% endblock %}
