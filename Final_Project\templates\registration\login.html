{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Sign In" %} - ECPI{% endblock %}

{% block extra_css %}
<link href="{% static 'css/auth.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="auth-card">
                    <!-- Header -->
                    <div class="auth-header text-center mb-4">
                        <img src="{% static 'images/logo.png' %}" alt="ECPI Logo" height="60" class="mb-3"
                             onerror="this.style.display='none';">
                        <h2 class="auth-title">{% trans "Welcome Back" %}</h2>
                        <p class="auth-subtitle text-muted">
                            {% trans "Sign in to access your consultation dashboard" %}
                        </p>
                    </div>

                    <!-- Login Form -->
                    <form method="post" class="auth-form" id="login-form">
                        {% csrf_token %}

                        <!-- Display form errors -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}

                        <!-- Username Field -->
                        <div class="form-group mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">
                                <i class="fas fa-user me-2"></i>{% trans "Username or Email" %}
                            </label>
                            <input type="text"
                                   class="form-control {% if form.username.errors %}is-invalid{% endif %}"
                                   id="{{ form.username.id_for_label }}"
                                   name="{{ form.username.name }}"
                                   value="{{ form.username.value|default:'' }}"
                                   placeholder="{% trans 'Enter your username or email' %}"
                                   required
                                   autofocus>
                            {% if form.username.errors %}
                                <div class="invalid-feedback">
                                    {{ form.username.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Password Field -->
                        <div class="form-group mb-3">
                            <label for="{{ form.password.id_for_label }}" class="form-label">
                                <i class="fas fa-lock me-2"></i>{% trans "Password" %}
                            </label>
                            <div class="password-input-group">
                                <input type="password"
                                       class="form-control {% if form.password.errors %}is-invalid{% endif %}"
                                       id="{{ form.password.id_for_label }}"
                                       name="{{ form.password.name }}"
                                       placeholder="{% trans 'Enter your password' %}"
                                       required>
                                <button type="button" class="password-toggle" onclick="togglePassword('{{ form.password.id_for_label }}')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            {% if form.password.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.password.errors.0 }}
                                </div>
                            {% endif %}
                        </div>
                        
                        
                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg">
                                {% trans "Sign In" %}
                            </button>
                        </div>
                        
                        <div class="text-center">
                            <a href="{% url 'accounts:password_reset' %}" class="text-decoration-none">
                                {% trans "Forgot your password?" %}
                            </a>
                        </div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="mb-2">{% trans "Don't have an account?" %}</p>
                        <a href="{% url 'accounts:signup' %}" class="btn btn-outline-primary">
                            {% trans "Sign Up" %}
                        </a>
                        <a href="{% url 'accounts:psychologist_signup' %}" class="btn btn-outline-success ms-2">
                            {% trans "Join as Psychologist" %}
                        </a>
                    </div>
                </div>
            </div>

            <!-- Demo Accounts -->
            <div class="demo-accounts mt-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">{% trans "Demo Accounts (Click to auto-fill)" %}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="demo-account p-2 border rounded mb-2" style="cursor: pointer;" onclick="quickLogin('admin', 'admin123')">
                                    <h6 class="text-primary mb-1">{% trans "Admin" %}</h6>
                                    <small><strong>Username:</strong> admin<br><strong>Password:</strong> admin123</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="demo-account p-2 border rounded mb-2" style="cursor: pointer;" onclick="quickLogin('dr_sarah_jones', 'password123')">
                                    <h6 class="text-success mb-1">{% trans "Psychologist" %}</h6>
                                    <small><strong>Username:</strong> dr_sarah_jones<br><strong>Password:</strong> password123</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="demo-account p-2 border rounded mb-2" style="cursor: pointer;" onclick="quickLogin('john_doe', 'password123')">
                                    <h6 class="text-info mb-1">{% trans "User" %}</h6>
                                    <small><strong>Username:</strong> john_doe<br><strong>Password:</strong> password123</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function quickLogin(username, password) {
    document.getElementById('{{ form.username.id_for_label }}').value = username;
    document.getElementById('{{ form.password.id_for_label }}').value = password;
}

// Add hover effects to demo accounts
document.addEventListener('DOMContentLoaded', function() {
    const demoAccounts = document.querySelectorAll('.demo-account');
    demoAccounts.forEach(account => {
        account.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });
        account.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
});
</script>
{% endblock %}
