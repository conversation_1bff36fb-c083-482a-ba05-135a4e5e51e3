{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Password Reset Sent" %} - ECPI{% endblock %}

{% block extra_css %}
<link href="{% static 'css/auth.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="auth-card text-center">
                    <!-- Success Icon -->
                    <div class="mb-4">
                        <div class="success-icon">
                            <i class="fas fa-check-circle fa-5x text-success mb-3"></i>
                        </div>
                    </div>

                    <!-- Header -->
                    <div class="auth-header mb-4">
                        <h2 class="auth-title">{% trans "Check Your Email" %}</h2>
                        <p class="auth-subtitle text-muted">
                            {% trans "We've sent password reset instructions to your email address." %}
                        </p>
                    </div>

                    <!-- Instructions -->
                    <div class="instructions mb-4">
                        <div class="alert alert-info">
                            <h6 class="alert-heading">
                                <i class="fas fa-info-circle me-2"></i>{% trans "What's Next?" %}
                            </h6>
                            <ol class="text-start mb-0">
                                <li>{% trans "Check your email inbox (and spam folder)" %}</li>
                                <li>{% trans "Click the reset link in the email" %}</li>
                                <li>{% trans "Create a new password" %}</li>
                                <li>{% trans "Sign in with your new password" %}</li>
                            </ol>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="d-grid gap-2 mb-4">
                        <a href="{% url 'accounts:login' %}" class="btn btn-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>{% trans "Back to Sign In" %}
                        </a>
                        <button type="button" class="btn btn-outline-secondary" onclick="window.location.reload()">
                            <i class="fas fa-redo me-2"></i>{% trans "Resend Email" %}
                        </button>
                    </div>

                    <!-- Help -->
                    <div class="auth-footer">
                        <p class="text-muted small">
                            {% trans "Didn't receive the email?" %} 
                            <a href="{% url 'core:contact' %}" class="auth-link">{% trans "Contact Support" %}</a>
                        </p>
                    </div>
                </div>

                <!-- Additional Info -->
                <div class="auth-info mt-4">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="text-center mb-3">
                                <i class="fas fa-shield-alt text-success me-2"></i>{% trans "Security Notice" %}
                            </h6>
                            <ul class="list-unstyled small text-muted mb-0">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    {% trans "The reset link will expire in 24 hours" %}
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    {% trans "The link can only be used once" %}
                                </li>
                                <li class="mb-0">
                                    <i class="fas fa-check text-success me-2"></i>
                                    {% trans "Your account remains secure during this process" %}
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
