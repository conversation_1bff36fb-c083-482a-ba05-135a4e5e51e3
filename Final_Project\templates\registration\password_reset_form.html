{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Reset Password" %} - ECPI{% endblock %}

{% block extra_css %}
<link href="{% static 'css/auth.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="auth-card">
                    <!-- Header -->
                    <div class="auth-header text-center mb-4">
                        <img src="{% static 'images/logo.png' %}" alt="ECPI Logo" height="60" class="mb-3"
                             onerror="this.style.display='none';">
                        <h2 class="auth-title">{% trans "Reset Your Password" %}</h2>
                        <p class="auth-subtitle text-muted">
                            {% trans "Enter your email address and we'll send you a link to reset your password." %}
                        </p>
                    </div>

                    <!-- Password Reset Form -->
                    <form method="post" class="auth-form">
                        {% csrf_token %}
                        
                        <!-- Display form errors -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}

                        <!-- Email Field -->
                        <div class="form-group mb-4">
                            <label for="{{ form.email.id_for_label }}" class="form-label">
                                <i class="fas fa-envelope me-2"></i>{% trans "Email Address" %}
                            </label>
                            <input type="email" 
                                   class="form-control {% if form.email.errors %}is-invalid{% endif %}" 
                                   id="{{ form.email.id_for_label }}"
                                   name="{{ form.email.name }}" 
                                   value="{{ form.email.value|default:'' }}"
                                   placeholder="{% trans 'Enter your email address' %}"
                                   required
                                   autofocus>
                            {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {{ form.email.errors.0 }}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                {% trans "We'll send password reset instructions to this email address." %}
                            </small>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid mb-4">
                            <button type="submit" class="btn btn-primary btn-lg auth-submit-btn">
                                <i class="fas fa-paper-plane me-2"></i>{% trans "Send Reset Link" %}
                            </button>
                        </div>
                    </form>

                    <!-- Back to Login -->
                    <div class="auth-footer text-center">
                        <p class="mb-0">
                            {% trans "Remember your password?" %} 
                            <a href="{% url 'accounts:login' %}" class="auth-link">{% trans "Back to Sign In" %}</a>
                        </p>
                    </div>
                </div>

                <!-- Help Info -->
                <div class="auth-info mt-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <i class="fas fa-question-circle fa-2x text-info mb-3"></i>
                            <h6>{% trans "Need Help?" %}</h6>
                            <p class="text-muted mb-3">
                                {% trans "If you're having trouble resetting your password, please contact our support team." %}
                            </p>
                            <a href="{% url 'core:contact' %}" class="btn btn-outline-info">
                                <i class="fas fa-envelope me-2"></i>{% trans "Contact Support" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
