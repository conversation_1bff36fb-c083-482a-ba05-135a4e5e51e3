{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Sign Up" %} - ECPI{% endblock %}

{% block extra_css %}
<link href="{% static 'css/auth.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="auth-card">
                    <!-- Header -->
                    <div class="auth-header text-center mb-4">
                        <img src="{% static 'images/logo.png' %}" alt="ECPI Logo" height="60" class="mb-3"
                             onerror="this.style.display='none';">
                        <h2 class="auth-title">{% trans "Create Your Account" %}</h2>
                        <p class="auth-subtitle text-muted">
                            {% trans "Join ECPI to access professional psychological consultation services" %}
                        </p>
                    </div>

                    <!-- Signup Form -->
                    <form method="post" class="auth-form" id="signup-form">
                        {% csrf_token %}
                        
                        <!-- Display form errors -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}

                        <!-- Username Field -->
                        <div class="form-group mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">
                                <i class="fas fa-user me-2"></i>{% trans "Username" %}
                            </label>
                            <input type="text" 
                                   class="form-control {% if form.username.errors %}is-invalid{% endif %}" 
                                   id="{{ form.username.id_for_label }}"
                                   name="{{ form.username.name }}" 
                                   value="{{ form.username.value|default:'' }}"
                                   placeholder="{% trans 'Enter your username' %}"
                                   required>
                            {% if form.username.errors %}
                                <div class="invalid-feedback">
                                    {{ form.username.errors.0 }}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                {% trans "Letters, digits and @/./+/-/_ only. 150 characters or fewer." %}
                            </small>
                        </div>

                        <!-- Email Field -->
                        <div class="form-group mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">
                                <i class="fas fa-envelope me-2"></i>{% trans "Email Address" %}
                            </label>
                            <input type="email" 
                                   class="form-control {% if form.email.errors %}is-invalid{% endif %}" 
                                   id="{{ form.email.id_for_label }}"
                                   name="{{ form.email.name }}" 
                                   value="{{ form.email.value|default:'' }}"
                                   placeholder="{% trans 'Enter your email address' %}"
                                   required>
                            {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {{ form.email.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <!-- First Name Field -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                        {% trans "First Name" %}
                                    </label>
                                    <input type="text" 
                                           class="form-control {% if form.first_name.errors %}is-invalid{% endif %}" 
                                           id="{{ form.first_name.id_for_label }}"
                                           name="{{ form.first_name.name }}" 
                                           value="{{ form.first_name.value|default:'' }}"
                                           placeholder="{% trans 'First name' %}">
                                    {% if form.first_name.errors %}
                                        <div class="invalid-feedback">
                                            {{ form.first_name.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                        {% trans "Last Name" %}
                                    </label>
                                    <input type="text" 
                                           class="form-control {% if form.last_name.errors %}is-invalid{% endif %}" 
                                           id="{{ form.last_name.id_for_label }}"
                                           name="{{ form.last_name.name }}" 
                                           value="{{ form.last_name.value|default:'' }}"
                                           placeholder="{% trans 'Last name' %}">
                                    {% if form.last_name.errors %}
                                        <div class="invalid-feedback">
                                            {{ form.last_name.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Password Field -->
                        <div class="form-group mb-3">
                            <label for="{{ form.password1.id_for_label }}" class="form-label">
                                <i class="fas fa-lock me-2"></i>{% trans "Password" %}
                            </label>
                            <div class="password-input-group">
                                <input type="password" 
                                       class="form-control {% if form.password1.errors %}is-invalid{% endif %}" 
                                       id="{{ form.password1.id_for_label }}"
                                       name="{{ form.password1.name }}" 
                                       placeholder="{% trans 'Enter your password' %}"
                                       required>
                                <button type="button" class="password-toggle" onclick="togglePassword('{{ form.password1.id_for_label }}')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            {% if form.password1.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.password1.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Confirm Password Field -->
                        <div class="form-group mb-4">
                            <label for="{{ form.password2.id_for_label }}" class="form-label">
                                <i class="fas fa-lock me-2"></i>{% trans "Confirm Password" %}
                            </label>
                            <div class="password-input-group">
                                <input type="password" 
                                       class="form-control {% if form.password2.errors %}is-invalid{% endif %}" 
                                       id="{{ form.password2.id_for_label }}"
                                       name="{{ form.password2.name }}" 
                                       placeholder="{% trans 'Confirm your password' %}"
                                       required>
                                <button type="button" class="password-toggle" onclick="togglePassword('{{ form.password2.id_for_label }}')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            {% if form.password2.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.password2.errors.0 }}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="terms-checkbox" required>
                            <label class="form-check-label" for="terms-checkbox">
                                {% trans "I agree to the" %} 
                                <a href="{% url 'core:terms' %}" target="_blank">{% trans "Terms of Service" %}</a> 
                                {% trans "and" %} 
                                <a href="{% url 'core:privacy' %}" target="_blank">{% trans "Privacy Policy" %}</a>
                            </label>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid mb-4">
                            <button type="submit" class="btn btn-primary btn-lg auth-submit-btn">
                                <i class="fas fa-user-plus me-2"></i>{% trans "Create Account" %}
                            </button>
                        </div>

                        <!-- Social Login Options -->
                        <div class="auth-divider">
                            <span>{% trans "or" %}</span>
                        </div>

                        <div class="social-login mb-4">
                            <button type="button" class="btn btn-outline-danger btn-social">
                                <i class="fab fa-google me-2"></i>{% trans "Sign up with Google" %}
                            </button>
                        </div>
                    </form>

                    <!-- User Type Selection -->
                    <div class="user-type-selection mt-4">
                        <div class="text-center mb-3">
                            <h6 class="text-muted">{% trans "Or join as a professional" %}</h6>
                        </div>
                        <div class="d-grid">
                            <a href="{% url 'accounts:psychologist_signup' %}" class="btn btn-outline-success btn-lg">
                                <i class="fas fa-user-md me-2"></i>{% trans "Sign Up as Psychologist" %}
                            </a>
                        </div>
                    </div>

                    <!-- Login Link -->
                    <div class="auth-footer text-center mt-4">
                        <p class="mb-0">
                            {% trans "Already have an account?" %}
                            <a href="{% url 'accounts:login' %}" class="auth-link">{% trans "Sign In" %}</a>
                        </p>
                    </div>
                </div>


            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/auth.js' %}"></script>
<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const toggle = field.nextElementSibling.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        toggle.classList.remove('fa-eye');
        toggle.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        toggle.classList.remove('fa-eye-slash');
        toggle.classList.add('fa-eye');
    }
}

// Form validation
document.getElementById('signup-form').addEventListener('submit', function(e) {
    const password1 = document.getElementById('{{ form.password1.id_for_label }}').value;
    const password2 = document.getElementById('{{ form.password2.id_for_label }}').value;
    const termsCheckbox = document.getElementById('terms-checkbox');
    
    if (!termsCheckbox.checked) {
        e.preventDefault();
        alert('{% trans "Please accept the Terms of Service and Privacy Policy" %}');
        return false;
    }
    
    if (password1 !== password2) {
        e.preventDefault();
        alert('{% trans "Passwords do not match" %}');
        return false;
    }
});
</script>
{% endblock %}
