{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Delete Resource" %} - {{ object.title }} - ECPI{% endblock %}

{% block extra_css %}
<style>
    .delete-section {
        background: #f8f9fa;
        padding: 4rem 0;
        min-height: 100vh;
    }
    
    .delete-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    
    .delete-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        padding: 2rem;
        text-align: center;
    }
    
    .resource-preview {
        background: #fff;
        border: 1px solid #dee2e6;
        border-radius: 10px;
        padding: 1.5rem;
        margin: 2rem 0;
    }
    
    .resource-meta {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
    }
    
    .resource-type-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .type-article { background: #e3f2fd; color: #1976d2; }
    .type-video { background: #fce4ec; color: #c2185b; }
    .type-audio { background: #f3e5f5; color: #7b1fa2; }
    .type-document { background: #e8f5e8; color: #388e3c; }
    .type-infographic { background: #fff3e0; color: #f57c00; }
    .type-quiz { background: #e1f5fe; color: #0277bd; }
    .type-worksheet { background: #f1f8e9; color: #689f38; }
    
    .difficulty-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 600;
    }
    
    .difficulty-beginner { background: #c8e6c9; color: #2e7d32; }
    .difficulty-intermediate { background: #fff3c4; color: #f57f17; }
    .difficulty-advanced { background: #ffcdd2; color: #c62828; }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 1rem;
        margin: 1.5rem 0;
    }
    
    .stats-item {
        text-align: center;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 8px;
    }
    
    .stats-number {
        font-size: 1.5rem;
        font-weight: bold;
        color: #dc3545;
    }
    
    .warning-box {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 2rem 0;
    }
    
    .warning-icon {
        color: #856404;
        font-size: 2rem;
        margin-bottom: 1rem;
    }
    
    .btn-delete {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-delete:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
    }
    
    .btn-cancel {
        background: #6c757d;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-cancel:hover {
        background: #5a6268;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
    }
</style>
{% endblock %}

{% block content %}
<section class="delete-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card delete-card">
                    <div class="delete-header">
                        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                        <h1 class="mb-0">{% trans "Delete Resource" %}</h1>
                        <p class="mb-0 mt-2 opacity-75">{% trans "This action cannot be undone" %}</p>
                    </div>
                    
                    <div class="card-body p-4">
                        <!-- Resource Preview -->
                        <div class="resource-preview">
                            <div class="resource-meta">
                                <span class="resource-type-badge type-{{ object.resource_type }}">
                                    {% if object.resource_type == 'article' %}
                                        <i class="fas fa-newspaper me-1"></i>
                                    {% elif object.resource_type == 'video' %}
                                        <i class="fas fa-video me-1"></i>
                                    {% elif object.resource_type == 'audio' %}
                                        <i class="fas fa-music me-1"></i>
                                    {% elif object.resource_type == 'document' %}
                                        <i class="fas fa-file-pdf me-1"></i>
                                    {% elif object.resource_type == 'infographic' %}
                                        <i class="fas fa-chart-bar me-1"></i>
                                    {% elif object.resource_type == 'quiz' %}
                                        <i class="fas fa-question-circle me-1"></i>
                                    {% elif object.resource_type == 'worksheet' %}
                                        <i class="fas fa-file-alt me-1"></i>
                                    {% endif %}
                                    {{ object.get_resource_type_display }}
                                </span>
                                <span class="difficulty-badge difficulty-{{ object.difficulty_level }}">
                                    {{ object.get_difficulty_level_display }}
                                </span>
                                {% if object.is_featured %}
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-star me-1"></i>{% trans "Featured" %}
                                    </span>
                                {% endif %}
                            </div>
                            
                            <h3 class="mb-3">{{ object.title }}</h3>
                            <p class="text-muted mb-3">{{ object.description }}</p>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>
                                        <strong>{% trans "Author:" %}</strong> {{ object.author.get_full_name|default:object.author.username }}
                                    </small>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <i class="fas fa-folder me-1"></i>
                                        <strong>{% trans "Category:" %}</strong> {{ object.category.name }}
                                    </small>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        <strong>{% trans "Created:" %}</strong> {{ object.created_at|date:"M d, Y" }}
                                    </small>
                                </div>
                                <div class="col-md-6">
                                    <small class="text-muted">
                                        <i class="fas fa-edit me-1"></i>
                                        <strong>{% trans "Updated:" %}</strong> {{ object.updated_at|date:"M d, Y" }}
                                    </small>
                                </div>
                            </div>
                            
                            <!-- Resource Statistics -->
                            <div class="stats-grid">
                                <div class="stats-item">
                                    <div class="stats-number">{{ object.view_count }}</div>
                                    <div class="small text-muted">{% trans "Views" %}</div>
                                </div>
                                <div class="stats-item">
                                    <div class="stats-number">{{ object.like_count }}</div>
                                    <div class="small text-muted">{% trans "Likes" %}</div>
                                </div>
                                <div class="stats-item">
                                    <div class="stats-number">{{ object.download_count }}</div>
                                    <div class="small text-muted">{% trans "Downloads" %}</div>
                                </div>
                            </div>
                            
                            <!-- Tags -->
                            {% if object.tags %}
                                <div class="mt-3">
                                    <strong class="small text-muted">{% trans "Tags:" %}</strong>
                                    {% for tag in object.tags|split:"," %}
                                        <span class="badge bg-secondary me-1">{{ tag|trim }}</span>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Warning Box -->
                        <div class="warning-box text-center">
                            <i class="fas fa-exclamation-triangle warning-icon"></i>
                            <h5 class="text-warning mb-3">{% trans "Warning: Permanent Deletion" %}</h5>
                            <p class="mb-3">
                                {% trans "Are you sure you want to delete this resource? This action will:" %}
                            </p>
                            <ul class="list-unstyled text-start">
                                <li class="mb-2">
                                    <i class="fas fa-times text-danger me-2"></i>
                                    {% trans "Permanently remove the resource from the system" %}
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-times text-danger me-2"></i>
                                    {% trans "Delete all associated files (images, videos, documents)" %}
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-times text-danger me-2"></i>
                                    {% trans "Remove all user likes and view history" %}
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-times text-danger me-2"></i>
                                    {% trans "Make the resource inaccessible to all users" %}
                                </li>
                            </ul>
                            <p class="text-muted mt-3">
                                <strong>{% trans "This action cannot be undone!" %}</strong>
                            </p>
                        </div>
                        
                        <!-- Confirmation Form -->
                        <form method="post" class="text-center">
                            {% csrf_token %}
                            <div class="mb-4">
                                <div class="form-check d-inline-block">
                                    <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                                    <label class="form-check-label" for="confirmDelete">
                                        {% trans "I understand that this action is permanent and cannot be undone" %}
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-danger btn-delete me-3" id="deleteButton" disabled>
                                <i class="fas fa-trash me-2"></i>
                                {% trans "Yes, Delete Resource" %}
                            </button>
                            <a href="{{ object.get_absolute_url }}" class="btn btn-secondary btn-cancel">
                                <i class="fas fa-arrow-left me-2"></i>
                                {% trans "Cancel" %}
                            </a>
                        </form>
                        
                        <!-- Alternative Actions -->
                        <div class="text-center mt-4 pt-4 border-top">
                            <p class="text-muted mb-3">{% trans "Instead of deleting, you might want to:" %}</p>
                            <a href="{% url 'resources:edit_resource' object.slug %}" class="btn btn-outline-primary btn-sm me-2">
                                <i class="fas fa-edit me-1"></i>
                                {% trans "Edit Resource" %}
                            </a>
                            <button class="btn btn-outline-secondary btn-sm" onclick="unpublishResource()">
                                <i class="fas fa-eye-slash me-1"></i>
                                {% trans "Unpublish Instead" %}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const confirmCheckbox = document.getElementById('confirmDelete');
    const deleteButton = document.getElementById('deleteButton');
    
    confirmCheckbox.addEventListener('change', function() {
        deleteButton.disabled = !this.checked;
        if (this.checked) {
            deleteButton.classList.remove('btn-secondary');
            deleteButton.classList.add('btn-danger');
        } else {
            deleteButton.classList.remove('btn-danger');
            deleteButton.classList.add('btn-secondary');
        }
    });
});

function unpublishResource() {
    if (confirm('{% trans "Do you want to unpublish this resource instead of deleting it?" %}')) {
        // Create a form to unpublish the resource
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{% url "resources:edit_resource" object.slug %}';
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = 'csrfmiddlewaretoken';
        csrfToken.value = '{{ csrf_token }}';
        
        const isPublished = document.createElement('input');
        isPublished.type = 'hidden';
        isPublished.name = 'is_published';
        isPublished.value = 'false';
        
        form.appendChild(csrfToken);
        form.appendChild(isPublished);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
