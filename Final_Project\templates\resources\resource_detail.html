{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ resource.title }} - ECPI{% endblock %}

{% block meta_description %}{{ resource.meta_description|default:resource.description|truncatewords:30 }}{% endblock %}

{% block extra_css %}
<style>
    .resource-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 4rem 0;
    }
    
    .resource-meta {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 1.5rem;
        margin-top: 2rem;
    }
    
    .resource-content {
        padding: 3rem 0;
    }
    
    .resource-sidebar {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .resource-actions {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }
    
    .btn-action {
        border-radius: 25px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        margin: 0.25rem;
        transition: all 0.3s ease;
    }
    
    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    .resource-type-badge {
        display: inline-block;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .type-article { background: #e3f2fd; color: #1976d2; }
    .type-video { background: #fce4ec; color: #c2185b; }
    .type-audio { background: #f3e5f5; color: #7b1fa2; }
    .type-document { background: #e8f5e8; color: #388e3c; }
    .type-infographic { background: #fff3e0; color: #f57c00; }
    .type-quiz { background: #e1f5fe; color: #0277bd; }
    .type-worksheet { background: #f1f8e9; color: #689f38; }
    
    .difficulty-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 600;
    }
    
    .difficulty-beginner { background: #c8e6c9; color: #2e7d32; }
    .difficulty-intermediate { background: #fff3c4; color: #f57f17; }
    .difficulty-advanced { background: #ffcdd2; color: #c62828; }
    
    .stats-item {
        text-align: center;
        padding: 1rem;
        border-radius: 10px;
        background: white;
        margin: 0.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .stats-number {
        font-size: 1.5rem;
        font-weight: bold;
        color: #667eea;
    }
    
    .related-resource {
        border: none;
        border-radius: 10px;
        overflow: hidden;
        transition: all 0.3s ease;
        margin-bottom: 1rem;
    }
    
    .related-resource:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }
    
    .video-container {
        position: relative;
        width: 100%;
        height: 0;
        padding-bottom: 56.25%; /* 16:9 aspect ratio */
        margin-bottom: 2rem;
    }
    
    .video-container iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 10px;
    }
    
    .audio-player {
        width: 100%;
        margin-bottom: 2rem;
    }
    
    .document-preview {
        background: #f8f9fa;
        border: 2px dashed #dee2e6;
        border-radius: 10px;
        padding: 3rem;
        text-align: center;
        margin-bottom: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Resource Header -->
<section class="resource-header">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <div class="d-flex align-items-center mb-3">
                    <span class="resource-type-badge type-{{ resource.resource_type }}">
                        {% if resource.resource_type == 'article' %}
                            <i class="fas fa-newspaper me-1"></i>
                        {% elif resource.resource_type == 'video' %}
                            <i class="fas fa-video me-1"></i>
                        {% elif resource.resource_type == 'audio' %}
                            <i class="fas fa-music me-1"></i>
                        {% elif resource.resource_type == 'document' %}
                            <i class="fas fa-file-pdf me-1"></i>
                        {% elif resource.resource_type == 'infographic' %}
                            <i class="fas fa-chart-bar me-1"></i>
                        {% elif resource.resource_type == 'quiz' %}
                            <i class="fas fa-question-circle me-1"></i>
                        {% elif resource.resource_type == 'worksheet' %}
                            <i class="fas fa-file-alt me-1"></i>
                        {% endif %}
                        {{ resource.get_resource_type_display }}
                    </span>
                    <span class="difficulty-badge difficulty-{{ resource.difficulty_level }} ms-2">
                        {{ resource.get_difficulty_level_display }}
                    </span>
                </div>
                
                <h1 class="display-5 fw-bold mb-3">{{ resource.title }}</h1>
                <p class="lead">{{ resource.description }}</p>
                
                <div class="resource-meta">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-user me-2"></i>
                                <span>{% trans "By" %} {{ resource.author.get_full_name|default:resource.author.username }}</span>
                            </div>
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-folder me-2"></i>
                                <span>{{ resource.category.name }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-calendar me-2"></i>
                                <span>{{ resource.created_at|date:"M d, Y" }}</span>
                            </div>
                            {% if resource.duration_minutes %}
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-clock me-2"></i>
                                    <span>{{ resource.duration_minutes }} {% trans "minutes" %}</span>
                                </div>
                            {% elif resource.reading_time_minutes %}
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fas fa-clock me-2"></i>
                                    <span>{{ resource.reading_time_minutes }} {% trans "min read" %}</span>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="row">
                    <div class="col-4">
                        <div class="stats-item">
                            <div class="stats-number">{{ resource.view_count }}</div>
                            <div class="small text-muted">{% trans "Views" %}</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stats-item">
                            <div class="stats-number">{{ resource.like_count }}</div>
                            <div class="small text-muted">{% trans "Likes" %}</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="stats-item">
                            <div class="stats-number">{{ resource.download_count }}</div>
                            <div class="small text-muted">{% trans "Downloads" %}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Resource Content -->
<section class="resource-content">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <!-- Featured Image -->
                {% if resource.featured_image %}
                    <div class="mb-4">
                        <img src="{{ resource.featured_image.url }}" alt="{{ resource.title }}" 
                             class="img-fluid rounded shadow">
                    </div>
                {% endif %}
                
                <!-- Video Content -->
                {% if resource.resource_type == 'video' %}
                    {% if resource.youtube_url %}
                        <div class="video-container">
                            {% with resource.youtube_url|slice:"32:" as video_id %}
                                <iframe src="https://www.youtube.com/embed/{{ video_id }}" 
                                        frameborder="0" allowfullscreen></iframe>
                            {% endwith %}
                        </div>
                    {% elif resource.video_file %}
                        <div class="mb-4">
                            <video controls class="w-100 rounded">
                                <source src="{{ resource.video_file.url }}" type="video/mp4">
                                {% trans "Your browser does not support the video tag." %}
                            </video>
                        </div>
                    {% endif %}
                {% endif %}
                
                <!-- Audio Content -->
                {% if resource.resource_type == 'audio' and resource.audio_file %}
                    <div class="mb-4">
                        <audio controls class="audio-player">
                            <source src="{{ resource.audio_file.url }}" type="audio/mpeg">
                            {% trans "Your browser does not support the audio element." %}
                        </audio>
                    </div>
                {% endif %}
                
                <!-- Document Preview -->
                {% if resource.resource_type == 'document' and resource.document_file %}
                    <div class="document-preview">
                        <i class="fas fa-file-pdf fa-4x text-muted mb-3"></i>
                        <h5>{{ resource.title }}</h5>
                        <p class="text-muted">{% trans "Click download to view this document" %}</p>
                    </div>
                {% endif %}
                
                <!-- Article Content -->
                {% if resource.content %}
                    <div class="content-body">
                        {{ resource.content|linebreaks }}
                    </div>
                {% endif %}
                
                <!-- External Link -->
                {% if resource.external_url %}
                    <div class="alert alert-info">
                        <i class="fas fa-external-link-alt me-2"></i>
                        <strong>{% trans "External Resource:" %}</strong>
                        <a href="{{ resource.external_url }}" target="_blank" class="alert-link">
                            {{ resource.external_url }}
                        </a>
                    </div>
                {% endif %}
                
                <!-- Tags -->
                {% if resource.tags %}
                    <div class="mt-4">
                        <h6>{% trans "Tags:" %}</h6>
                        {% for tag in resource.tags|split:"," %}
                            <span class="badge bg-secondary me-1">{{ tag|trim }}</span>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Actions -->
                <div class="resource-actions">
                    <h5 class="mb-3">{% trans "Actions" %}</h5>
                    
                    {% if user.is_authenticated %}
                        <!-- Like Button -->
                        <form method="post" action="{% url 'resources:like_resource' resource.slug %}" class="d-inline">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-action {% if user_has_liked %}btn-danger{% else %}btn-outline-danger{% endif %}">
                                <i class="fas fa-heart me-1"></i>
                                {% if user_has_liked %}{% trans "Unlike" %}{% else %}{% trans "Like" %}{% endif %}
                            </button>
                        </form>
                    {% endif %}
                    
                    <!-- Download Button -->
                    {% if resource.document_file or resource.video_file or resource.audio_file %}
                        <a href="{% url 'resources:download_resource' resource.slug %}" 
                           class="btn btn-action btn-primary">
                            <i class="fas fa-download me-1"></i>
                            {% trans "Download" %}
                        </a>
                    {% endif %}
                    
                    <!-- Share Button -->
                    <button class="btn btn-action btn-outline-primary" onclick="shareResource()">
                        <i class="fas fa-share me-1"></i>
                        {% trans "Share" %}
                    </button>
                    
                    <!-- Edit/Delete (for author) -->
                    {% if user == resource.author or user.is_staff %}
                        <div class="mt-3 pt-3 border-top">
                            <a href="{% url 'resources:edit_resource' resource.slug %}" 
                               class="btn btn-action btn-outline-warning btn-sm">
                                <i class="fas fa-edit me-1"></i>
                                {% trans "Edit" %}
                            </a>
                            <a href="{% url 'resources:delete_resource' resource.slug %}" 
                               class="btn btn-action btn-outline-danger btn-sm"
                               onclick="return confirm('{% trans "Are you sure you want to delete this resource?" %}')">
                                <i class="fas fa-trash me-1"></i>
                                {% trans "Delete" %}
                            </a>
                        </div>
                    {% endif %}
                </div>
                
                <!-- Resource Info -->
                <div class="resource-sidebar">
                    <h5 class="mb-3">{% trans "Resource Information" %}</h5>
                    
                    <div class="mb-3">
                        <strong>{% trans "Category:" %}</strong><br>
                        <a href="{% url 'resources:category_resources' resource.category.name|lower %}" 
                           class="text-decoration-none">
                            {{ resource.category.name }}
                        </a>
                    </div>
                    
                    <div class="mb-3">
                        <strong>{% trans "Type:" %}</strong><br>
                        {{ resource.get_resource_type_display }}
                    </div>
                    
                    <div class="mb-3">
                        <strong>{% trans "Difficulty:" %}</strong><br>
                        {{ resource.get_difficulty_level_display }}
                    </div>
                    
                    <div class="mb-3">
                        <strong>{% trans "Author:" %}</strong><br>
                        {{ resource.author.get_full_name|default:resource.author.username }}
                    </div>
                    
                    <div class="mb-3">
                        <strong>{% trans "Published:" %}</strong><br>
                        {{ resource.created_at|date:"F d, Y" }}
                    </div>
                    
                    {% if resource.duration_minutes or resource.reading_time_minutes %}
                        <div class="mb-3">
                            <strong>
                                {% if resource.duration_minutes %}
                                    {% trans "Duration:" %}
                                {% else %}
                                    {% trans "Reading Time:" %}
                                {% endif %}
                            </strong><br>
                            {% if resource.duration_minutes %}
                                {{ resource.duration_minutes }} {% trans "minutes" %}
                            {% else %}
                                {{ resource.reading_time_minutes }} {% trans "minutes" %}
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
                
                <!-- Related Resources -->
                {% if related_resources %}
                    <div class="resource-sidebar">
                        <h5 class="mb-3">{% trans "Related Resources" %}</h5>
                        {% for related in related_resources %}
                            <div class="card related-resource">
                                <div class="card-body p-3">
                                    <h6 class="card-title mb-2">
                                        <a href="{{ related.get_absolute_url }}" class="text-decoration-none">
                                            {{ related.title|truncatechars:50 }}
                                        </a>
                                    </h6>
                                    <p class="card-text small text-muted mb-2">
                                        {{ related.description|truncatechars:80 }}
                                    </p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-eye me-1"></i>{{ related.view_count }}
                                        </small>
                                        <span class="badge bg-light text-dark">
                                            {{ related.get_resource_type_display }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
function shareResource() {
    if (navigator.share) {
        navigator.share({
            title: '{{ resource.title|escapejs }}',
            text: '{{ resource.description|escapejs }}',
            url: window.location.href
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(window.location.href).then(function() {
            alert('{% trans "Link copied to clipboard!" %}');
        });
    }
}
</script>
{% endblock %}
