{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}
    {% if object %}
        {% trans "Edit Resource" %} - {{ object.title }}
    {% else %}
        {% trans "Upload Resource" %}
    {% endif %} - ECPI
{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        background: #f8f9fa;
        padding: 3rem 0;
        min-height: 100vh;
    }
    
    .form-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 15px 15px 0 0;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }
    
    .form-control, .form-select {
        border-radius: 8px;
        border: 2px solid #e9ecef;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .file-upload-area {
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        background: #f8f9fa;
    }
    
    .file-upload-area:hover {
        border-color: #667eea;
        background: #f0f2ff;
    }
    
    .btn-submit {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }
    
    .section-header {
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 1rem;
        margin-bottom: 2rem;
    }
    
    .required-field::after {
        content: " *";
        color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<section class="form-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card form-card">
                    <div class="form-header text-center">
                        <h1 class="mb-0">
                            <i class="fas fa-{% if object %}edit{% else %}cloud-upload-alt{% endif %} me-3"></i>
                            {% if object %}
                                {% trans "Edit Resource" %}
                            {% else %}
                                {% trans "Upload New Resource" %}
                            {% endif %}
                        </h1>
                        {% if object %}
                            <p class="mb-0 mt-2 opacity-75">{{ object.title }}</p>
                        {% else %}
                            <p class="mb-0 mt-2 opacity-75">{% trans "Share your knowledge with the community" %}</p>
                        {% endif %}
                    </div>
                    
                    <div class="card-body p-5">
                        <form method="post" enctype="multipart/form-data" id="resourceForm">
                            {% csrf_token %}
                            
                            <!-- Basic Information -->
                            <div class="section-header">
                                <h4>
                                    <i class="fas fa-info-circle text-primary me-2"></i>
                                    {% trans "Basic Information" %}
                                </h4>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="form-group">
                                        <label for="{{ form.title.id_for_label }}" class="form-label required-field">
                                            {{ form.title.label }}
                                        </label>
                                        {{ form.title }}
                                        {% if form.title.errors %}
                                            <div class="text-danger small mt-1">{{ form.title.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="{{ form.category.id_for_label }}" class="form-label required-field">
                                            {{ form.category.label }}
                                        </label>
                                        {{ form.category }}
                                        {% if form.category.errors %}
                                            <div class="text-danger small mt-1">{{ form.category.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.resource_type.id_for_label }}" class="form-label required-field">
                                            {{ form.resource_type.label }}
                                        </label>
                                        {{ form.resource_type }}
                                        {% if form.resource_type.errors %}
                                            <div class="text-danger small mt-1">{{ form.resource_type.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.difficulty_level.id_for_label }}" class="form-label">
                                            {{ form.difficulty_level.label }}
                                        </label>
                                        {{ form.difficulty_level }}
                                        {% if form.difficulty_level.errors %}
                                            <div class="text-danger small mt-1">{{ form.difficulty_level.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="{{ form.description.id_for_label }}" class="form-label required-field">
                                    {{ form.description.label }}
                                </label>
                                {{ form.description }}
                                {% if form.description.errors %}
                                    <div class="text-danger small mt-1">{{ form.description.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="form-group">
                                <label for="{{ form.tags.id_for_label }}" class="form-label">
                                    {{ form.tags.label }}
                                </label>
                                {{ form.tags }}
                                <small class="form-text text-muted">{{ form.tags.help_text }}</small>
                                {% if form.tags.errors %}
                                    <div class="text-danger small mt-1">{{ form.tags.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <!-- Content Section -->
                            <div class="section-header">
                                <h4>
                                    <i class="fas fa-edit text-primary me-2"></i>
                                    {% trans "Content" %}
                                </h4>
                            </div>
                            
                            <div class="form-group" id="contentField">
                                <label for="{{ form.content.id_for_label }}" class="form-label">
                                    {{ form.content.label }}
                                </label>
                                {{ form.content }}
                                <small class="form-text text-muted">{{ form.content.help_text }}</small>
                                {% if form.content.errors %}
                                    <div class="text-danger small mt-1">{{ form.content.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <!-- Media Files -->
                            <div class="section-header">
                                <h4>
                                    <i class="fas fa-file-upload text-primary me-2"></i>
                                    {% trans "Media Files" %}
                                </h4>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.featured_image.id_for_label }}" class="form-label">
                                            {{ form.featured_image.label }}
                                        </label>
                                        <div class="file-upload-area">
                                            <i class="fas fa-image fa-2x text-muted mb-2"></i>
                                            <div>{{ form.featured_image }}</div>
                                            {% if object and object.featured_image %}
                                                <small class="text-muted d-block mt-2">
                                                    {% trans "Current:" %} {{ object.featured_image.name|slice:":30" }}
                                                </small>
                                            {% endif %}
                                        </div>
                                        {% if form.featured_image.errors %}
                                            <div class="text-danger small mt-1">{{ form.featured_image.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6" id="videoField">
                                    <div class="form-group">
                                        <label for="{{ form.video_file.id_for_label }}" class="form-label">
                                            {{ form.video_file.label }}
                                        </label>
                                        <div class="file-upload-area">
                                            <i class="fas fa-video fa-2x text-muted mb-2"></i>
                                            <div>{{ form.video_file }}</div>
                                            {% if object and object.video_file %}
                                                <small class="text-muted d-block mt-2">
                                                    {% trans "Current:" %} {{ object.video_file.name|slice:":30" }}
                                                </small>
                                            {% endif %}
                                        </div>
                                        {% if form.video_file.errors %}
                                            <div class="text-danger small mt-1">{{ form.video_file.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6" id="audioField">
                                    <div class="form-group">
                                        <label for="{{ form.audio_file.id_for_label }}" class="form-label">
                                            {{ form.audio_file.label }}
                                        </label>
                                        <div class="file-upload-area">
                                            <i class="fas fa-music fa-2x text-muted mb-2"></i>
                                            <div>{{ form.audio_file }}</div>
                                            {% if object and object.audio_file %}
                                                <small class="text-muted d-block mt-2">
                                                    {% trans "Current:" %} {{ object.audio_file.name|slice:":30" }}
                                                </small>
                                            {% endif %}
                                        </div>
                                        {% if form.audio_file.errors %}
                                            <div class="text-danger small mt-1">{{ form.audio_file.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6" id="documentField">
                                    <div class="form-group">
                                        <label for="{{ form.document_file.id_for_label }}" class="form-label">
                                            {{ form.document_file.label }}
                                        </label>
                                        <div class="file-upload-area">
                                            <i class="fas fa-file-pdf fa-2x text-muted mb-2"></i>
                                            <div>{{ form.document_file }}</div>
                                            {% if object and object.document_file %}
                                                <small class="text-muted d-block mt-2">
                                                    {% trans "Current:" %} {{ object.document_file.name|slice:":30" }}
                                                </small>
                                            {% endif %}
                                        </div>
                                        {% if form.document_file.errors %}
                                            <div class="text-danger small mt-1">{{ form.document_file.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            
                            <!-- External Links -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.external_url.id_for_label }}" class="form-label">
                                            {{ form.external_url.label }}
                                        </label>
                                        {{ form.external_url }}
                                        {% if form.external_url.errors %}
                                            <div class="text-danger small mt-1">{{ form.external_url.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6" id="youtubeField">
                                    <div class="form-group">
                                        <label for="{{ form.youtube_url.id_for_label }}" class="form-label">
                                            {{ form.youtube_url.label }}
                                        </label>
                                        {{ form.youtube_url }}
                                        {% if form.youtube_url.errors %}
                                            <div class="text-danger small mt-1">{{ form.youtube_url.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Metadata -->
                            <div class="section-header">
                                <h4>
                                    <i class="fas fa-clock text-primary me-2"></i>
                                    {% trans "Duration & Time" %}
                                </h4>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.duration_minutes.id_for_label }}" class="form-label">
                                            {{ form.duration_minutes.label }}
                                        </label>
                                        {{ form.duration_minutes }}
                                        <small class="form-text text-muted">{{ form.duration_minutes.help_text }}</small>
                                        {% if form.duration_minutes.errors %}
                                            <div class="text-danger small mt-1">{{ form.duration_minutes.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.reading_time_minutes.id_for_label }}" class="form-label">
                                            {{ form.reading_time_minutes.label }}
                                        </label>
                                        {{ form.reading_time_minutes }}
                                        <small class="form-text text-muted">{{ form.reading_time_minutes.help_text }}</small>
                                        {% if form.reading_time_minutes.errors %}
                                            <div class="text-danger small mt-1">{{ form.reading_time_minutes.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Publishing Options -->
                            <div class="section-header">
                                <h4>
                                    <i class="fas fa-cog text-primary me-2"></i>
                                    {% trans "Publishing Options" %}
                                </h4>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        {{ form.is_published }}
                                        <label class="form-check-label" for="{{ form.is_published.id_for_label }}">
                                            {{ form.is_published.label }}
                                        </label>
                                        <small class="form-text text-muted d-block">{{ form.is_published.help_text }}</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mb-3">
                                        {{ form.is_featured }}
                                        <label class="form-check-label" for="{{ form.is_featured.id_for_label }}">
                                            {{ form.is_featured.label }}
                                        </label>
                                        <small class="form-text text-muted d-block">{{ form.is_featured.help_text }}</small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Form Errors -->
                            {% if form.non_field_errors %}
                                <div class="alert alert-danger">
                                    {{ form.non_field_errors }}
                                </div>
                            {% endif %}
                            
                            <!-- Submit Buttons -->
                            <div class="text-center mt-5">
                                <button type="submit" class="btn btn-primary btn-submit me-3">
                                    <i class="fas fa-{% if object %}save{% else %}cloud-upload-alt{% endif %} me-2"></i>
                                    {% if object %}
                                        {% trans "Update Resource" %}
                                    {% else %}
                                        {% trans "Upload Resource" %}
                                    {% endif %}
                                </button>
                                <a href="{% url 'resources:resource_list' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>
                                    {% trans "Cancel" %}
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const resourceTypeField = document.getElementById('{{ form.resource_type.id_for_label }}');
    const contentField = document.getElementById('contentField');
    const videoField = document.getElementById('videoField');
    const audioField = document.getElementById('audioField');
    const documentField = document.getElementById('documentField');
    const youtubeField = document.getElementById('youtubeField');
    
    function toggleFields() {
        const resourceType = resourceTypeField.value;
        
        // Hide all optional fields first
        contentField.style.display = 'none';
        videoField.style.display = 'none';
        audioField.style.display = 'none';
        documentField.style.display = 'none';
        youtubeField.style.display = 'none';
        
        // Show relevant fields based on resource type
        switch(resourceType) {
            case 'article':
                contentField.style.display = 'block';
                break;
            case 'video':
                videoField.style.display = 'block';
                youtubeField.style.display = 'block';
                break;
            case 'audio':
                audioField.style.display = 'block';
                break;
            case 'document':
            case 'worksheet':
                documentField.style.display = 'block';
                break;
            case 'infographic':
                documentField.style.display = 'block';
                break;
        }
    }
    
    // Initial toggle
    toggleFields();
    
    // Toggle on change
    resourceTypeField.addEventListener('change', toggleFields);
});
</script>
{% endblock %}
