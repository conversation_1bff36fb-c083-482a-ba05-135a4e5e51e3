{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Resources" %} - ECPI{% endblock %}

{% block extra_css %}
<style>
    .resources-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 4rem 0;
    }

    .filter-section {
        background: #f8f9fa;
        padding: 2rem 0;
        border-bottom: 1px solid #dee2e6;
    }

    .filter-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .resource-card {
        border: none;
        border-radius: 15px;
        overflow: hidden;
        transition: all 0.3s ease;
        height: 100%;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .resource-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }

    .resource-image {
        height: 200px;
        object-fit: cover;
        width: 100%;
    }

    .resource-type-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .type-article { background: #e3f2fd; color: #1976d2; }
    .type-video { background: #fce4ec; color: #c2185b; }
    .type-audio { background: #f3e5f5; color: #7b1fa2; }
    .type-document { background: #e8f5e8; color: #388e3c; }
    .type-infographic { background: #fff3e0; color: #f57c00; }
    .type-quiz { background: #e1f5fe; color: #0277bd; }
    .type-worksheet { background: #f1f8e9; color: #689f38; }

    .difficulty-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .difficulty-beginner { background: #c8e6c9; color: #2e7d32; }
    .difficulty-intermediate { background: #fff3c4; color: #f57f17; }
    .difficulty-advanced { background: #ffcdd2; color: #c62828; }

    .stats-item {
        font-size: 0.875rem;
        color: #6c757d;
    }

    .btn-upload {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 25px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-upload:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #6c757d;
    }

    .search-highlight {
        background: #fff3cd;
        padding: 0.125rem 0.25rem;
        border-radius: 3px;
    }
</style>
{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="resources-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold mb-3">
                    <i class="fas fa-book-open me-3"></i>
                    {% trans "Mental Health Resources" %}
                </h1>
                <p class="lead">
                    {% trans "Educational materials, tools, and content to support your mental health journey" %}
                </p>
            </div>
            <div class="col-lg-4 text-end">
                {% if user.is_authenticated and user.psychologist_profile or user.is_staff %}
                    <a href="{% url 'resources:upload_resource' %}" class="btn btn-light btn-upload">
                        <i class="fas fa-plus me-2"></i>
                        {% trans "Upload Resource" %}
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
</section>

<!-- Filter Section -->
<section class="filter-section">
    <div class="container">
        <div class="card filter-card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">{% trans "Search" %}</label>
                        <input type="text" class="form-control" id="search" name="search"
                               value="{{ search_query }}" placeholder="{% trans 'Search resources...' %}">
                    </div>
                    <div class="col-md-2">
                        <label for="category" class="form-label">{% trans "Category" %}</label>
                        <select class="form-select" id="category" name="category">
                            <option value="">{% trans "All Categories" %}</option>
                            {% for cat in categories %}
                                <option value="{{ cat.name|lower }}"
                                        {% if selected_category == cat.name|lower %}selected{% endif %}>
                                    {{ cat.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="type" class="form-label">{% trans "Type" %}</label>
                        <select class="form-select" id="type" name="type">
                            <option value="">{% trans "All Types" %}</option>
                            {% for value, label in resource_types %}
                                <option value="{{ value }}" {% if selected_type == value %}selected{% endif %}>
                                    {{ label }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="difficulty" class="form-label">{% trans "Difficulty" %}</label>
                        <select class="form-select" id="difficulty" name="difficulty">
                            <option value="">{% trans "All Levels" %}</option>
                            {% for value, label in difficulty_levels %}
                                <option value="{{ value }}" {% if selected_difficulty == value %}selected{% endif %}>
                                    {{ label }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search me-1"></i>
                            {% trans "Filter" %}
                        </button>
                        <a href="{% url 'resources:resource_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Resources Section -->
<section class="py-5">
    <div class="container">
        {% if resources %}
            <!-- Results Info -->
            <div class="row mb-4">
                <div class="col-12">
                    <p class="text-muted">
                        {% blocktrans count counter=resources|length %}
                            Showing {{ counter }} resource
                        {% plural %}
                            Showing {{ counter }} resources
                        {% endblocktrans %}
                        {% if search_query %}
                            {% trans "for" %} "<strong>{{ search_query }}</strong>"
                        {% endif %}
                    </p>
                </div>
            </div>

            <!-- Resources Grid -->
            <div class="row">
                {% for resource in resources %}
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card resource-card">
                            <div class="position-relative">
                                {% if resource.featured_image %}
                                    <img src="{{ resource.featured_image.url }}" alt="{{ resource.title }}"
                                         class="resource-image">
                                {% else %}
                                    <div class="resource-image bg-light d-flex align-items-center justify-content-center">
                                        {% if resource.resource_type == 'article' %}
                                            <i class="fas fa-newspaper fa-3x text-muted"></i>
                                        {% elif resource.resource_type == 'video' %}
                                            <i class="fas fa-video fa-3x text-muted"></i>
                                        {% elif resource.resource_type == 'audio' %}
                                            <i class="fas fa-music fa-3x text-muted"></i>
                                        {% elif resource.resource_type == 'document' %}
                                            <i class="fas fa-file-pdf fa-3x text-muted"></i>
                                        {% elif resource.resource_type == 'infographic' %}
                                            <i class="fas fa-chart-bar fa-3x text-muted"></i>
                                        {% elif resource.resource_type == 'quiz' %}
                                            <i class="fas fa-question-circle fa-3x text-muted"></i>
                                        {% elif resource.resource_type == 'worksheet' %}
                                            <i class="fas fa-file-alt fa-3x text-muted"></i>
                                        {% endif %}
                                    </div>
                                {% endif %}

                                <span class="resource-type-badge type-{{ resource.resource_type }}">
                                    {{ resource.get_resource_type_display }}
                                </span>

                                {% if resource.is_featured %}
                                    <span class="badge bg-warning position-absolute" style="top: 1rem; left: 1rem;">
                                        <i class="fas fa-star me-1"></i>{% trans "Featured" %}
                                    </span>
                                {% endif %}
                            </div>

                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h5 class="card-title mb-0">
                                        <a href="{{ resource.get_absolute_url }}" class="text-decoration-none">
                                            {{ resource.title|truncatechars:60 }}
                                        </a>
                                    </h5>
                                    <span class="difficulty-badge difficulty-{{ resource.difficulty_level }}">
                                        {{ resource.get_difficulty_level_display }}
                                    </span>
                                </div>

                                <p class="card-text text-muted mb-3">
                                    {{ resource.description|truncatechars:120 }}
                                </p>

                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>
                                        {{ resource.author.get_full_name|default:resource.author.username }}
                                    </small>
                                    <small class="text-muted">
                                        <i class="fas fa-folder me-1"></i>
                                        {{ resource.category.name }}
                                    </small>
                                </div>

                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex">
                                        <span class="stats-item me-3">
                                            <i class="fas fa-eye me-1"></i>{{ resource.view_count }}
                                        </span>
                                        <span class="stats-item me-3">
                                            <i class="fas fa-heart me-1"></i>{{ resource.like_count }}
                                        </span>
                                        <span class="stats-item">
                                            <i class="fas fa-download me-1"></i>{{ resource.download_count }}
                                        </span>
                                    </div>

                                    {% if resource.duration_minutes or resource.reading_time_minutes %}
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            {% if resource.duration_minutes %}
                                                {{ resource.duration_minutes }}m
                                            {% else %}
                                                {{ resource.reading_time_minutes }}m read
                                            {% endif %}
                                        </small>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="card-footer bg-transparent">
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        {{ resource.created_at|date:"M d, Y" }}
                                    </small>
                                    <a href="{{ resource.get_absolute_url }}" class="btn btn-primary btn-sm">
                                        {% trans "View Resource" %}
                                        <i class="fas fa-arrow-right ms-1"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
                <div class="row mt-5">
                    <div class="col-12">
                        <nav aria-label="{% trans 'Resources pagination' %}">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_type %}&type={{ selected_type }}{% endif %}{% if selected_difficulty %}&difficulty={{ selected_difficulty }}{% endif %}">
                                            {% trans "First" %}
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_type %}&type={{ selected_type }}{% endif %}{% if selected_difficulty %}&difficulty={{ selected_difficulty }}{% endif %}">
                                            {% trans "Previous" %}
                                        </a>
                                    </li>
                                {% endif %}

                                <li class="page-item active">
                                    <span class="page-link">
                                        {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_type %}&type={{ selected_type }}{% endif %}{% if selected_difficulty %}&difficulty={{ selected_difficulty }}{% endif %}">
                                            {% trans "Next" %}
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}{% if selected_type %}&type={{ selected_type }}{% endif %}{% if selected_difficulty %}&difficulty={{ selected_difficulty }}{% endif %}">
                                            {% trans "Last" %}
                                        </a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                </div>
            {% endif %}

        {% else %}
            <!-- Empty State -->
            <div class="empty-state">
                <i class="fas fa-search fa-4x mb-4"></i>
                <h3>{% trans "No Resources Found" %}</h3>
                <p class="lead text-muted mb-4">
                    {% if search_query or selected_category or selected_type or selected_difficulty %}
                        {% trans "Try adjusting your search criteria or browse all resources." %}
                    {% else %}
                        {% trans "No resources have been uploaded yet." %}
                    {% endif %}
                </p>

                {% if search_query or selected_category or selected_type or selected_difficulty %}
                    <a href="{% url 'resources:resource_list' %}" class="btn btn-primary">
                        <i class="fas fa-list me-2"></i>
                        {% trans "View All Resources" %}
                    </a>
                {% endif %}

                {% if user.is_authenticated and user.psychologist_profile or user.is_staff %}
                    <a href="{% url 'resources:upload_resource' %}" class="btn btn-outline-primary ms-2">
                        <i class="fas fa-plus me-2"></i>
                        {% trans "Upload First Resource" %}
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</section>
{% endblock %}
