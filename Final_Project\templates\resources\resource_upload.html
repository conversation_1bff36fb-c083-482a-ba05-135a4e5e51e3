{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Upload Resource" %} - ECPI{% endblock %}

{% block extra_css %}
<style>
    .upload-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 4rem 0;
    }
    
    .form-section {
        background: #f8f9fa;
        padding: 3rem 0;
    }
    
    .form-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }
    
    .form-control, .form-select {
        border-radius: 8px;
        border: 2px solid #e9ecef;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .file-upload-area {
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        background: #f8f9fa;
    }
    
    .file-upload-area:hover {
        border-color: #667eea;
        background: #f0f2ff;
    }
    
    .resource-type-info {
        background: #e3f2fd;
        border-left: 4px solid #2196f3;
        padding: 1rem;
        margin-top: 1rem;
        border-radius: 0 8px 8px 0;
    }
    
    .btn-upload {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-upload:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }
</style>
{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="upload-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h1 class="display-5 fw-bold mb-3">
                    <i class="fas fa-cloud-upload-alt me-3"></i>
                    {% trans "Upload Resource" %}
                </h1>
                <p class="lead">
                    {% trans "Share your knowledge and help others on their mental health journey" %}
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Form Section -->
<section class="form-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card form-card">
                    <div class="card-body p-5">
                        <form method="post" enctype="multipart/form-data" id="resourceForm">
                            {% csrf_token %}
                            
                            <!-- Basic Information -->
                            <div class="row">
                                <div class="col-12">
                                    <h4 class="mb-4">
                                        <i class="fas fa-info-circle text-primary me-2"></i>
                                        {% trans "Basic Information" %}
                                    </h4>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="form-group">
                                        <label for="{{ form.title.id_for_label }}" class="form-label">
                                            {{ form.title.label }} <span class="text-danger">*</span>
                                        </label>
                                        {{ form.title }}
                                        {% if form.title.errors %}
                                            <div class="text-danger small mt-1">{{ form.title.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="{{ form.category.id_for_label }}" class="form-label">
                                            {{ form.category.label }} <span class="text-danger">*</span>
                                        </label>
                                        {{ form.category }}
                                        {% if form.category.errors %}
                                            <div class="text-danger small mt-1">{{ form.category.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.resource_type.id_for_label }}" class="form-label">
                                            {{ form.resource_type.label }} <span class="text-danger">*</span>
                                        </label>
                                        {{ form.resource_type }}
                                        {% if form.resource_type.errors %}
                                            <div class="text-danger small mt-1">{{ form.resource_type.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.difficulty_level.id_for_label }}" class="form-label">
                                            {{ form.difficulty_level.label }}
                                        </label>
                                        {{ form.difficulty_level }}
                                        {% if form.difficulty_level.errors %}
                                            <div class="text-danger small mt-1">{{ form.difficulty_level.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="{{ form.description.id_for_label }}" class="form-label">
                                    {{ form.description.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.description }}
                                {% if form.description.errors %}
                                    <div class="text-danger small mt-1">{{ form.description.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="form-group">
                                <label for="{{ form.tags.id_for_label }}" class="form-label">
                                    {{ form.tags.label }}
                                </label>
                                {{ form.tags }}
                                <small class="form-text text-muted">{{ form.tags.help_text }}</small>
                                {% if form.tags.errors %}
                                    <div class="text-danger small mt-1">{{ form.tags.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <!-- Content Section -->
                            <div class="row mt-5">
                                <div class="col-12">
                                    <h4 class="mb-4">
                                        <i class="fas fa-edit text-primary me-2"></i>
                                        {% trans "Content" %}
                                    </h4>
                                </div>
                            </div>
                            
                            <div class="form-group" id="contentField">
                                <label for="{{ form.content.id_for_label }}" class="form-label">
                                    {{ form.content.label }}
                                </label>
                                {{ form.content }}
                                <small class="form-text text-muted">{{ form.content.help_text }}</small>
                                {% if form.content.errors %}
                                    <div class="text-danger small mt-1">{{ form.content.errors.0 }}</div>
                                {% endif %}
                            </div>
                            
                            <!-- Media Files -->
                            <div class="row mt-5">
                                <div class="col-12">
                                    <h4 class="mb-4">
                                        <i class="fas fa-file-upload text-primary me-2"></i>
                                        {% trans "Media Files" %}
                                    </h4>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.featured_image.id_for_label }}" class="form-label">
                                            {{ form.featured_image.label }}
                                        </label>
                                        <div class="file-upload-area">
                                            <i class="fas fa-image fa-2x text-muted mb-2"></i>
                                            {{ form.featured_image }}
                                        </div>
                                        {% if form.featured_image.errors %}
                                            <div class="text-danger small mt-1">{{ form.featured_image.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6" id="videoField">
                                    <div class="form-group">
                                        <label for="{{ form.video_file.id_for_label }}" class="form-label">
                                            {{ form.video_file.label }}
                                        </label>
                                        <div class="file-upload-area">
                                            <i class="fas fa-video fa-2x text-muted mb-2"></i>
                                            {{ form.video_file }}
                                        </div>
                                        {% if form.video_file.errors %}
                                            <div class="text-danger small mt-1">{{ form.video_file.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6" id="audioField">
                                    <div class="form-group">
                                        <label for="{{ form.audio_file.id_for_label }}" class="form-label">
                                            {{ form.audio_file.label }}
                                        </label>
                                        <div class="file-upload-area">
                                            <i class="fas fa-music fa-2x text-muted mb-2"></i>
                                            {{ form.audio_file }}
                                        </div>
                                        {% if form.audio_file.errors %}
                                            <div class="text-danger small mt-1">{{ form.audio_file.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6" id="documentField">
                                    <div class="form-group">
                                        <label for="{{ form.document_file.id_for_label }}" class="form-label">
                                            {{ form.document_file.label }}
                                        </label>
                                        <div class="file-upload-area">
                                            <i class="fas fa-file-pdf fa-2x text-muted mb-2"></i>
                                            {{ form.document_file }}
                                        </div>
                                        {% if form.document_file.errors %}
                                            <div class="text-danger small mt-1">{{ form.document_file.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            
                            <!-- External Links -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.external_url.id_for_label }}" class="form-label">
                                            {{ form.external_url.label }}
                                        </label>
                                        {{ form.external_url }}
                                        {% if form.external_url.errors %}
                                            <div class="text-danger small mt-1">{{ form.external_url.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6" id="youtubeField">
                                    <div class="form-group">
                                        <label for="{{ form.youtube_url.id_for_label }}" class="form-label">
                                            {{ form.youtube_url.label }}
                                        </label>
                                        {{ form.youtube_url }}
                                        {% if form.youtube_url.errors %}
                                            <div class="text-danger small mt-1">{{ form.youtube_url.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Metadata -->
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.duration_minutes.id_for_label }}" class="form-label">
                                            {{ form.duration_minutes.label }}
                                        </label>
                                        {{ form.duration_minutes }}
                                        <small class="form-text text-muted">{{ form.duration_minutes.help_text }}</small>
                                        {% if form.duration_minutes.errors %}
                                            <div class="text-danger small mt-1">{{ form.duration_minutes.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.reading_time_minutes.id_for_label }}" class="form-label">
                                            {{ form.reading_time_minutes.label }}
                                        </label>
                                        {{ form.reading_time_minutes }}
                                        <small class="form-text text-muted">{{ form.reading_time_minutes.help_text }}</small>
                                        {% if form.reading_time_minutes.errors %}
                                            <div class="text-danger small mt-1">{{ form.reading_time_minutes.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Publishing Options -->
                            <div class="row mt-5">
                                <div class="col-12">
                                    <h4 class="mb-4">
                                        <i class="fas fa-cog text-primary me-2"></i>
                                        {% trans "Publishing Options" %}
                                    </h4>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        {{ form.is_published }}
                                        <label class="form-check-label" for="{{ form.is_published.id_for_label }}">
                                            {{ form.is_published.label }}
                                        </label>
                                        <small class="form-text text-muted d-block">{{ form.is_published.help_text }}</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        {{ form.is_featured }}
                                        <label class="form-check-label" for="{{ form.is_featured.id_for_label }}">
                                            {{ form.is_featured.label }}
                                        </label>
                                        <small class="form-text text-muted d-block">{{ form.is_featured.help_text }}</small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Form Errors -->
                            {% if form.non_field_errors %}
                                <div class="alert alert-danger mt-4">
                                    {{ form.non_field_errors }}
                                </div>
                            {% endif %}
                            
                            <!-- Submit Buttons -->
                            <div class="row mt-5">
                                <div class="col-12 text-center">
                                    <button type="submit" class="btn btn-primary btn-upload me-3">
                                        <i class="fas fa-cloud-upload-alt me-2"></i>
                                        {% trans "Upload Resource" %}
                                    </button>
                                    <a href="{% url 'resources:resource_list' %}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-2"></i>
                                        {% trans "Cancel" %}
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const resourceTypeField = document.getElementById('{{ form.resource_type.id_for_label }}');
    const contentField = document.getElementById('contentField');
    const videoField = document.getElementById('videoField');
    const audioField = document.getElementById('audioField');
    const documentField = document.getElementById('documentField');
    const youtubeField = document.getElementById('youtubeField');
    
    function toggleFields() {
        const resourceType = resourceTypeField.value;
        
        // Hide all optional fields first
        contentField.style.display = 'none';
        videoField.style.display = 'none';
        audioField.style.display = 'none';
        documentField.style.display = 'none';
        youtubeField.style.display = 'none';
        
        // Show relevant fields based on resource type
        switch(resourceType) {
            case 'article':
                contentField.style.display = 'block';
                break;
            case 'video':
                videoField.style.display = 'block';
                youtubeField.style.display = 'block';
                break;
            case 'audio':
                audioField.style.display = 'block';
                break;
            case 'document':
            case 'worksheet':
                documentField.style.display = 'block';
                break;
            case 'infographic':
                documentField.style.display = 'block';
                break;
        }
    }
    
    // Initial toggle
    toggleFields();
    
    // Toggle on change
    resourceTypeField.addEventListener('change', toggleFields);
});
</script>
{% endblock %}
