/*
* jQuery UI CSS Framework
* Copyright (c) 2010 AUTHORS.txt (http://jqueryui.com/about)
* Dual licensed under the MIT (MIT-LICENSE.txt) and GPL (GPL-LICENSE.txt) licenses.
* http://ajax.googleapis.com/ajax/libs/jqueryui/1.8.2/themes/base/jquery.ui.core.css
*/

/*
backward compatibility:
.ui-tabs-selected:      jquery ui <  1.10
.ui-tabs-active classes jquery ui >= 1.10
*/

/* Layout helpers
----------------------------------*/
.ui-helper-hidden { display: none; }
.ui-helper-hidden-accessible { position: absolute; left: -99999999px; }
.ui-helper-reset { margin: 0; padding: 0; border: 0; outline: 0; line-height: 1.3; text-decoration: none; font-size: 100%; list-style: none; }
.ui-helper-clearfix:after { content: "."; display: block; height: 0; clear: both; visibility: hidden; }
.ui-helper-clearfix { display: inline-block; }
/* required comment for clearfix to work in Opera \*/
* html .ui-helper-clearfix { height:1%; }
.ui-helper-clearfix { display:block; }
.ui-helper-zfix { width: 100%; height: 100%; top: 0; left: 0; position: absolute; opacity: 0; filter:Alpha(Opacity=0); }
.ui-state-disabled { cursor: default !important; }
.ui-icon { display: block; text-indent: -99999px; overflow: hidden; background-repeat: no-repeat; }
/* http://ajax.googleapis.com/ajax/libs/jqueryui/1.8.2/themes/base/jquery.ui.tabs.css */
.ui-widget-overlay { position: absolute; top: 0; left: 0; width: 100%; height: 100%; }
.ui-tabs { position: relative; padding: .2em; zoom: 1; } /* position: relative prevents IE scroll bug (element with position: relative inside container with overflow: auto appear as "fixed") */
.ui-tabs .ui-tabs-nav { margin: 0; padding: .2em .2em 0; }
.ui-tabs .ui-tabs-nav li { list-style: none; float: left; position: relative; top: 1px; margin: 0 .2em 1px 0; border-bottom: 0 !important; padding: 0; white-space: nowrap; }
.ui-tabs .ui-tabs-nav li a { float: left; padding: .5em 1em; text-decoration: none; }
.ui-tabs .ui-tabs-nav li.ui-tabs-active, .ui-tabs .ui-tabs-nav li.ui-tabs-selected { margin-bottom: 0; padding-bottom: 1px; }
.ui-tabs .ui-tabs-nav li.ui-tabs-active a, .ui-tabs .ui-tabs-nav li.ui-tabs-selected a, .ui-tabs .ui-tabs-nav li.ui-state-disabled a, .ui-tabs .ui-tabs-nav li.ui-state-processing a { cursor: text; }
.ui-tabs .ui-tabs-nav li a, .ui-tabs.ui-tabs-collapsible .ui-tabs-nav li.ui-tabs-selected a { cursor: pointer; } /* first selector in group seems obsolete, but required to overcome bug in Opera applying cursor: text overall if defined elsewhere... */
.ui-tabs .ui-tabs-panel { display: block; border-width: 0; padding: 1em 1.4em; background: none; }
.ui-tabs .ui-tabs-hide {
    position: absolute;
    display: none;
}

/* custom tabs theme */
.ui-tabs { padding: 0; }
.ui-tabs .ui-tabs-nav { padding: 5px 0 0 10px; border-bottom: 1px solid #EEEEEE; }
.ui-tabs .ui-tabs-nav li { margin: 0; }
.ui-tabs .ui-tabs-nav li.required { font-weight: bold; }
.ui-tabs .ui-tabs-nav li a {
    border: 1px solid #CCCCCC;
    background: #eeeeee repeat-x;
    border-bottom-width: 0;
    color: #666666;
    padding: 4px 10px 4px 10px;
    margin-top: 2px;
    -moz-border-radius-topright: 4px;
	-webkit-border-top-right-radius: 4px;
	-moz-border-radius-topleft: 4px;
	-webkit-border-top-left-radius: 4px;
	border-top-left-radius: 4px;
	border-top-right-radius: 4px;
    font-size: 12px;
}

.ui-tabs .ui-tabs-nav li.ui-tabs-active a, .ui-tabs .ui-tabs-nav li.ui-tabs-selected a {
    background: #7CA0C7 repeat-x;
    color: #fff;
    padding: 6px 10px 4px 10px;
    margin-top: 0;
}

.ui-tabs .ui-tabs-panel {
    padding: 0;
}

.ui-tabs .ui-tab-has-errors a::after {
    content: "•";
    color: #ba2121;
    position: absolute;
    font-size: 1.8rem;
}

.inline-group .tabular .ui-tabs .ui-tabs-panel {
    padding: 5px;
}
.inline-group .tabular .ui-tabs .ui-tabs-nav {
    padding-left: 4px;
    font-family: "Lucida Grande", "DejaVu Sans", "Bitstream Vera Sans", Verdana,Arial, sans-serif;
}
.inline-group .tabular tr td {
    vertical-align: bottom;
}
.inline-group .tabular tr.has_original td.original,
.inline-group .tabular tr td.delete {
    vertical-align: top;
}

.inline-group .tabular .datetime > input {
    margin-right: 5px;
}
.inline-group .tabular .datetime br {
    display: none;
}
